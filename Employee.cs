using System;

namespace Ahmedapp_for_work
{
    public class Employee
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NationalId { get; set; } = string.Empty;
        public string InsuranceNumber { get; set; } = string.Empty;
        public string JobTitle { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public DateTime HireDate { get; set; }
        public string EmployeeType { get; set; } = "موظف عادي"; // موظف عادي، منتدب، إصابة عمل، إجازة بدون مرتب
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; } = string.Empty;

        // Delegation specific fields
        public string? DecisionNumber { get; set; }
        public DateTime? DecisionDate { get; set; }
        public string? DelegationType { get; set; } // نوع الانتداب
        public string? FromToEntity { get; set; }
        public string? WorkDepartment { get; set; }
        public string? Management { get; set; }
        public DateTime? LeaveEndDate { get; set; }
        public string? AppointmentType { get; set; }
        public string? RequiredDocuments { get; set; }

        // Injury specific fields
        public string? InjuryType { get; set; }
        public DateTime? InjuryDate { get; set; }
        public string? InjuryLocation { get; set; }
        public string? InjuryDescription { get; set; }

        // الاشتراكات التأمينية
        public decimal? TotalInsuranceContributions { get; set; }
        public decimal? Rate12Percent { get; set; }
        public decimal? Rate9Percent { get; set; }
        public decimal? Rate3Percent { get; set; }
        public decimal? Rate1Percent_1 { get; set; }
        public decimal? Rate1Percent_2 { get; set; }
        public decimal? Rate1Percent_3 { get; set; }
        public decimal? Rate1Percent_4 { get; set; }
        public decimal? Rate025Percent { get; set; }

        // الحقول الإضافية
        public decimal? CashReplacement { get; set; }
        public int? ConsiderationPeriod { get; set; } // بالأشهر
        public decimal? LoanInstallment { get; set; }
        public string? OtherDetails { get; set; }

        // Unpaid leave specific fields
        public string? UnpaidLeaveType { get; set; }
        public string? LeaveStatus { get; set; }
        public DateTime? LeaveStartDate { get; set; }
        public DateTime? UnpaidLeaveEndDate { get; set; }

        // Additional fields
        public string? Notes { get; set; }
        public string? ContactInfo { get; set; }
        public string? EmergencyContact { get; set; }
    }

    public class EmployeeFile
    {
        public int Id { get; set; }
        public int EmployeeId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string FileType { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public DateTime UploadDate { get; set; } = DateTime.Now;
        public string UploadedBy { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty; // مستندات، صور، تقارير، etc.
        public string Description { get; set; } = string.Empty;
    }

    public class EmployeeHistory
    {
        public int Id { get; set; }
        public int EmployeeId { get; set; }
        public string Action { get; set; } = string.Empty; // إضافة، تعديل، حذف، تغيير حالة
        public string Details { get; set; } = string.Empty;
        public DateTime ActionDate { get; set; } = DateTime.Now;
        public string ActionBy { get; set; } = string.Empty;
        public string OldValue { get; set; } = string.Empty;
        public string NewValue { get; set; } = string.Empty;
    }
}
