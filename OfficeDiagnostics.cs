using System;
using System.Collections.Generic;
using System.Data.OleDb;
using System.IO;
using Microsoft.Win32;

namespace Ahmedapp_for_work
{
    public class OfficeDiagnostics
    {
        // معلومات إصدار Office
        public class OfficeVersionInfo
        {
            public string Version { get; set; } = "";
            public string Provider { get; set; } = "";
            public string DisplayName { get; set; } = "";
            public bool IsInstalled { get; set; }
            public bool IsWorking { get; set; }
            public string InstallPath { get; set; } = "";
            public string ErrorMessage { get; set; } = "";
        }

        // فحص جميع إصدارات Office المتاحة
        public static List<OfficeVersionInfo> DiagnoseAllOfficeVersions()
        {
            var versions = new List<OfficeVersionInfo>();

            // قائمة إصدارات Office المدعومة
            var officeVersions = new[]
            {
                new { Version = "16.0", Provider = "Microsoft.ACE.OLEDB.16.0", DisplayName = "Office 2016/2019/365", RegistryKey = @"SOFTWARE\Microsoft\Office\16.0" },
                new { Version = "15.0", Provider = "Microsoft.ACE.OLEDB.15.0", DisplayName = "Office 2013", RegistryKey = @"SOFTWARE\Microsoft\Office\15.0" },
                new { Version = "14.0", Provider = "Microsoft.ACE.OLEDB.14.0", DisplayName = "Office 2010", RegistryKey = @"SOFTWARE\Microsoft\Office\14.0" },
                new { Version = "12.0", Provider = "Microsoft.ACE.OLEDB.12.0", DisplayName = "Office 2007", RegistryKey = @"SOFTWARE\Microsoft\Office\12.0" },
                new { Version = "4.0", Provider = "Microsoft.Jet.OLEDB.4.0", DisplayName = "Jet Engine (Access 97-2003)", RegistryKey = @"SOFTWARE\Microsoft\Jet" }
            };

            foreach (var office in officeVersions)
            {
                var versionInfo = new OfficeVersionInfo
                {
                    Version = office.Version,
                    Provider = office.Provider,
                    DisplayName = office.DisplayName
                };

                // فحص التثبيت من Registry
                CheckRegistryInstallation(versionInfo, office.RegistryKey);

                // فحص عمل Provider
                CheckProviderFunctionality(versionInfo);

                versions.Add(versionInfo);
            }

            return versions;
        }

        // فحص التثبيت من Registry
        private static void CheckRegistryInstallation(OfficeVersionInfo versionInfo, string registryPath)
        {
            try
            {
                // فحص في HKEY_LOCAL_MACHINE
                using (var key = Registry.LocalMachine.OpenSubKey(registryPath))
                {
                    if (key != null)
                    {
                        versionInfo.IsInstalled = true;
                        
                        // محاولة الحصول على مسار التثبيت
                        try
                        {
                            var installRoot = key.GetValue("InstallRoot") as string;
                            if (!string.IsNullOrEmpty(installRoot))
                            {
                                versionInfo.InstallPath = installRoot;
                            }
                        }
                        catch { }

                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {versionInfo.DisplayName} في Registry");
                        return;
                    }
                }

                // فحص في HKEY_CURRENT_USER
                using (var key = Registry.CurrentUser.OpenSubKey(registryPath))
                {
                    if (key != null)
                    {
                        versionInfo.IsInstalled = true;
                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {versionInfo.DisplayName} في Registry (Current User)");
                        return;
                    }
                }

                // فحص في مسارات أخرى للـ ACE Engine
                if (versionInfo.Provider.Contains("ACE"))
                {
                    CheckACEEngineInstallation(versionInfo);
                }

                System.Diagnostics.Debug.WriteLine($"❌ لم يتم العثور على {versionInfo.DisplayName} في Registry");
            }
            catch (Exception ex)
            {
                versionInfo.ErrorMessage = $"خطأ في فحص Registry: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص {versionInfo.DisplayName}: {ex.Message}");
            }
        }

        // فحص تثبيت ACE Engine
        private static void CheckACEEngineInstallation(OfficeVersionInfo versionInfo)
        {
            try
            {
                // فحص مسارات ACE Engine المعروفة
                var acePaths = new[]
                {
                    @"SOFTWARE\Microsoft\Office\ClickToRun\REGISTRY\MACHINE\Software\Microsoft\Office",
                    @"SOFTWARE\Classes\TypeLib\{4AC9E1DA-5BAD-4AC7-86E3-24F4CDCECA28}",
                    @"SOFTWARE\Microsoft\Office\Common\FilesPaths"
                };

                foreach (var path in acePaths)
                {
                    try
                    {
                        using (var key = Registry.LocalMachine.OpenSubKey(path))
                        {
                            if (key != null)
                            {
                                versionInfo.IsInstalled = true;
                                System.Diagnostics.Debug.WriteLine($"✅ تم العثور على ACE Engine في: {path}");
                                return;
                            }
                        }
                    }
                    catch { }
                }

                // فحص ملفات ACE Engine في النظام
                var aceFiles = new[]
                {
                    @"C:\Program Files\Microsoft Office\root\VFS\ProgramFilesCommonX86\Microsoft Shared\OFFICE16\ACEOLEDB.DLL",
                    @"C:\Program Files (x86)\Microsoft Office\root\VFS\ProgramFilesCommonX86\Microsoft Shared\OFFICE16\ACEOLEDB.DLL",
                    @"C:\Program Files\Common Files\Microsoft Shared\OFFICE16\ACEOLEDB.DLL",
                    @"C:\Program Files (x86)\Common Files\Microsoft Shared\OFFICE16\ACEOLEDB.DLL",
                    @"C:\Program Files\Common Files\Microsoft Shared\OFFICE15\ACEOLEDB.DLL",
                    @"C:\Program Files (x86)\Common Files\Microsoft Shared\OFFICE15\ACEOLEDB.DLL",
                    @"C:\Program Files\Common Files\Microsoft Shared\OFFICE14\ACEOLEDB.DLL",
                    @"C:\Program Files (x86)\Common Files\Microsoft Shared\OFFICE14\ACEOLEDB.DLL",
                    @"C:\Program Files\Common Files\Microsoft Shared\OFFICE12\ACEOLEDB.DLL",
                    @"C:\Program Files (x86)\Common Files\Microsoft Shared\OFFICE12\ACEOLEDB.DLL"
                };

                foreach (var filePath in aceFiles)
                {
                    if (File.Exists(filePath))
                    {
                        versionInfo.IsInstalled = true;
                        versionInfo.InstallPath = Path.GetDirectoryName(filePath) ?? "";
                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على ACE Engine في: {filePath}");
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص ACE Engine: {ex.Message}");
            }
        }

        // فحص عمل Provider
        private static void CheckProviderFunctionality(OfficeVersionInfo versionInfo)
        {
            try
            {
                // إنشاء connection string للاختبار
                string testConnectionString = $"Provider={versionInfo.Provider};Data Source=test_temp.accdb;";

                using (var connection = new OleDbConnection(testConnectionString))
                {
                    // لا نحتاج فتح الاتصال، فقط إنشاء الكائن
                    versionInfo.IsWorking = true;
                    System.Diagnostics.Debug.WriteLine($"✅ Provider {versionInfo.Provider} يعمل بشكل صحيح");
                }
            }
            catch (Exception ex)
            {
                versionInfo.IsWorking = false;
                versionInfo.ErrorMessage = ex.Message;
                System.Diagnostics.Debug.WriteLine($"❌ Provider {versionInfo.Provider} لا يعمل: {ex.Message}");
            }
        }

        // الحصول على أفضل provider متاح
        public static string GetBestAvailableProvider()
        {
            var versions = DiagnoseAllOfficeVersions();

            // البحث عن أفضل provider يعمل
            foreach (var version in versions)
            {
                if (version.IsWorking)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم اختيار أفضل provider: {version.Provider} ({version.DisplayName})");
                    return version.Provider;
                }
            }

            // إذا لم يعمل أي provider، ارجع الافتراضي
            System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على provider يعمل، سيتم استخدام الافتراضي");
            return "Microsoft.ACE.OLEDB.12.0";
        }

        // إنشاء تقرير تشخيص شامل
        public static string GenerateDetailedDiagnosticReport()
        {
            var report = new System.Text.StringBuilder();
            
            report.AppendLine("🔍 تقرير تشخيص شامل لإصدارات Microsoft Office");
            report.AppendLine("=" + new string('=', 60));
            report.AppendLine($"📅 تاريخ التشخيص: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"💻 نظام التشغيل: {Environment.OSVersion}");
            report.AppendLine($"🏗️ معمارية النظام: {(Environment.Is64BitOperatingSystem ? "64-bit" : "32-bit")}");
            report.AppendLine($"🔧 معمارية التطبيق: {(Environment.Is64BitProcess ? "64-bit" : "32-bit")}");
            report.AppendLine();

            var versions = DiagnoseAllOfficeVersions();

            report.AppendLine("📊 نتائج فحص إصدارات Office:");
            report.AppendLine("-" + new string('-', 40));

            foreach (var version in versions)
            {
                report.AppendLine($"\n🏢 {version.DisplayName}:");
                report.AppendLine($"   📋 Provider: {version.Provider}");
                report.AppendLine($"   📦 مثبت: {(version.IsInstalled ? "✅ نعم" : "❌ لا")}");
                report.AppendLine($"   ⚙️ يعمل: {(version.IsWorking ? "✅ نعم" : "❌ لا")}");
                
                if (!string.IsNullOrEmpty(version.InstallPath))
                {
                    report.AppendLine($"   📁 مسار التثبيت: {version.InstallPath}");
                }
                
                if (!string.IsNullOrEmpty(version.ErrorMessage))
                {
                    report.AppendLine($"   ❌ رسالة الخطأ: {version.ErrorMessage}");
                }
            }

            // إحصائيات
            int installedCount = 0;
            int workingCount = 0;
            string bestProvider = "";

            foreach (var version in versions)
            {
                if (version.IsInstalled) installedCount++;
                if (version.IsWorking)
                {
                    workingCount++;
                    if (string.IsNullOrEmpty(bestProvider))
                    {
                        bestProvider = version.Provider;
                    }
                }
            }

            report.AppendLine($"\n📈 ملخص الإحصائيات:");
            report.AppendLine($"   📦 إصدارات مثبتة: {installedCount} من {versions.Count}");
            report.AppendLine($"   ⚙️ إصدارات تعمل: {workingCount} من {versions.Count}");
            report.AppendLine($"   🎯 أفضل provider: {bestProvider}");

            // توصيات
            report.AppendLine($"\n💡 التوصيات:");
            
            if (workingCount == 0)
            {
                report.AppendLine($"   ⚠️ لا توجد إصدارات Office تعمل!");
                report.AppendLine($"   📝 قم بتثبيت Microsoft Access Database Engine");
                report.AppendLine($"   🔗 رابط التحميل: https://www.microsoft.com/en-us/download/details.aspx?id=54920");
                report.AppendLine($"   ⚠️ تأكد من تحميل الإصدار المناسب (32-bit أو 64-bit)");
            }
            else if (workingCount < installedCount)
            {
                report.AppendLine($"   ⚠️ بعض الإصدارات المثبتة لا تعمل بشكل صحيح");
                report.AppendLine($"   🔧 قد تحتاج إلى إعادة تثبيت أو إصلاح Office");
            }
            else
            {
                report.AppendLine($"   ✅ جميع الإصدارات المثبتة تعمل بشكل صحيح");
                report.AppendLine($"   🎯 سيتم استخدام: {bestProvider}");
            }

            report.AppendLine($"\n🔧 نصائح إضافية:");
            report.AppendLine($"   📝 استخدم نفس معمارية Office والتطبيق (32-bit أو 64-bit)");
            report.AppendLine($"   📝 في حالة وجود مشاكل، جرب تشغيل التطبيق كمدير");
            report.AppendLine($"   📝 تأكد من تحديث Windows وOffice لآخر إصدار");

            return report.ToString();
        }

        // فحص سريع للتوافق
        public static bool IsOfficeCompatible()
        {
            try
            {
                var versions = DiagnoseAllOfficeVersions();
                return versions.Exists(v => v.IsWorking);
            }
            catch
            {
                return false;
            }
        }

        // الحصول على Connection String الأمثل
        public static string GetOptimalConnectionString(string databasePath)
        {
            string bestProvider = GetBestAvailableProvider();
            return $"Provider={bestProvider};Data Source={databasePath};Persist Security Info=False;";
        }

        // اختبار Connection String محدد
        public static bool TestConnectionString(string connectionString)
        {
            try
            {
                using (var connection = new OleDbConnection(connectionString))
                {
                    connection.Open();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        // إنشاء قائمة بجميع Connection Strings المحتملة
        public static List<string> GetAllPossibleConnectionStrings(string databasePath)
        {
            var connectionStrings = new List<string>();
            var versions = DiagnoseAllOfficeVersions();

            foreach (var version in versions)
            {
                if (version.IsInstalled || version.IsWorking)
                {
                    // إضافة عدة تنويعات للـ connection string
                    connectionStrings.Add($"Provider={version.Provider};Data Source={databasePath};Persist Security Info=False;");
                    connectionStrings.Add($"Provider={version.Provider};Data Source={databasePath};");
                    
                    if (version.Provider.Contains("ACE"))
                    {
                        connectionStrings.Add($"Provider={version.Provider};Data Source={databasePath};Extended Properties='';");
                    }
                }
            }

            return connectionStrings;
        }
    }
}
