using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using System.Drawing;

namespace Ahmedapp_for_work
{
    public static class EnhancedFileManager
    {
        private static readonly string[] SupportedFileTypes = {
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".jpg", ".jpeg", ".png", ".txt", ".zip", ".rar"
        };

        private static readonly string UploadDirectory = Path.Combine(Application.StartupPath, "UploadedFiles");

        static EnhancedFileManager()
        {
            // إنشاء مجلد الملفات إذا لم يكن موجوداً
            if (!Directory.Exists(UploadDirectory))
            {
                Directory.CreateDirectory(UploadDirectory);
            }
        }

        public static void ShowFileManagerDialog(int employeeId = 0, string employeeName = "")
        {
            Form fileManagerForm = new Form
            {
                Text = $"إدارة الملفات - File Manager {(string.IsNullOrEmpty(employeeName) ? "" : $"- {employeeName}")}",
                Size = new Size(1200, 800),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.Sizable,
                MinimumSize = new Size(800, 600),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            // العنوان الرئيسي
            Label titleLabel = new Label
            {
                Text = "إدارة الملفات - File Management System",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(20, 20),
                Size = new Size(1150, 35),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // قسم رفع الملفات
            Panel uploadPanel = CreateUploadPanel(employeeId);
            uploadPanel.Location = new Point(20, 70);

            // قسم عرض الملفات
            Panel filesPanel = CreateFilesPanel(employeeId);
            filesPanel.Location = new Point(20, 250);

            // معلومات الأنواع المدعومة
            Label supportedTypesLabel = new Label
            {
                Text = "الأنواع المدعومة: PDF, Word, Excel, الصور (JPG, PNG), النصوص, المضغوطة (ZIP, RAR)",
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(20, 720),
                Size = new Size(1150, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // زر الإغلاق
            Button closeButton = new Button
            {
                Text = "إغلاق",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(100, 40),
                Location = new Point(550, 750),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            closeButton.Click += (s, e) => fileManagerForm.Close();

            fileManagerForm.Controls.AddRange(new Control[] {
                titleLabel, uploadPanel, filesPanel, supportedTypesLabel, closeButton
            });

            fileManagerForm.ShowDialog();
        }

        private static Panel CreateUploadPanel(int employeeId)
        {
            Panel uploadPanel = new Panel
            {
                Size = new Size(1150, 160),
                BackColor = Color.FromArgb(236, 240, 241),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label uploadTitle = new Label
            {
                Text = "رفع الملفات الجديدة - Upload New Files",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(10, 10),
                Size = new Size(400, 25)
            };

            Button selectFilesBtn = new Button
            {
                Text = "اختيار الملفات\nSelect Files",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(150, 60),
                Location = new Point(20, 50),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label selectedFilesLabel = new Label
            {
                Text = "لم يتم اختيار ملفات",
                Font = new Font("Tahoma", 10),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(190, 50),
                Size = new Size(400, 25)
            };

            Button uploadBtn = new Button
            {
                Text = "رفع الملفات\nUpload Files",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(150, 60),
                Location = new Point(190, 80),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                TextAlign = ContentAlignment.MiddleCenter,
                Enabled = false
            };

            ComboBox categoryCombo = new ComboBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(600, 50),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            categoryCombo.Items.AddRange(new string[] {
                "مستندات شخصية", "شهادات", "تقارير طبية", "قرارات إدارية",
                "ملفات إصابة العمل", "مستندات الإجازات", "أخرى"
            });
            categoryCombo.SelectedIndex = 0;

            Label categoryLabel = new Label
            {
                Text = "فئة الملف:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(520, 50),
                Size = new Size(70, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            TextBox descriptionTextBox = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(600, 90),
                Size = new Size(200, 50),
                Multiline = true,
                RightToLeft = RightToLeft.Yes,
                PlaceholderText = "وصف الملف (اختياري)"
            };

            Label descriptionLabel = new Label
            {
                Text = "الوصف:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(540, 90),
                Size = new Size(50, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            string[] selectedFiles = null;

            selectFilesBtn.Click += (s, e) =>
            {
                using (OpenFileDialog openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Multiselect = true;
                    openFileDialog.Filter = "جميع الملفات المدعومة|*.pdf;*.doc;*.docx;*.xls;*.xlsx;*.jpg;*.jpeg;*.png;*.txt;*.zip;*.rar|" +
                                           "ملفات PDF|*.pdf|" +
                                           "ملفات Word|*.doc;*.docx|" +
                                           "ملفات Excel|*.xls;*.xlsx|" +
                                           "الصور|*.jpg;*.jpeg;*.png|" +
                                           "ملفات نصية|*.txt|" +
                                           "ملفات مضغوطة|*.zip;*.rar";

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        selectedFiles = openFileDialog.FileNames;
                        selectedFilesLabel.Text = $"تم اختيار {selectedFiles.Length} ملف";
                        uploadBtn.Enabled = true;
                    }
                }
            };

            uploadBtn.Click += (s, e) =>
            {
                if (selectedFiles != null && selectedFiles.Length > 0)
                {
                    UploadFiles(selectedFiles, employeeId, categoryCombo.Text, descriptionTextBox.Text);
                    selectedFiles = null;
                    selectedFilesLabel.Text = "لم يتم اختيار ملفات";
                    uploadBtn.Enabled = false;
                    descriptionTextBox.Clear();

                    // تحديث قائمة الملفات
                    RefreshFilesList(uploadPanel.Parent.Controls.OfType<Panel>().FirstOrDefault(p => p != uploadPanel), employeeId);
                }
            };

            uploadPanel.Controls.AddRange(new Control[] {
                uploadTitle, selectFilesBtn, selectedFilesLabel, uploadBtn,
                categoryLabel, categoryCombo, descriptionLabel, descriptionTextBox
            });

            return uploadPanel;
        }

        private static Panel CreateFilesPanel(int employeeId)
        {
            Panel filesPanel = new Panel
            {
                Size = new Size(1150, 450),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true
            };

            Label filesTitle = new Label
            {
                Text = "الملفات المرفوعة - Uploaded Files",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(10, 10),
                Size = new Size(400, 25)
            };

            Button refreshBtn = new Button
            {
                Text = "تحديث القائمة",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(120, 30),
                Location = new Point(1010, 10),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            ListView filesList = new ListView
            {
                Location = new Point(10, 50),
                Size = new Size(1120, 380),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Font = new Font("Tahoma", 10),
                RightToLeft = RightToLeft.Yes
            };

            filesList.Columns.Add("اسم الملف", 300);
            filesList.Columns.Add("النوع", 80);
            filesList.Columns.Add("الحجم", 100);
            filesList.Columns.Add("الفئة", 150);
            filesList.Columns.Add("تاريخ الرفع", 120);
            filesList.Columns.Add("رفع بواسطة", 120);
            filesList.Columns.Add("الوصف", 200);

            refreshBtn.Click += (s, e) => RefreshFilesList(filesPanel, employeeId);

            filesPanel.Controls.AddRange(new Control[] { filesTitle, refreshBtn, filesList });

            // تحميل الملفات عند الإنشاء
            RefreshFilesList(filesPanel, employeeId);

            return filesPanel;
        }

        private static void UploadFiles(string[] filePaths, int employeeId, string category, string description)
        {
            try
            {
                int successCount = 0;
                int failCount = 0;
                var employeeManager = new EmployeeManager();

                foreach (string filePath in filePaths)
                {
                    try
                    {
                        FileInfo fileInfo = new FileInfo(filePath);

                        // التحقق من نوع الملف
                        if (!SupportedFileTypes.Contains(fileInfo.Extension.ToLower()))
                        {
                            failCount++;
                            continue;
                        }

                        // إنشاء اسم ملف فريد
                        string fileName = fileInfo.Name;
                        string destinationPath = Path.Combine(UploadDirectory, fileName);

                        // إضافة timestamp إذا كان الملف موجود
                        if (File.Exists(destinationPath))
                        {
                            string nameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
                            string extension = Path.GetExtension(fileName);
                            fileName = $"{nameWithoutExt}_{DateTime.Now:yyyyMMdd_HHmmss}{extension}";
                            destinationPath = Path.Combine(UploadDirectory, fileName);
                        }

                        // نسخ الملف
                        File.Copy(filePath, destinationPath);

                        // حفظ في قاعدة البيانات أو كملف مؤقت
                        if (employeeId > 0)
                        {
                            bool saved = employeeManager.SaveEmployeeFile(
                                employeeId, fileName, destinationPath,
                                fileInfo.Extension, fileInfo.Length,
                                "أحمد ابراهيم", category, description
                            );

                            if (!saved)
                            {
                                // إذا فشل الحفظ في قاعدة البيانات، احذف الملف المنسوخ
                                if (File.Exists(destinationPath))
                                {
                                    File.Delete(destinationPath);
                                }
                                failCount++;
                                continue;
                            }
                        }
                        else
                        {
                            // حفظ كملف مؤقت للربط لاحقاً بالموظف الجديد
                            Form1.AddTemporaryFile(fileName, destinationPath, fileInfo.Extension, fileInfo.Length, category, description);

                            // حفظ في ملف JSON محلي للملفات العامة أيضاً
                            SaveFileInfoToJson(fileName, destinationPath, fileInfo.Extension, fileInfo.Length, category, description);
                        }

                        successCount++;
                    }
                    catch
                    {
                        failCount++;
                    }
                }

                string message = $"تم رفع {successCount} ملف بنجاح";
                if (failCount > 0)
                    message += $"\nفشل في رفع {failCount} ملف";

                MessageBox.Show(message, "نتيجة الرفع", MessageBoxButtons.OK,
                    successCount > 0 ? MessageBoxIcon.Information : MessageBoxIcon.Warning);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في رفع الملفات:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void RefreshFilesList(Panel filesPanel, int employeeId)
        {
            try
            {
                var filesList = filesPanel.Controls.OfType<ListView>().FirstOrDefault();
                if (filesList == null) return;

                filesList.Items.Clear();

                if (employeeId > 0)
                {
                    var employeeManager = new EmployeeManager();
                    var files = employeeManager.GetEmployeeFiles(employeeId);

                    foreach (var file in files)
                    {
                        var item = new ListViewItem(file.FileName);
                        item.SubItems.Add(file.FileType);
                        item.SubItems.Add(FormatFileSize(file.FileSize));
                        item.SubItems.Add(file.Category);
                        item.SubItems.Add(file.UploadDate.ToString("yyyy-MM-dd HH:mm"));
                        item.SubItems.Add(file.UploadedBy);
                        item.SubItems.Add(file.Description);
                        item.Tag = file;

                        filesList.Items.Add(item);
                    }
                }
                else
                {
                    // عرض جميع الملفات في المجلد
                    if (Directory.Exists(UploadDirectory))
                    {
                        var files = Directory.GetFiles(UploadDirectory);
                        foreach (string filePath in files)
                        {
                            FileInfo fileInfo = new FileInfo(filePath);
                            var item = new ListViewItem(fileInfo.Name);
                            item.SubItems.Add(fileInfo.Extension);
                            item.SubItems.Add(FormatFileSize(fileInfo.Length));
                            item.SubItems.Add("عام");
                            item.SubItems.Add(fileInfo.CreationTime.ToString("yyyy-MM-dd HH:mm"));
                            item.SubItems.Add("النظام");
                            item.SubItems.Add("");

                            filesList.Items.Add(item);
                        }
                    }
                }

                // إضافة قائمة سياق للملفات
                ContextMenuStrip contextMenu = new ContextMenuStrip();
                contextMenu.Items.Add("فتح الملف", null, (s, e) => OpenSelectedFile(filesList));
                contextMenu.Items.Add("حذف الملف", null, (s, e) => DeleteSelectedFile(filesList, employeeId));
                filesList.ContextMenuStrip = contextMenu;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث قائمة الملفات:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void OpenSelectedFile(ListView filesList)
        {
            if (filesList.SelectedItems.Count > 0)
            {
                try
                {
                    string fileName = filesList.SelectedItems[0].Text;
                    string filePath = Path.Combine(UploadDirectory, fileName);

                    if (File.Exists(filePath))
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = filePath,
                            UseShellExecute = true
                        });
                    }
                    else
                    {
                        MessageBox.Show("الملف غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح الملف:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private static void DeleteSelectedFile(ListView filesList, int employeeId)
        {
            if (filesList.SelectedItems.Count > 0)
            {
                var result = MessageBox.Show("هل أنت متأكد من حذف هذا الملف؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        string fileName = filesList.SelectedItems[0].Text;
                        string filePath = Path.Combine(UploadDirectory, fileName);

                        // حذف من قاعدة البيانات
                        if (filesList.SelectedItems[0].Tag is EmployeeFile file)
                        {
                            var employeeManager = new EmployeeManager();
                            employeeManager.DeleteEmployeeFile(file.Id);
                        }

                        // حذف الملف الفعلي
                        if (File.Exists(filePath))
                        {
                            File.Delete(filePath);
                        }

                        // تحديث القائمة
                        RefreshFilesList((Panel)filesList.Parent, employeeId);

                        MessageBox.Show("تم حذف الملف بنجاح", "تم الحذف", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الملف:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        // حفظ معلومات الملفات في JSON للملفات العامة
        private static void SaveFileInfoToJson(string fileName, string filePath, string fileType, long fileSize, string category, string description)
        {
            try
            {
                string jsonFilePath = Path.Combine(UploadDirectory, "files_info.json");
                List<dynamic> filesInfo = new List<dynamic>();

                // قراءة الملفات الموجودة
                if (File.Exists(jsonFilePath))
                {
                    string existingJson = File.ReadAllText(jsonFilePath);
                    if (!string.IsNullOrEmpty(existingJson))
                    {
                        try
                        {
                            var existingFiles = Newtonsoft.Json.JsonConvert.DeserializeObject<List<dynamic>>(existingJson);
                            if (existingFiles != null)
                                filesInfo = existingFiles;
                        }
                        catch
                        {
                            // في حالة فشل قراءة JSON، ابدأ بقائمة جديدة
                        }
                    }
                }

                // إضافة الملف الجديد
                var newFileInfo = new
                {
                    Id = DateTime.Now.Ticks,
                    FileName = fileName,
                    FilePath = filePath,
                    FileType = fileType,
                    FileSize = fileSize,
                    UploadDate = DateTime.Now,
                    UploadedBy = "أحمد ابراهيم",
                    Category = category,
                    Description = description
                };

                filesInfo.Add(newFileInfo);

                // حفظ في JSON
                string json = Newtonsoft.Json.JsonConvert.SerializeObject(filesInfo, Newtonsoft.Json.Formatting.Indented);
                File.WriteAllText(jsonFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ معلومات الملف في JSON: {ex.Message}");
            }
        }

        // قراءة معلومات الملفات من JSON
        private static List<dynamic> LoadFilesInfoFromJson()
        {
            try
            {
                string jsonFilePath = Path.Combine(UploadDirectory, "files_info.json");
                if (File.Exists(jsonFilePath))
                {
                    string json = File.ReadAllText(jsonFilePath);
                    if (!string.IsNullOrEmpty(json))
                    {
                        return Newtonsoft.Json.JsonConvert.DeserializeObject<List<dynamic>>(json) ?? new List<dynamic>();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في قراءة معلومات الملفات من JSON: {ex.Message}");
            }
            return new List<dynamic>();
        }
    }
}
