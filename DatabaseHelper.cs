using System;
using System.Data.SQLite;
using System.Security.Cryptography;
using System.Text;
using System.Data;
using System.IO;

namespace Ahmedapp_for_work
{
    public static class DatabaseHelper
    {
        private static string connectionString = "Data Source=hr_management.db;Version=3;";
        private static AccessDatabaseManager? accessManager;

        public static void CreateDatabaseAndTable()
        {
            try
            {
                // تهيئة النسخة المحمولة
                PortableManager.InitializePortableMode();

                // تهيئة نظام التشفير
                EncryptionManager.InitializeEncryption();

                // تحميل إعدادات البريد الإلكتروني
                NotificationManager.LoadEmailSettings();

                using (var connection = new SQLiteConnection(connectionString))
                {
                    connection.Open();

                    string createUsersTable = @"
                        CREATE TABLE IF NOT EXISTS Users (
                            Id INTEGER PRIMARY KEY AUTOINCREMENT,
                            Username TEXT NOT NULL UNIQUE,
                            PasswordHash TEXT NOT NULL,
                            PasswordSalt TEXT NOT NULL,
                            Role TEXT NOT NULL,
                            IsActive INTEGER NOT NULL DEFAULT 1
                        );";

                    using (var cmd = new SQLiteCommand(createUsersTable, connection))
                    {
                        cmd.ExecuteNonQuery();
                    }

                    // إنشاء المستخدمين الافتراضيين إذا لم يكونوا موجودين
                    CreateDefaultUsers();
                }

                // تهيئة قاعدة بيانات Access
                SyncDatabases();

                // بدء النسخ الاحتياطي التلقائي
                BackupManager.StartAutoBackup();

                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة جميع أنظمة قاعدة البيانات والمزايا المتقدمة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة النظام: {ex.Message}");
                NotificationManager.SendErrorNotification("خطأ في تهيئة النظام", ex.Message, ex);
            }
        }

        // إنشاء المستخدمين الافتراضيين
        private static void CreateDefaultUsers()
        {
            try
            {
                // التحقق من وجود مستخدم admin
                if (!UserExists("admin"))
                {
                    AddUser("admin", "123456", "Admin");
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء المستخدم الافتراضي: admin");
                }

                // إضافة مستخدمين إضافيين
                if (!UserExists("مدير"))
                {
                    AddUser("مدير", "123456", "Admin");
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء المستخدم الافتراضي: مدير");
                }

                if (!UserExists("hr"))
                {
                    AddUser("hr", "hr123", "User");
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء المستخدم الافتراضي: hr");
                }

                if (!UserExists("موارد"))
                {
                    AddUser("موارد", "123", "User");
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء المستخدم الافتراضي: موارد");
                }

                if (!UserExists("user"))
                {
                    AddUser("user", "user123", "User");
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء المستخدم الافتراضي: user");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"تحذير: فشل في إنشاء المستخدمين الافتراضيين: {ex.Message}");
            }
        }

        private static string GenerateSalt()
        {
            byte[] saltBytes = new byte[16];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(saltBytes);
            }
            return Convert.ToBase64String(saltBytes);
        }

        private static string ComputeHash(string password, string salt)
        {
            using (var sha256 = SHA256.Create())
            {
                var combined = password + salt;
                byte[] hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(combined));
                return Convert.ToBase64String(hashBytes);
            }
        }

        // إضافة مستخدم جديد (هاش + ملح)
        public static void AddUser(string username, string password, string role)
        {
            string salt = GenerateSalt();
            string hash = ComputeHash(password, salt);

            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string insert = "INSERT INTO Users (Username, PasswordHash, PasswordSalt, Role, IsActive) VALUES (@u, @h, @s, @r, 1)";
                using (var cmd = new SQLiteCommand(insert, connection))
                {
                    cmd.Parameters.AddWithValue("@u", username);
                    cmd.Parameters.AddWithValue("@h", hash);
                    cmd.Parameters.AddWithValue("@s", salt);
                    cmd.Parameters.AddWithValue("@r", role);
                    cmd.ExecuteNonQuery();
                }
            }
        }

        // تحقق من المستخدم + التحقق من التفعيل
        public static string VerifyUser(string username, string password)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                string query = "SELECT PasswordHash, PasswordSalt, Role, IsActive FROM Users WHERE Username = @u";
                using (var cmd = new SQLiteCommand(query, connection))
                {
                    cmd.Parameters.AddWithValue("@u", username);

                    using (var reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            string storedHash = reader.GetString(0);
                            string storedSalt = reader.GetString(1);
                            string role = reader.GetString(2);
                            bool isActive = reader.GetInt32(3) == 1;

                            if (!isActive)
                                return null; // المستخدم موقوف لا يسمح بالدخول

                            string computedHash = ComputeHash(password, storedSalt);

                            if (storedHash == computedHash)
                                return role;
                        }
                    }
                }
            }
            return null;
        }

        // إلغاء / تفعيل مستخدم (للمدير فقط)
        public static void SetUserActiveStatus(string username, bool isActive)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                string update = "UPDATE Users SET IsActive = @active WHERE Username = @u";
                using (var cmd = new SQLiteCommand(update, connection))
                {
                    cmd.Parameters.AddWithValue("@active", isActive ? 1 : 0);
                    cmd.Parameters.AddWithValue("@u", username);
                    cmd.ExecuteNonQuery();
                }
            }
        }

        // تغيير كلمة المرور (للمدير أو المستخدم نفسه)
        public static bool ChangePassword(string username, string newPassword)
        {
            string newSalt = GenerateSalt();
            string newHash = ComputeHash(newPassword, newSalt);

            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                string update = "UPDATE Users SET PasswordHash = @h, PasswordSalt = @s WHERE Username = @u";
                using (var cmd = new SQLiteCommand(update, connection))
                {
                    cmd.Parameters.AddWithValue("@h", newHash);
                    cmd.Parameters.AddWithValue("@s", newSalt);
                    cmd.Parameters.AddWithValue("@u", username);

                    int rows = cmd.ExecuteNonQuery();
                    return rows > 0;
                }
            }
        }

        // البحث عن مستخدم (مثلاً في حالة نسيان اسم المستخدم)
        public static bool UserExists(string username)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                string query = "SELECT COUNT(*) FROM Users WHERE Username = @u";
                using (var cmd = new SQLiteCommand(query, connection))
                {
                    cmd.Parameters.AddWithValue("@u", username);
                    long count = (long)cmd.ExecuteScalar();
                    return count > 0;
                }
            }
        }

        // جلب كل المستخدمين (مثلاً للمدير لعرضهم في واجهة)
        public static System.Data.DataTable GetAllUsers()
        {
            var dataTable = new System.Data.DataTable();
            dataTable.Columns.Add("Username", typeof(string));
            dataTable.Columns.Add("Role", typeof(string));
            dataTable.Columns.Add("IsActive", typeof(bool));
            dataTable.Columns.Add("Status", typeof(string));

            try
            {
                using (var connection = new SQLiteConnection(connectionString))
                {
                    connection.Open();

                    string query = "SELECT Username, Role, IsActive FROM Users ORDER BY Username";
                    using (var cmd = new SQLiteCommand(query, connection))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                string username = reader.GetString(0);
                                string role = reader.GetString(1);
                                bool isActive = reader.GetInt32(2) == 1;
                                string status = isActive ? "نشط" : "معطل";

                                dataTable.Rows.Add(username, role, isActive, status);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في جلب المستخدمين: {ex.Message}");

                // إضافة بيانات تجريبية في حالة الخطأ
                dataTable.Rows.Add("admin", "Admin", true, "نشط");
                dataTable.Rows.Add("مدير", "Admin", true, "نشط");
                dataTable.Rows.Add("hr", "User", true, "نشط");
                dataTable.Rows.Add("موارد", "User", true, "نشط");
                dataTable.Rows.Add("user", "User", true, "نشط");
            }

            return dataTable;
        }

        // دالة إضافية لجلب المستخدمين كـ Tuple (للاستخدام الداخلي)
        public static (string Username, string Role, bool IsActive)[] GetAllUsersAsTuple()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                string query = "SELECT Username, Role, IsActive FROM Users";
                using (var cmd = new SQLiteCommand(query, connection))
                {
                    using (var reader = cmd.ExecuteReader())
                    {
                        var list = new System.Collections.Generic.List<(string, string, bool)>();
                        while (reader.Read())
                        {
                            string username = reader.GetString(0);
                            string role = reader.GetString(1);
                            bool isActive = reader.GetInt32(2) == 1;
                            list.Add((username, role, isActive));
                        }
                        return list.ToArray();
                    }
                }
            }
        }

        // ===== دوال دعم قاعدة بيانات Access =====

        // تهيئة قاعدة بيانات Access
        public static void InitializeAccessDatabase()
        {
            try
            {
                if (accessManager == null)
                {
                    accessManager = new AccessDatabaseManager();
                }

                bool success = accessManager.InitializeAccessDatabase();
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم تهيئة قاعدة بيانات Access بنجاح");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ فشل في تهيئة قاعدة بيانات Access");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة قاعدة بيانات Access: {ex.Message}");
            }
        }

        // اختبار اتصال قاعدة بيانات Access
        public static bool TestAccessConnection()
        {
            try
            {
                if (accessManager == null)
                {
                    accessManager = new AccessDatabaseManager();
                }

                return accessManager.TestConnection();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في اختبار اتصال Access: {ex.Message}");
                return false;
            }
        }

        // حفظ موظف في قاعدة بيانات Access مع المزايا المتقدمة
        public static int SaveEmployeeToAccess(Employee employee)
        {
            try
            {
                if (accessManager == null)
                {
                    accessManager = new AccessDatabaseManager();
                }

                // تشفير البيانات الحساسة
                Employee encryptedEmployee = EncryptionManager.EncryptEmployeeSensitiveData(employee);

                // حفظ في قاعدة البيانات
                int employeeId = accessManager.AddEmployee(encryptedEmployee);

                // إنشاء تقرير PDF تلقائياً
                Task.Run(async () =>
                {
                    try
                    {
                        string pdfPath = await PDFReportGenerator.GenerateEmployeeReport(employee);

                        // إرسال إشعار بالبريد الإلكتروني
                        NotificationManager.SendNewEmployeeNotification(employee, pdfPath);

                        System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء تقرير PDF وإرسال إشعار للموظف: {employee.Name}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء التقرير أو الإشعار: {ex.Message}");
                        NotificationManager.SendErrorNotification("خطأ في إنشاء تقرير الموظف", ex.Message, ex);
                    }
                });

                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ الموظف بنجاح: {employee.Name} (ID: {employeeId})");
                return employeeId;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ الموظف في Access: {ex.Message}");
                NotificationManager.SendErrorNotification("خطأ في حفظ الموظف", ex.Message, ex);
                throw;
            }
        }

        // جلب جميع الموظفين من قاعدة بيانات Access
        public static DataTable GetAllEmployeesFromAccess()
        {
            try
            {
                if (accessManager == null)
                {
                    accessManager = new AccessDatabaseManager();
                }

                return accessManager.GetAllEmployees();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب الموظفين من Access: {ex.Message}");
                return new DataTable();
            }
        }

        // تشخيص قاعدة بيانات Access
        public static string DiagnoseAccessDatabase()
        {
            try
            {
                var diagnosis = new System.Text.StringBuilder();

                // تشخيص إصدارات Office
                diagnosis.AppendLine("🔍 تشخيص شامل لنظام قاعدة البيانات");
                diagnosis.AppendLine("=" + new string('=', 50));
                diagnosis.AppendLine();

                // تشخيص Office
                diagnosis.AppendLine("🏢 تشخيص إصدارات Microsoft Office:");
                diagnosis.AppendLine(OfficeDiagnostics.GenerateDetailedDiagnosticReport());
                diagnosis.AppendLine();

                // تشخيص قاعدة البيانات
                diagnosis.AppendLine("💾 تشخيص قاعدة بيانات Access:");
                diagnosis.AppendLine("-" + new string('-', 30));

                if (accessManager == null)
                {
                    accessManager = new AccessDatabaseManager();
                }

                diagnosis.AppendLine(accessManager.DiagnoseDatabase());

                // اختبار Connection Strings المختلفة
                diagnosis.AppendLine();
                diagnosis.AppendLine("🔧 اختبار Connection Strings المختلفة:");
                diagnosis.AppendLine("-" + new string('-', 40));

                var connectionStrings = OfficeDiagnostics.GetAllPossibleConnectionStrings("HRManagement.accdb");
                int workingCount = 0;

                foreach (var connStr in connectionStrings)
                {
                    bool works = OfficeDiagnostics.TestConnectionString(connStr);
                    if (works) workingCount++;

                    diagnosis.AppendLine($"   {(works ? "✅" : "❌")} {connStr}");
                }

                diagnosis.AppendLine();
                diagnosis.AppendLine($"📊 ملخص الاختبار: {workingCount} من {connectionStrings.Count} connection strings تعمل");

                // التوصية النهائية
                diagnosis.AppendLine();
                diagnosis.AppendLine("🎯 التوصية النهائية:");
                if (workingCount > 0)
                {
                    string optimal = OfficeDiagnostics.GetOptimalConnectionString("HRManagement.accdb");
                    diagnosis.AppendLine($"   ✅ استخدم: {optimal}");
                }
                else
                {
                    diagnosis.AppendLine("   ❌ لا توجد connection strings تعمل!");
                    diagnosis.AppendLine("   📝 قم بتثبيت Microsoft Access Database Engine");
                }

                return diagnosis.ToString();
            }
            catch (Exception ex)
            {
                return $"❌ خطأ في تشخيص قاعدة بيانات Access: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}";
            }
        }

        // دمج البيانات بين SQLite و Access
        public static void SyncDatabases()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء مزامنة قواعد البيانات...");

                // تهيئة قاعدة بيانات Access
                InitializeAccessDatabase();

                // اختبار الاتصالات
                bool sqliteOk = File.Exists("hr_management.db");
                bool accessOk = TestAccessConnection();

                System.Diagnostics.Debug.WriteLine($"SQLite: {(sqliteOk ? "✅" : "❌")} | Access: {(accessOk ? "✅" : "❌")}");

                if (accessOk)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم تهيئة قاعدة بيانات Access وهي جاهزة للاستخدام");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ قاعدة بيانات Access غير متاحة، سيتم استخدام SQLite فقط");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في مزامنة قواعد البيانات: {ex.Message}");
            }
        }
    }
}
