using System;
using System.Data.SQLite;
using System.Security.Cryptography;
using System.Text;

namespace Ahmedapp_for_work
{
    public static class DatabaseHelper
    {
        private static string connectionString = "Data Source=hr_management.db;Version=3;";

        public static void CreateDatabaseAndTable()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                string createUsersTable = @"
                    CREATE TABLE IF NOT EXISTS Users (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Username TEXT NOT NULL UNIQUE,
                        PasswordHash TEXT NOT NULL,
                        PasswordSalt TEXT NOT NULL,
                        Role TEXT NOT NULL,
                        IsActive INTEGER NOT NULL DEFAULT 1
                    );";

                using (var cmd = new SQLiteCommand(createUsersTable, connection))
                {
                    cmd.ExecuteNonQuery();
                }
            }
        }

        private static string GenerateSalt()
        {
            byte[] saltBytes = new byte[16];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(saltBytes);
            }
            return Convert.ToBase64String(saltBytes);
        }

        private static string ComputeHash(string password, string salt)
        {
            using (var sha256 = SHA256.Create())
            {
                var combined = password + salt;
                byte[] hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(combined));
                return Convert.ToBase64String(hashBytes);
            }
        }

        // إضافة مستخدم جديد (هاش + ملح)
        public static void AddUser(string username, string password, string role)
        {
            string salt = GenerateSalt();
            string hash = ComputeHash(password, salt);

            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string insert = "INSERT INTO Users (Username, PasswordHash, PasswordSalt, Role, IsActive) VALUES (@u, @h, @s, @r, 1)";
                using (var cmd = new SQLiteCommand(insert, connection))
                {
                    cmd.Parameters.AddWithValue("@u", username);
                    cmd.Parameters.AddWithValue("@h", hash);
                    cmd.Parameters.AddWithValue("@s", salt);
                    cmd.Parameters.AddWithValue("@r", role);
                    cmd.ExecuteNonQuery();
                }
            }
        }

        // تحقق من المستخدم + التحقق من التفعيل
        public static string VerifyUser(string username, string password)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                string query = "SELECT PasswordHash, PasswordSalt, Role, IsActive FROM Users WHERE Username = @u";
                using (var cmd = new SQLiteCommand(query, connection))
                {
                    cmd.Parameters.AddWithValue("@u", username);

                    using (var reader = cmd.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            string storedHash = reader.GetString(0);
                            string storedSalt = reader.GetString(1);
                            string role = reader.GetString(2);
                            bool isActive = reader.GetInt32(3) == 1;

                            if (!isActive)
                                return null; // المستخدم موقوف لا يسمح بالدخول

                            string computedHash = ComputeHash(password, storedSalt);

                            if (storedHash == computedHash)
                                return role;
                        }
                    }
                }
            }
            return null;
        }

        // إلغاء / تفعيل مستخدم (للمدير فقط)
        public static void SetUserActiveStatus(string username, bool isActive)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                string update = "UPDATE Users SET IsActive = @active WHERE Username = @u";
                using (var cmd = new SQLiteCommand(update, connection))
                {
                    cmd.Parameters.AddWithValue("@active", isActive ? 1 : 0);
                    cmd.Parameters.AddWithValue("@u", username);
                    cmd.ExecuteNonQuery();
                }
            }
        }

        // تغيير كلمة المرور (للمدير أو المستخدم نفسه)
        public static bool ChangePassword(string username, string newPassword)
        {
            string newSalt = GenerateSalt();
            string newHash = ComputeHash(newPassword, newSalt);

            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                string update = "UPDATE Users SET PasswordHash = @h, PasswordSalt = @s WHERE Username = @u";
                using (var cmd = new SQLiteCommand(update, connection))
                {
                    cmd.Parameters.AddWithValue("@h", newHash);
                    cmd.Parameters.AddWithValue("@s", newSalt);
                    cmd.Parameters.AddWithValue("@u", username);

                    int rows = cmd.ExecuteNonQuery();
                    return rows > 0;
                }
            }
        }

        // البحث عن مستخدم (مثلاً في حالة نسيان اسم المستخدم)
        public static bool UserExists(string username)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                string query = "SELECT COUNT(*) FROM Users WHERE Username = @u";
                using (var cmd = new SQLiteCommand(query, connection))
                {
                    cmd.Parameters.AddWithValue("@u", username);
                    long count = (long)cmd.ExecuteScalar();
                    return count > 0;
                }
            }
        }

        // جلب كل المستخدمين (مثلاً للمدير لعرضهم في واجهة)
        public static (string Username, string Role, bool IsActive)[] GetAllUsers()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                string query = "SELECT Username, Role, IsActive FROM Users";
                using (var cmd = new SQLiteCommand(query, connection))
                {
                    using (var reader = cmd.ExecuteReader())
                    {
                        var list = new System.Collections.Generic.List<(string, string, bool)>();
                        while (reader.Read())
                        {
                            string username = reader.GetString(0);
                            string role = reader.GetString(1);
                            bool isActive = reader.GetInt32(2) == 1;
                            list.Add((username, role, isActive));
                        }
                        return list.ToArray();
                    }
                }
            }
        }
    }
}
