using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Collections.Generic;

namespace Ahmedapp_for_work
{
    public class DatabaseManager
    {
        private readonly string connectionString;

        public DatabaseManager()
        {
            // استخدام سلسلة اتصال محسنة مع معالجة شاملة للأخطاء
            try
            {
                connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                    ?? @"Data Source=(LocalDB)\MSSQLLocalDB;Initial Catalog=HRManagementDB_Professional;Integrated Security=True;Connect Timeout=60;MultipleActiveResultSets=True;TrustServerCertificate=True";
            }
            catch
            {
                // سلسلة اتصال احتياطية محسنة
                connectionString = @"Data Source=(LocalDB)\MSSQLLocalDB;Initial Catalog=HRManagementDB_Professional;Integrated Security=True;Connect Timeout=60;MultipleActiveResultSets=True;TrustServerCertificate=True";
            }

            // محاولة إنشاء قاعدة البيانات عند الإنشاء
            try
            {
                InitializeDatabase();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"تحذير: فشل في إنشاء قاعدة البيانات التلقائي: {ex.Message}");
            }
        }

        public DatabaseManager(string customConnectionString)
        {
            connectionString = customConnectionString;
        }

        public DataTable ExecuteQuery(string query, SqlParameter[]? parameters = null)
        {
            DataTable dataTable = new DataTable();
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }

                        connection.Open();
                        using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                        {
                            adapter.Fill(dataTable);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الاستعلام: {ex.Message}");
            }
            return dataTable;
        }

        public int ExecuteNonQuery(string query, SqlParameter[]? parameters = null)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }

                        connection.Open();
                        return command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ العملية: {ex.Message}");
            }
        }

        public object? ExecuteScalar(string query, SqlParameter[]? parameters = null)
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }

                        connection.Open();
                        return command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الاستعلام: {ex.Message}");
            }
        }

        public bool TestConnection()
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        // إنشاء قاعدة البيانات والجداول
        public bool InitializeDatabase()
        {
            try
            {
                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                CreateDatabaseIfNotExists();

                // إنشاء الجداول
                CreateTablesIfNotExist();

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        private void CreateDatabaseIfNotExists()
        {
            try
            {
                string masterConnectionString = connectionString.Replace("Initial Catalog=HRManagementDB_Professional;", "Initial Catalog=master;");

                using (SqlConnection connection = new SqlConnection(masterConnectionString))
                {
                    connection.Open();

                    string checkDbQuery = "SELECT COUNT(*) FROM sys.databases WHERE name = 'HRManagementDB_Professional'";
                    using (SqlCommand command = new SqlCommand(checkDbQuery, connection))
                    {
                        int dbExists = (int)command.ExecuteScalar();

                        if (dbExists == 0)
                        {
                            string createDbQuery = @"
                                CREATE DATABASE HRManagementDB_Professional
                                ON (
                                    NAME = 'HRManagementDB_Professional',
                                    FILENAME = 'HRManagementDB_Professional.mdf',
                                    SIZE = 100MB,
                                    MAXSIZE = 1GB,
                                    FILEGROWTH = 10MB
                                )
                                LOG ON (
                                    NAME = 'HRManagementDB_Professional_Log',
                                    FILENAME = 'HRManagementDB_Professional_Log.ldf',
                                    SIZE = 10MB,
                                    MAXSIZE = 100MB,
                                    FILEGROWTH = 5MB
                                )";

                            using (SqlCommand createCommand = new SqlCommand(createDbQuery, connection))
                            {
                                createCommand.CommandTimeout = 120; // زيادة وقت الانتظار
                                createCommand.ExecuteNonQuery();
                            }

                            System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قاعدة البيانات HRManagementDB_Professional بنجاح");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("✅ قاعدة البيانات HRManagementDB_Professional موجودة بالفعل");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إنشاء قاعدة البيانات: {ex.Message}");
            }
        }

        private void CreateTablesIfNotExist()
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    // إنشاء جدول الموظفين المحسن
                    string createEmployeesTable = @"
                        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Employees' AND xtype='U')
                        CREATE TABLE Employees (
                            Id INT IDENTITY(1,1) PRIMARY KEY,

                            -- البيانات الأساسية
                            Name NVARCHAR(255) NOT NULL,
                            NationalId NVARCHAR(14) UNIQUE NOT NULL,
                            InsuranceNumber NVARCHAR(50),
                            JobTitle NVARCHAR(255),
                            Department NVARCHAR(255),
                            HireDate DATE,
                            EmployeeType NVARCHAR(50) NOT NULL DEFAULT 'منتدب',
                            EmployeeStatus NVARCHAR(50) DEFAULT 'نشط',
                            Salary DECIMAL(18,2),

                            -- معلومات الاتصال
                            Phone NVARCHAR(20),
                            Email NVARCHAR(255),
                            Address NTEXT,

                            -- بيانات النظام
                            CreatedBy NVARCHAR(255) DEFAULT 'النظام',
                            CreatedDate DATETIME DEFAULT GETDATE(),
                            LastModified DATETIME DEFAULT GETDATE(),
                            ModifiedBy NVARCHAR(255),
                            IsActive BIT DEFAULT 1,

                            -- حقول الانتداب
                            DecisionNumber NVARCHAR(100),
                            DecisionDate DATETIME,
                            FromToEntity NVARCHAR(500),
                            WorkDepartment NVARCHAR(255),
                            Management NVARCHAR(255),
                            LeaveEndDate DATETIME,
                            AppointmentType NVARCHAR(100),
                            RequiredDocuments NTEXT,

                            -- الاشتراكات التأمينية
                            TotalInsuranceContributions DECIMAL(18,2),
                            Rate12Percent DECIMAL(18,2),
                            Rate9Percent DECIMAL(18,2),
                            Rate3Percent DECIMAL(18,2),
                            Rate1Percent_1 DECIMAL(18,2),
                            Rate1Percent_2 DECIMAL(18,2),
                            Rate1Percent_3 DECIMAL(18,2),
                            Rate1Percent_4 DECIMAL(18,2),
                            Rate025Percent DECIMAL(18,2),

                            -- الحقول الإضافية
                            CashReplacement DECIMAL(18,2),
                            ConsiderationPeriod INT, -- بالأشهر
                            LoanInstallment DECIMAL(18,2),
                            OtherDetails NTEXT,

                            -- حقول إصابة العمل
                            InjuryType NVARCHAR(255),
                            InjuryDate DATETIME,
                            InjuryLocation NVARCHAR(255),
                            InjuryDescription NTEXT,

                            -- حقول الإجازة بدون مرتب
                            UnpaidLeaveType NVARCHAR(255),
                            LeaveStatus NVARCHAR(100),
                            LeaveStartDate DATETIME,
                            UnpaidLeaveEndDate DATETIME,
                            LeaveReason NTEXT,

                            -- فهارس للبحث السريع
                            INDEX IX_Employees_NationalId (NationalId),
                            INDEX IX_Employees_Name (Name),
                            INDEX IX_Employees_Department (Department),
                            INDEX IX_Employees_EmployeeType (EmployeeType),
                            INDEX IX_Employees_CreatedDate (CreatedDate)
                        )";

                    using (SqlCommand command = new SqlCommand(createEmployeesTable, connection))
                    {
                        command.ExecuteNonQuery();
                    }

                    // إنشاء جدول ملفات الموظفين المحسن
                    string createEmployeeFilesTable = @"
                        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmployeeFiles' AND xtype='U')
                        CREATE TABLE EmployeeFiles (
                            Id INT IDENTITY(1,1) PRIMARY KEY,
                            EmployeeId INT NOT NULL,

                            -- معلومات الملف
                            FileName NVARCHAR(255) NOT NULL,
                            OriginalFileName NVARCHAR(255),
                            FilePath NVARCHAR(500) NOT NULL,
                            FileType NVARCHAR(50),
                            FileExtension NVARCHAR(10),
                            FileSize BIGINT,
                            FileSizeFormatted AS (
                                CASE
                                    WHEN FileSize < 1024 THEN CAST(FileSize AS NVARCHAR) + ' بايت'
                                    WHEN FileSize < 1048576 THEN CAST(FileSize/1024 AS NVARCHAR) + ' كيلوبايت'
                                    WHEN FileSize < 1073741824 THEN CAST(FileSize/1048576 AS NVARCHAR) + ' ميجابايت'
                                    ELSE CAST(FileSize/1073741824 AS NVARCHAR) + ' جيجابايت'
                                END
                            ),

                            -- تصنيف الملف
                            Category NVARCHAR(100) DEFAULT 'عام',
                            SubCategory NVARCHAR(100),
                            DocumentType NVARCHAR(100), -- CV, شهادة, عقد, إلخ
                            Priority NVARCHAR(20) DEFAULT 'عادي', -- عالي, متوسط, عادي

                            -- وصف ومعلومات إضافية
                            Description NTEXT,
                            Tags NVARCHAR(500), -- للبحث
                            Notes NTEXT,

                            -- معلومات الرفع
                            UploadedBy NVARCHAR(255) DEFAULT 'النظام',
                            UploadDate DATETIME DEFAULT GETDATE(),
                            UploadIP NVARCHAR(45),

                            -- حالة الملف
                            IsActive BIT DEFAULT 1,
                            IsPublic BIT DEFAULT 0,
                            IsArchived BIT DEFAULT 0,
                            ExpiryDate DATETIME,

                            -- معلومات التعديل
                            LastAccessed DATETIME,
                            AccessCount INT DEFAULT 0,
                            LastModified DATETIME DEFAULT GETDATE(),
                            ModifiedBy NVARCHAR(255),

                            -- أمان الملف
                            FileHash NVARCHAR(64), -- SHA-256
                            IsEncrypted BIT DEFAULT 0,
                            AccessLevel NVARCHAR(20) DEFAULT 'عام', -- سري, محدود, عام

                            -- فهارس للبحث السريع
                            INDEX IX_EmployeeFiles_EmployeeId (EmployeeId),
                            INDEX IX_EmployeeFiles_Category (Category),
                            INDEX IX_EmployeeFiles_UploadDate (UploadDate),
                            INDEX IX_EmployeeFiles_FileType (FileType),

                            FOREIGN KEY (EmployeeId) REFERENCES Employees(Id) ON DELETE CASCADE
                        )";

                    using (SqlCommand command = new SqlCommand(createEmployeeFilesTable, connection))
                    {
                        command.ExecuteNonQuery();
                    }

                    // إنشاء جدول الإشعارات
                    string createNotificationsTable = @"
                        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Notifications' AND xtype='U')
                        CREATE TABLE Notifications (
                            NotificationID INT IDENTITY(1,1) PRIMARY KEY,
                            Title NVARCHAR(255) NOT NULL,
                            Message NTEXT NOT NULL,
                            NotificationType NVARCHAR(50) DEFAULT 'Info',
                            Priority NVARCHAR(20) DEFAULT 'Medium',
                            TargetUsers NVARCHAR(255) DEFAULT 'All',
                            IsActive BIT DEFAULT 1,
                            ExpiryDate DATETIME,
                            CreatedBy NVARCHAR(255),
                            CreatedDate DATETIME DEFAULT GETDATE(),
                            ReadBy NTEXT
                        )";

                    using (SqlCommand command = new SqlCommand(createNotificationsTable, connection))
                    {
                        command.ExecuteNonQuery();
                    }

                    // إنشاء جدول الملاحظات
                    string createNotesTable = @"
                        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Notes' AND xtype='U')
                        CREATE TABLE Notes (
                            NoteID INT IDENTITY(1,1) PRIMARY KEY,
                            Title NVARCHAR(255) NOT NULL,
                            Content NTEXT NOT NULL,
                            Category NVARCHAR(100) DEFAULT 'General',
                            RelatedEntityType NVARCHAR(50),
                            RelatedEntityID INT,
                            IsPrivate BIT DEFAULT 0,
                            CreatedBy NVARCHAR(255),
                            CreatedDate DATETIME DEFAULT GETDATE(),
                            LastModified DATETIME DEFAULT GETDATE(),
                            Tags NVARCHAR(500)
                        )";

                    using (SqlCommand command = new SqlCommand(createNotesTable, connection))
                    {
                        command.ExecuteNonQuery();
                    }

                    // إنشاء جدول التلميحات
                    string createTipsTable = @"
                        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Tips' AND xtype='U')
                        CREATE TABLE Tips (
                            TipID INT IDENTITY(1,1) PRIMARY KEY,
                            Title NVARCHAR(255) NOT NULL,
                            Content NTEXT NOT NULL,
                            Category NVARCHAR(100) DEFAULT 'General',
                            TipType NVARCHAR(50) DEFAULT 'Tip',
                            IsActive BIT DEFAULT 1,
                            DisplayOrder INT DEFAULT 0,
                            CreatedBy NVARCHAR(255),
                            CreatedDate DATETIME DEFAULT GETDATE(),
                            LastModified DATETIME DEFAULT GETDATE()
                        )";

                    using (SqlCommand command = new SqlCommand(createTipsTable, connection))
                    {
                        command.ExecuteNonQuery();
                    }

                    // إنشاء جدول سجل النشاطات
                    string createActivityLogTable = @"
                        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ActivityLog' AND xtype='U')
                        CREATE TABLE ActivityLog (
                            Id INT IDENTITY(1,1) PRIMARY KEY,
                            UserId NVARCHAR(255),
                            UserName NVARCHAR(255),
                            Action NVARCHAR(255) NOT NULL,
                            EntityType NVARCHAR(100), -- Employee, File, etc.
                            EntityId INT,
                            Description NTEXT,
                            IPAddress NVARCHAR(45),
                            UserAgent NVARCHAR(500),
                            Timestamp DATETIME DEFAULT GETDATE(),

                            INDEX IX_ActivityLog_Timestamp (Timestamp),
                            INDEX IX_ActivityLog_UserId (UserId),
                            INDEX IX_ActivityLog_Action (Action)
                        )";

                    using (SqlCommand command = new SqlCommand(createActivityLogTable, connection))
                    {
                        command.ExecuteNonQuery();
                    }

                    // إنشاء جدول إعدادات النظام
                    string createSystemSettingsTable = @"
                        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SystemSettings' AND xtype='U')
                        CREATE TABLE SystemSettings (
                            Id INT IDENTITY(1,1) PRIMARY KEY,
                            SettingKey NVARCHAR(100) UNIQUE NOT NULL,
                            SettingValue NTEXT,
                            SettingType NVARCHAR(50) DEFAULT 'String', -- String, Number, Boolean, JSON
                            Category NVARCHAR(100) DEFAULT 'عام',
                            Description NTEXT,
                            IsEditable BIT DEFAULT 1,
                            CreatedDate DATETIME DEFAULT GETDATE(),
                            LastModified DATETIME DEFAULT GETDATE(),
                            ModifiedBy NVARCHAR(255),

                            INDEX IX_SystemSettings_Category (Category),
                            INDEX IX_SystemSettings_SettingKey (SettingKey)
                        )";

                    using (SqlCommand command = new SqlCommand(createSystemSettingsTable, connection))
                    {
                        command.ExecuteNonQuery();
                    }

                    // إنشاء جدول النسخ الاحتياطية
                    string createBackupsTable = @"
                        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DatabaseBackups' AND xtype='U')
                        CREATE TABLE DatabaseBackups (
                            Id INT IDENTITY(1,1) PRIMARY KEY,
                            BackupName NVARCHAR(255) NOT NULL,
                            BackupPath NVARCHAR(500) NOT NULL,
                            BackupSize BIGINT,
                            BackupType NVARCHAR(50) DEFAULT 'كامل', -- كامل, تزايدي, تفاضلي
                            Status NVARCHAR(50) DEFAULT 'مكتمل', -- جاري, مكتمل, فاشل
                            StartTime DATETIME,
                            EndTime DATETIME,
                            Duration AS DATEDIFF(SECOND, StartTime, EndTime),
                            CreatedBy NVARCHAR(255) DEFAULT 'النظام',
                            Notes NTEXT,

                            INDEX IX_DatabaseBackups_BackupType (BackupType),
                            INDEX IX_DatabaseBackups_StartTime (StartTime)
                        )";

                    using (SqlCommand command = new SqlCommand(createBackupsTable, connection))
                    {
                        command.ExecuteNonQuery();
                    }

                    // إدراج إعدادات النظام الافتراضية
                    string insertDefaultSettings = @"
                        IF NOT EXISTS (SELECT * FROM SystemSettings WHERE SettingKey = 'SystemVersion')
                        INSERT INTO SystemSettings (SettingKey, SettingValue, SettingType, Category, Description) VALUES
                        ('SystemVersion', '3.8.0', 'String', 'النظام', 'إصدار نظام إدارة الموارد البشرية'),
                        ('MaxFileSize', '10485760', 'Number', 'الملفات', 'الحد الأقصى لحجم الملف بالبايت (10 ميجابايت)'),
                        ('AllowedFileTypes', 'pdf,doc,docx,jpg,jpeg,png,gif,txt,xlsx,xls', 'String', 'الملفات', 'أنواع الملفات المسموحة'),
                        ('AutoBackup', 'true', 'Boolean', 'النسخ الاحتياطي', 'تفعيل النسخ الاحتياطي التلقائي'),
                        ('BackupInterval', '24', 'Number', 'النسخ الاحتياطي', 'فترة النسخ الاحتياطي بالساعات'),
                        ('DatabaseName', 'HRManagementDB_Professional', 'String', 'النظام', 'اسم قاعدة البيانات'),
                        ('CompanyName', 'شركة إدارة الموارد البشرية', 'String', 'الشركة', 'اسم الشركة'),
                        ('SystemLanguage', 'ar', 'String', 'النظام', 'لغة النظام الافتراضية'),
                        ('DateFormat', 'dd/MM/yyyy', 'String', 'النظام', 'تنسيق التاريخ'),
                        ('CurrencySymbol', 'ج.م', 'String', 'النظام', 'رمز العملة')";

                    using (SqlCommand command = new SqlCommand(insertDefaultSettings, connection))
                    {
                        command.ExecuteNonQuery();
                    }

                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء جميع الجداول والإعدادات بنجاح");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إنشاء الجداول: {ex.Message}");
            }
        }

        // تشخيص شامل لقاعدة البيانات
        public string ComprehensiveDatabaseDiagnosis()
        {
            var diagnosis = new List<string>();

            try
            {
                // 1. فحص الاتصال
                diagnosis.Add("🔍 فحص الاتصال بقاعدة البيانات:");
                if (TestConnection())
                {
                    diagnosis.Add("✅ الاتصال بقاعدة البيانات ناجح");
                }
                else
                {
                    diagnosis.Add("❌ فشل الاتصال بقاعدة البيانات");
                    diagnosis.Add("🔧 محاولة إنشاء قاعدة البيانات...");

                    if (InitializeDatabase())
                    {
                        diagnosis.Add("✅ تم إنشاء قاعدة البيانات بنجاح");
                    }
                    else
                    {
                        diagnosis.Add("❌ فشل في إنشاء قاعدة البيانات");
                        return string.Join("\n", diagnosis);
                    }
                }

                // 2. فحص الجداول
                diagnosis.Add("\n📊 فحص الجداول:");
                string[] requiredTables = { "Employees", "EmployeeFiles", "Notifications", "Notes", "Tips", "ActivityLog", "SystemSettings", "DatabaseBackups" };

                foreach (string tableName in requiredTables)
                {
                    try
                    {
                        var result = ExecuteQuery($"SELECT COUNT(*) FROM {tableName}");
                        int count = Convert.ToInt32(result.Rows[0][0]);
                        diagnosis.Add($"✅ جدول {tableName}: موجود - عدد السجلات: {count}");
                    }
                    catch (Exception ex)
                    {
                        diagnosis.Add($"❌ جدول {tableName}: غير موجود أو تالف - {ex.Message}");
                    }
                }

                // 3. فحص بنية جدول الموظفين
                diagnosis.Add("\n🏗️ فحص بنية جدول الموظفين:");
                try
                {
                    var columns = ExecuteQuery("SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' ORDER BY ORDINAL_POSITION");
                    diagnosis.Add($"✅ عدد الأعمدة: {columns.Rows.Count}");

                    string[] requiredColumns = { "Id", "Name", "NationalId", "InsuranceNumber", "FromToEntity", "EmployeeType" };
                    foreach (string columnName in requiredColumns)
                    {
                        bool found = false;
                        foreach (DataRow row in columns.Rows)
                        {
                            if (row["COLUMN_NAME"].ToString() == columnName)
                            {
                                found = true;
                                diagnosis.Add($"✅ العمود {columnName}: موجود ({row["DATA_TYPE"]})");
                                break;
                            }
                        }
                        if (!found)
                        {
                            diagnosis.Add($"❌ العمود {columnName}: غير موجود");
                        }
                    }
                }
                catch (Exception ex)
                {
                    diagnosis.Add($"❌ فشل في فحص بنية الجدول: {ex.Message}");
                }

                // 4. اختبار عمليات قاعدة البيانات
                diagnosis.Add("\n🧪 اختبار العمليات:");
                try
                {
                    // اختبار الإدراج
                    string testQuery = "SELECT 1 as TestResult";
                    var testResult = ExecuteQuery(testQuery);
                    diagnosis.Add("✅ عملية الاستعلام تعمل بشكل صحيح");
                }
                catch (Exception ex)
                {
                    diagnosis.Add($"❌ فشل في اختبار العمليات: {ex.Message}");
                }

                diagnosis.Add("\n🎯 النتيجة النهائية:");
                diagnosis.Add("✅ قاعدة البيانات جاهزة للاستخدام");

            }
            catch (Exception ex)
            {
                diagnosis.Add($"\n❌ خطأ عام في التشخيص: {ex.Message}");
            }

            return string.Join("\n", diagnosis);
        }

        // إضافة دعم حفظ الملفات المحسن
        public bool SaveEmployeeFile(int employeeId, string fileName, string filePath, string fileType, long fileSize, string uploadedBy, string category = "", string description = "")
        {
            try
            {
                // استخراج معلومات إضافية من الملف
                string originalFileName = System.IO.Path.GetFileName(fileName);
                string fileExtension = System.IO.Path.GetExtension(fileName).ToLower();
                string documentType = GetDocumentType(fileExtension);
                string fileHash = CalculateFileHash(filePath);

                string query = @"INSERT INTO EmployeeFiles
                    (EmployeeId, FileName, OriginalFileName, FilePath, FileType, FileExtension, FileSize,
                     Category, DocumentType, Description, UploadedBy, FileHash, UploadIP)
                    VALUES
                    (@EmployeeId, @FileName, @OriginalFileName, @FilePath, @FileType, @FileExtension, @FileSize,
                     @Category, @DocumentType, @Description, @UploadedBy, @FileHash, @UploadIP)";

                SqlParameter[] parameters = {
                    new SqlParameter("@EmployeeId", employeeId),
                    new SqlParameter("@FileName", fileName ?? ""),
                    new SqlParameter("@OriginalFileName", originalFileName ?? ""),
                    new SqlParameter("@FilePath", filePath ?? ""),
                    new SqlParameter("@FileType", fileType ?? ""),
                    new SqlParameter("@FileExtension", fileExtension ?? ""),
                    new SqlParameter("@FileSize", fileSize),
                    new SqlParameter("@Category", category ?? "عام"),
                    new SqlParameter("@DocumentType", documentType ?? "مستند"),
                    new SqlParameter("@Description", description ?? ""),
                    new SqlParameter("@UploadedBy", uploadedBy ?? "النظام"),
                    new SqlParameter("@FileHash", fileHash ?? ""),
                    new SqlParameter("@UploadIP", GetLocalIPAddress())
                };

                bool result = ExecuteNonQuery(query, parameters) > 0;

                if (result)
                {
                    // تسجيل النشاط
                    LogActivity(uploadedBy, "رفع ملف", "EmployeeFile", employeeId, $"تم رفع الملف: {originalFileName}");
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الملف: {ex.Message}");
                return false;
            }
        }

        // دالة مساعدة لتحديد نوع المستند
        private string GetDocumentType(string fileExtension)
        {
            return fileExtension.ToLower() switch
            {
                ".pdf" => "مستند PDF",
                ".doc" or ".docx" => "مستند Word",
                ".xls" or ".xlsx" => "جدول بيانات",
                ".jpg" or ".jpeg" or ".png" or ".gif" => "صورة",
                ".txt" => "ملف نصي",
                ".zip" or ".rar" => "ملف مضغوط",
                _ => "مستند عام"
            };
        }

        // دالة مساعدة لحساب hash الملف
        private string CalculateFileHash(string filePath)
        {
            try
            {
                if (!System.IO.File.Exists(filePath)) return "";

                using (var sha256 = System.Security.Cryptography.SHA256.Create())
                {
                    using (var stream = System.IO.File.OpenRead(filePath))
                    {
                        byte[] hash = sha256.ComputeHash(stream);
                        return Convert.ToBase64String(hash);
                    }
                }
            }
            catch
            {
                return "";
            }
        }

        // دالة مساعدة للحصول على IP المحلي
        private string GetLocalIPAddress()
        {
            try
            {
                return System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName())
                    .AddressList.FirstOrDefault(ip => ip.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)?.ToString() ?? "127.0.0.1";
            }
            catch
            {
                return "127.0.0.1";
            }
        }

        // دالة تسجيل النشاطات
        public void LogActivity(string userId, string action, string entityType, int? entityId, string description)
        {
            try
            {
                string query = @"INSERT INTO ActivityLog (UserId, UserName, Action, EntityType, EntityId, Description, IPAddress)
                               VALUES (@UserId, @UserName, @Action, @EntityType, @EntityId, @Description, @IPAddress)";

                SqlParameter[] parameters = {
                    new SqlParameter("@UserId", userId ?? "النظام"),
                    new SqlParameter("@UserName", userId ?? "النظام"),
                    new SqlParameter("@Action", action ?? ""),
                    new SqlParameter("@EntityType", entityType ?? ""),
                    new SqlParameter("@EntityId", (object)entityId ?? DBNull.Value),
                    new SqlParameter("@Description", description ?? ""),
                    new SqlParameter("@IPAddress", GetLocalIPAddress())
                };

                ExecuteNonQuery(query, parameters);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تسجيل النشاط: {ex.Message}");
            }
        }

        // إضافة دعم استرجاع ملفات الموظف
        public DataTable GetEmployeeFiles(int employeeId)
        {
            try
            {
                string query = @"SELECT * FROM EmployeeFiles WHERE EmployeeId = @EmployeeId ORDER BY UploadDate DESC";
                SqlParameter[] parameters = { new SqlParameter("@EmployeeId", employeeId) };
                return ExecuteQuery(query, parameters);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في استرجاع الملفات: {ex.Message}");
                return new DataTable();
            }
        }

        // إضافة دعم حذف الملفات
        public bool DeleteEmployeeFile(int fileId)
        {
            try
            {
                string query = "DELETE FROM EmployeeFiles WHERE Id = @FileId";
                SqlParameter[] parameters = { new SqlParameter("@FileId", fileId) };
                return ExecuteNonQuery(query, parameters) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف الملف: {ex.Message}");
                return false;
            }
        }


    }

    // نماذج البيانات - Data Models
    public class Notification
    {
        public int NotificationID { get; set; }
        public string Title { get; set; } = "";
        public string Message { get; set; } = "";
        public string NotificationType { get; set; } = "Info";
        public string Priority { get; set; } = "Medium";
        public string TargetUsers { get; set; } = "All";
        public bool IsActive { get; set; } = true;
        public DateTime? ExpiryDate { get; set; }
        public string CreatedBy { get; set; } = "";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string ReadBy { get; set; } = "";
    }

    public class Note
    {
        public int NoteID { get; set; }
        public string Title { get; set; } = "";
        public string Content { get; set; } = "";
        public string Category { get; set; } = "General";
        public string RelatedEntityType { get; set; } = "";
        public int? RelatedEntityID { get; set; }
        public bool IsPrivate { get; set; } = false;
        public string CreatedBy { get; set; } = "";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;
        public string Tags { get; set; } = "";
    }

    public class Tip
    {
        public int TipID { get; set; }
        public string Title { get; set; } = "";
        public string Content { get; set; } = "";
        public string Category { get; set; } = "General";
        public string TipType { get; set; } = "Tip";
        public bool IsActive { get; set; } = true;
        public int DisplayOrder { get; set; } = 0;
        public string CreatedBy { get; set; } = "";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime LastModified { get; set; } = DateTime.Now;
    }

    // فئات إدارة البيانات - Data Access Classes
    public class DatabaseNotificationManager
    {
        private readonly DatabaseManager dbManager;

        public DatabaseNotificationManager()
        {
            dbManager = new DatabaseManager();
        }

        public List<Notification> GetActiveNotifications()
        {
            var notifications = new List<Notification>();
            try
            {
                string query = @"SELECT * FROM Notifications
                               WHERE IsActive = 1 AND (ExpiryDate IS NULL OR ExpiryDate > GETDATE())
                               ORDER BY Priority DESC, CreatedDate DESC";

                DataTable dt = dbManager.ExecuteQuery(query);
                foreach (DataRow row in dt.Rows)
                {
                    notifications.Add(MapRowToNotification(row));
                }
            }
            catch (Exception)
            {
                // في حالة عدم وجود قاعدة البيانات، إرجاع بيانات تجريبية
                notifications.AddRange(GetSampleNotifications());
            }
            return notifications;
        }

        public bool AddNotification(Notification notification)
        {
            try
            {
                string query = @"INSERT INTO Notifications (Title, Message, NotificationType, Priority, TargetUsers, IsActive, ExpiryDate, CreatedBy)
                               VALUES (@Title, @Message, @NotificationType, @Priority, @TargetUsers, @IsActive, @ExpiryDate, @CreatedBy)";

                SqlParameter[] parameters = {
                    new SqlParameter("@Title", notification.Title),
                    new SqlParameter("@Message", notification.Message),
                    new SqlParameter("@NotificationType", notification.NotificationType),
                    new SqlParameter("@Priority", notification.Priority),
                    new SqlParameter("@TargetUsers", notification.TargetUsers),
                    new SqlParameter("@IsActive", notification.IsActive),
                    new SqlParameter("@ExpiryDate", (object)notification.ExpiryDate ?? DBNull.Value),
                    new SqlParameter("@CreatedBy", notification.CreatedBy)
                };

                return dbManager.ExecuteNonQuery(query, parameters) > 0;
            }
            catch
            {
                return false;
            }
        }

        private Notification MapRowToNotification(DataRow row)
        {
            return new Notification
            {
                NotificationID = Convert.ToInt32(row["NotificationID"]),
                Title = row["Title"].ToString() ?? "",
                Message = row["Message"].ToString() ?? "",
                NotificationType = row["NotificationType"].ToString() ?? "Info",
                Priority = row["Priority"].ToString() ?? "Medium",
                TargetUsers = row["TargetUsers"].ToString() ?? "All",
                IsActive = Convert.ToBoolean(row["IsActive"]),
                ExpiryDate = row["ExpiryDate"] == DBNull.Value ? null : Convert.ToDateTime(row["ExpiryDate"]),
                CreatedBy = row["CreatedBy"].ToString() ?? "",
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                ReadBy = row["ReadBy"].ToString() ?? ""
            };
        }

        private List<Notification> GetSampleNotifications()
        {
            return new List<Notification>
            {
                new Notification
                {
                    NotificationID = 1,
                    Title = "تحديث النظام",
                    Message = "سيتم تحديث نظام إدارة الموارد البشرية يوم الجمعة القادم من الساعة 6-8 مساءً",
                    NotificationType = "Info",
                    Priority = "Medium",
                    CreatedBy = "مدير النظام"
                },
                new Notification
                {
                    NotificationID = 2,
                    Title = "موعد نهائي للتقارير الشهرية",
                    Message = "يرجى تسليم التقارير الشهرية قبل نهاية الأسبوع الجاري",
                    NotificationType = "Warning",
                    Priority = "High",
                    CreatedBy = "مدير الموارد البشرية"
                },
                new Notification
                {
                    NotificationID = 3,
                    Title = "تهنئة",
                    Message = "تم ترقية الموظف أحمد إبراهيم إلى منصب كبير المطورين",
                    NotificationType = "Success",
                    Priority = "Medium",
                    CreatedBy = "الإدارة العليا"
                }
            };
        }
    }
}
