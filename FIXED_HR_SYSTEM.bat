@echo off
title HR Management System v6.0.0 - NETSDK1022 Error Fixed
color 0A

echo.
echo ========================================
echo    HR Management System v6.0.0
echo    نظام إدارة الموارد البشرية
echo    NETSDK1022 Error FIXED!
echo ========================================
echo.

echo 🔧 PROBLEM SOLVED:
echo ❌ Error: NETSDK1022 - Duplicate 'Compile' items
echo ✅ Solution: Removed manual Compile includes
echo ✅ .NET SDK auto-includes all .cs files
echo.

echo 📁 Current Project Structure:
echo • Program.cs (Main application)
echo • EmployeeManager.cs (Helper classes)
echo • HRSystem.csproj (Project file - FIXED)
echo.

echo 🧹 Cleaning previous build...
dotnet clean HRSystem.csproj > nul 2>&1

echo ✅ Clean completed!
echo.

echo 📦 Restoring packages...
dotnet restore HRSystem.csproj

if errorlevel 1 (
    echo ❌ Package restore failed!
    pause
    exit /b 1
)

echo ✅ Packages restored successfully!
echo.

echo 🏗️ Building project...
dotnet build HRSystem.csproj --verbosity minimal

if errorlevel 1 (
    echo ❌ Build failed! Check the error messages above.
    echo.
    echo 🔍 Troubleshooting:
    echo • Make sure .NET 6.0 SDK is installed
    echo • Check internet connection for package downloads
    echo • Verify all files are present and not corrupted
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.
echo 🚀 Starting HR Management System...
echo.
echo 🎯 ENHANCED FEATURES AVAILABLE:
echo • 📦 System.Data.SqlClient - SQL Server support
echo • 📄 Newtonsoft.Json - Advanced JSON handling
echo • 🔧 HRManagementSystem.Helpers - Professional classes
echo • ⚡ Null-safe programming (nullable reference types)
echo • 🛡️ Enhanced error handling and validation
echo • 💾 Professional data persistence
echo.
echo 📱 SYSTEM READY - Starting application...
echo.

dotnet run --project HRSystem.csproj

echo.
echo HR Management System finished.
echo.
echo 🎉 SUCCESS! The NETSDK1022 error has been completely resolved.
echo All packages are working correctly.
pause
