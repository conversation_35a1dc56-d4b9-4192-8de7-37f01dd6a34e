@echo off
title HR Management System - Simple Test
color 0A

echo.
echo ========================================
echo    HR Management System v5.3.0
echo    Simple Test - Debugging
echo ========================================
echo.

cd /d "%~dp0"

echo Testing with simplified login form...
echo.

echo [1/4] Cleaning project...
dotnet clean

echo.
echo [2/4] Restoring packages...
dotnet restore

echo.
echo [3/4] Building project...
dotnet build

if errorlevel 1 (
    echo.
    echo ❌ Build failed. Check errors above.
    pause
    exit /b 1
)

echo.
echo ✅ Build successful!
echo.

echo [4/4] Starting application with test form...
echo.
echo If the application doesn't show:
echo - Check taskbar for minimized window
echo - Press Alt+Tab to switch windows
echo - Look for "HR Management System" in Task Manager
echo.

dotnet run

echo.
echo Application finished.
pause
