using System;
using System.Drawing;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    public partial class MinimalTest : Form
    {
        public MinimalTest()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "HR System Test - Working!";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.LightGreen;

            Label testLabel = new Label
            {
                Text = "✅ HR Management System is working!\n\nThe application started successfully.\n\nClick OK to continue.",
                Font = new Font("Arial", 14, FontStyle.Bold),
                Size = new Size(350, 150),
                Location = new Point(25, 50),
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = Color.DarkGreen
            };

            Button okButton = new Button
            {
                Text = "OK",
                Size = new Size(100, 40),
                Location = new Point(150, 220),
                BackColor = Color.Green,
                ForeColor = Color.White,
                Font = new Font("Arial", 12, FontStyle.Bold)
            };

            okButton.Click += (s, e) => {
                MessageBox.Show("HR Management System v5.3.0\n\nApplication is working correctly!\n\nYou can now close this test.", 
                               "Success!", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.Close();
            };

            this.Controls.Add(testLabel);
            this.Controls.Add(okButton);
        }
    }
}
