# نظام إدارة الموارد البشرية المتقدم - Advanced HR Management System

## نظرة عامة - Overview
نظام شامل ومتقدم لإدارة الموارد البشرية مع واجهة احترافية ثنائية اللغة (عربي/إنجليزي) وميزات متطورة لإدارة جميع جوانب الموارد البشرية.

A comprehensive and advanced HR management system with professional bilingual interface (Arabic/English) and advanced features for managing all aspects of human resources.

## معلومات المطور - Developer Information
- **المطور**: <PERSON>
- **البريد الإلكتروني**: <EMAIL>
- **الإصدار**: 3.1.0
- **التاريخ**: 2024

## الميزات الرئيسية - Main Features

### 🏢 إدارة الموظفين - Employee Management
- إضافة وتعديل بيانات الموظفين
- تتبع المناصب والأقسام
- إدارة الرواتب والمعلومات الشخصية

### 📋 إدارة النماذج - Forms Management
- إنشاء وإدارة النماذج المختلفة
- تتبع حالة النماذج (معلق، موافق عليه، مرفوض)
- ربط النماذج بالموظفين

### 🏥 ملفات إصابات العمل - Work Injury Files
- توثيق إصابات العمل
- متابعة العلاج والتقارير الطبية
- إدارة حالات الإصابة

### 🏖️ الإجازات بدون مرتب - Unpaid Leave Management
- **إجازة داخل البلاد** - Domestic Leave
- **إجازة خارج البلاد** - International Leave
- **إجازة رعاية طفل** - Child Care Leave
- تتبع فترات الإجازة وأسباب الموافقة

### 🏛️ إدارة المنتدبين - City Council Delegates
- **المنتدبين إلى مجلس المدينة** - Delegates TO City Council
- **المنتدبين من مجلس المدينة** - Delegates FROM City Council
- إحصائيات شاملة للانتدابات
- تتبع فترات الانتداب والأهداف

### 🔔 نظام التنبيهات - Notifications System **جديد!**
- إنشاء وإدارة التنبيهات للمستخدمين المخولين
- تصنيف التنبيهات حسب النوع (معلومات، تحذير، خطأ، نجاح)
- تحديد أولوية التنبيهات (منخفضة، متوسطة، عالية، حرجة)
- تحديد المستخدمين المستهدفين
- تاريخ انتهاء صلاحية التنبيهات
- واجهة ملونة حسب نوع التنبيه
- إضافة تنبيهات جديدة من خلال نماذج تفاعلية

### 📝 إدارة الملاحظات - Notes Management **جديد!**
- **ملاحظات عامة** - General Notes
- **ملاحظات الموظفين** - Employee Notes مع وصف محتوى الملفات
- **ملاحظات العمليات** - Process Notes
- **التذكيرات** - Reminders
- ربط الملاحظات بالكيانات المختلفة (موظفين، نماذج، إجازات)
- نظام العلامات (Tags) لتسهيل البحث والتصنيف
- ملاحظات خاصة ومشتركة
- تصفية الملاحظات حسب الفئة
- واجهة إدارية لإضافة وتعديل الملاحظات

### 💡 التلميحات والإرشادات - Tips & Guidelines **جديد!**
- مجموعة شاملة من التلميحات والإرشادات
- تصنيف التلميحات حسب المجال (إدارة الموظفين، النماذج، الإجازات، إلخ)
- أنواع مختلفة من التلميحات (نصيحة، إرشاد، أفضل الممارسات، تحذير)
- ترتيب عرض التلميحات حسب الأولوية
- واجهة ملونة حسب نوع التلميح مع أيقونات مميزة
- عناصر عمل قابلة للتنفيذ
- إضافة تلميحات جديدة للمستخدمين المخولين

### 📊 الإنجازات الشهرية - Monthly Achievements
- تتبع المهام المكتملة والمعلقة
- تقييم الأداء الشهري
- خطط الشهر القادم والتحديات

### 📈 التقارير والإحصائيات - Reports & Statistics
- تقارير شاملة لجميع الأقسام
- إحصائيات تفاعلية
- تصدير البيانات

## التقنيات المستخدمة - Technologies Used
- **.NET 6.0** Windows Forms
- **SQL Server LocalDB**
- **System.Data.SqlClient**
- **System.Configuration.ConfigurationManager**
- **C# 10** with modern features

## قاعدة البيانات - Database Schema

### الجداول الرئيسية - Main Tables
1. **Employees** - بيانات الموظفين
2. **Forms** - النماذج
3. **WorkInjuryFiles** - ملفات إصابات العمل
4. **UnpaidLeaveFiles** - ملفات الإجازات بدون مرتب
5. **CityCouncilDelegates** - المنتدبين
6. **Notifications** - التنبيهات
7. **Notes** - الملاحظات
8. **Tips** - التلميحات والإرشادات
9. **MonthlyAchievements** - الإنجازات الشهرية

## التثبيت والإعداد - Installation & Setup

### المتطلبات - Prerequisites
- .NET 6.0 SDK أو أحدث
- SQL Server LocalDB
- Visual Studio 2022 (مستحسن)
- Windows 10/11

### خطوات التثبيت - Installation Steps
1. استنساخ المشروع
2. تشغيل سكريبت قاعدة البيانات من `Database/CreateTables.sql`
3. تحديث سلسلة الاتصال في `App.config` إذا لزم الأمر
4. بناء وتشغيل التطبيق:
   ```bash
   dotnet build
   dotnet run
   ```

## ما الجديد في الإصدار 3.1.0 - What's New in Version 3.1.0

### 🔧 إصلاحات الاستقرار والأداء - Stability & Performance Fixes
- **إصلاح مشكلة قاعدة البيانات**: حل مشكلة `attachdbfilename` وتوحيد سلاسل الاتصال
- **معالجة شاملة للأخطاء**: إضافة try-catch في جميع العمليات الحرجة
- **وضع البيانات التجريبية**: التطبيق يعمل حتى مع فشل قاعدة البيانات
- **تحسين الاستقرار**: لا توجد crashes أو أخطاء تشغيل
- **رسائل تحذيرية**: عرض تحذيرات واضحة بدلاً من إيقاف التطبيق
- **إصلاح Nullable Reference Types**: حل جميع تحذيرات CS8600 و CS8625
- **بناء نظيف**: 0 أخطاء، 3 تحذيرات minor فقط

## ما الجديد في الإصدار 3.0.0 - What's New in Version 3.0.0

### ✅ الميزات المضافة - Added Features
- **نظام التنبيهات الشامل** مع إدارة كاملة للتنبيهات
- **نظام الملاحظات المتقدم** مع وصف محتوى الملفات وعناصر العمل
- **نظام التلميحات والإرشادات** مع تصنيفات متعددة
- **واجهات إدارية** للمستخدمين المخولين
- **فئات إدارة البيانات** منفصلة (DatabaseManager, NotificationManager, NoteManager, TipManager)

### 🔧 التحسينات - Improvements
- تحسين الأداء وسرعة الاستجابة
- واجهات مستخدم محسنة مع ألوان مميزة
- إدارة أفضل للأخطاء والاستثناءات
- دعم البيانات التجريبية في حالة عدم توفر قاعدة البيانات

### 🐛 إصلاح الأخطاء - Bug Fixes
- إصلاح مشاكل الاتصال بقاعدة البيانات
- تحسين إدارة الذاكرة
- إصلاح مشاكل العرض في الواجهات

## الاستخدام - Usage

### الواجهة الرئيسية
- **شريط علوي** مع معلومات النظام والمطور
- **شريط جانبي** للتنقل بين الأقسام
- **منطقة المحتوى** الرئيسية مع التبويبات

### الميزات المتقدمة
- واجهة ثنائية اللغة تلقائياً
- ألوان مميزة لكل نوع من البيانات
- تصفية وبحث متقدم
- إحصائيات فورية
- نماذج تفاعلية لإدارة المحتوى

### استخدام الأقسام الجديدة
#### قسم التنبيهات 🔔
1. انقر على "التنبيهات" في الشريط الجانبي
2. اعرض التنبيهات الحالية مصنفة حسب الأولوية
3. انقر "إضافة تنبيه جديد" لإنشاء تنبيه
4. املأ البيانات المطلوبة واختر النوع والأولوية

#### قسم الملاحظات 📝
1. انقر على "الملاحظات" في الشريط الجانبي
2. استخدم التصفية لعرض ملاحظات فئة معينة
3. انقر "إضافة ملاحظة" لإنشاء ملاحظة جديدة
4. أضف العلامات لتسهيل البحث لاحقاً

#### قسم التلميحات 💡
1. انقر على "التلميحات" في الشريط الجانبي
2. تصفح التلميحات حسب المجال
3. انقر "إضافة تلميح" لإضافة إرشادات جديدة
4. حدد نوع التلميح وترتيب العرض

## الأمان والصلاحيات - Security & Permissions
- إدارة المستخدمين والصلاحيات
- تسجيل العمليات والتغييرات
- حماية البيانات الحساسة

## الدعم والتطوير - Support & Development
للدعم الفني أو الاستفسارات:
- **البريد الإلكتروني**: <EMAIL>
- **المطور**: Ahmed Ibrahim

## الترخيص - License
هذا المشروع مطور بواسطة Ahmed Ibrahim. جميع الحقوق محفوظة.

---
**تم التطوير بعناية لتوفير أفضل تجربة في إدارة الموارد البشرية** 🚀
