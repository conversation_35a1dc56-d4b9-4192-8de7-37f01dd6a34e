using System;
using System.Drawing;
using System.Windows.Forms;
using System.Collections.Generic;
using System.IO;

namespace StandaloneHR
{
    public class Employee
    {
        public string Name { get; set; } = "";
        public string NationalId { get; set; } = "";
        public string JobTitle { get; set; } = "";
        public string Department { get; set; } = "";
        public DateTime HireDate { get; set; }
        public string Phone { get; set; } = "";
        public string Email { get; set; } = "";
        public decimal Salary { get; set; }
    }

    public class HRManagementSystem : Form
    {
        private List<Employee> employees;
        private string dataFile = "hr_employees.txt";

        public HRManagementSystem()
        {
            InitializeComponent();
            LoadEmployees();
        }

        private void InitializeComponent()
        {
            this.Text = "نظام إدارة الموارد البشرية - HR Management System v5.3.0";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Icon = SystemIcons.Application;
            this.MinimumSize = new Size(800, 600);

            CreateInterface();
        }

        private void CreateInterface()
        {
            // العنوان الرئيسي
            var titleLabel = new Label
            {
                Text = "نظام إدارة الموارد البشرية\nHR Management System v5.3.0",
                Font = new Font("Arial", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Size = new Size(900, 80),
                Location = new Point(50, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // الأزرار الرئيسية
            var btnEmployees = CreateButton("👥 عرض الموظفين\nView Employees", 50, 120, ShowEmployeesList);
            var btnAdd = CreateButton("➕ إضافة موظف\nAdd Employee", 250, 120, ShowAddEmployeeForm);
            var btnSearch = CreateButton("🔍 البحث\nSearch", 450, 120, ShowSearchForm);
            var btnReports = CreateButton("📊 التقارير\nReports", 650, 120, ShowReportsMenu);

            var btnEdit = CreateButton("✏️ تعديل موظف\nEdit Employee", 50, 220, ShowEditEmployeeForm);
            var btnDelete = CreateButton("🗑️ حذف موظف\nDelete Employee", 250, 220, ShowDeleteEmployeeForm);
            var btnBackup = CreateButton("💾 نسخة احتياطية\nBackup", 450, 220, CreateBackup);
            var btnSettings = CreateButton("⚙️ الإعدادات\nSettings", 650, 220, ShowSettings);

            var btnImport = CreateButton("📥 استيراد\nImport Data", 50, 320, ImportData);
            var btnExport = CreateButton("📤 تصدير\nExport Data", 250, 320, ExportData);
            var btnHelp = CreateButton("❓ المساعدة\nHelp", 450, 320, ShowHelp);
            var btnExit = CreateButton("🚪 خروج\nExit", 650, 320, ExitApplication);

            // معلومات النظام
            var infoLabel = new Label
            {
                Text = $"عدد الموظفين: {employees?.Count ?? 0} | آخر تحديث: {DateTime.Now:yyyy-MM-dd HH:mm}",
                Font = new Font("Arial", 12),
                ForeColor = Color.Green,
                Size = new Size(900, 30),
                Location = new Point(50, 420),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var statusLabel = new Label
            {
                Text = "النظام جاهز للاستخدام ✅ | System Ready",
                Font = new Font("Arial", 10),
                ForeColor = Color.Gray,
                Size = new Size(900, 25),
                Location = new Point(50, 460),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // إضافة جميع العناصر
            this.Controls.AddRange(new Control[] {
                titleLabel, btnEmployees, btnAdd, btnSearch, btnReports,
                btnEdit, btnDelete, btnBackup, btnSettings,
                btnImport, btnExport, btnHelp, btnExit,
                infoLabel, statusLabel
            });
        }

        private Button CreateButton(string text, int x, int y, EventHandler clickHandler)
        {
            var button = new Button
            {
                Text = text,
                Size = new Size(180, 80),
                Location = new Point(x, y),
                Font = new Font("Arial", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                UseVisualStyleBackColor = false
            };

            button.Click += clickHandler;
            return button;
        }

        private void LoadEmployees()
        {
            employees = new List<Employee>();

            if (File.Exists(dataFile))
            {
                try
                {
                    var lines = File.ReadAllLines(dataFile);
                    foreach (var line in lines)
                    {
                        var parts = line.Split('|');
                        if (parts.Length >= 8)
                        {
                            employees.Add(new Employee
                            {
                                Name = parts[0],
                                NationalId = parts[1],
                                JobTitle = parts[2],
                                Department = parts[3],
                                HireDate = DateTime.Parse(parts[4]),
                                Phone = parts[5],
                                Email = parts[6],
                                Salary = decimal.Parse(parts[7])
                            });
                        }
                    }
                }
                catch
                {
                    CreateSampleData();
                }
            }
            else
            {
                CreateSampleData();
            }
        }

        private void CreateSampleData()
        {
            employees.Clear();
            employees.Add(new Employee { Name = "أحمد محمد علي", NationalId = "12345678901", JobTitle = "مدير الموارد البشرية", Department = "الموارد البشرية", HireDate = DateTime.Now.AddYears(-2), Phone = "01234567890", Email = "<EMAIL>", Salary = 15000 });
            employees.Add(new Employee { Name = "فاطمة أحمد حسن", NationalId = "12345678902", JobTitle = "محاسب أول", Department = "المحاسبة", HireDate = DateTime.Now.AddYears(-1), Phone = "01234567891", Email = "<EMAIL>", Salary = 12000 });
            employees.Add(new Employee { Name = "محمد عبدالله سالم", NationalId = "12345678903", JobTitle = "مطور برمجيات", Department = "تقنية المعلومات", HireDate = DateTime.Now.AddMonths(-6), Phone = "01234567892", Email = "<EMAIL>", Salary = 18000 });
            employees.Add(new Employee { Name = "سارة محمود عبدالرحمن", NationalId = "12345678904", JobTitle = "مساعد إداري", Department = "الإدارة", HireDate = DateTime.Now.AddMonths(-3), Phone = "01234567893", Email = "<EMAIL>", Salary = 8000 });
            employees.Add(new Employee { Name = "خالد أحمد محمد", NationalId = "12345678905", JobTitle = "مهندس شبكات", Department = "تقنية المعلومات", HireDate = DateTime.Now.AddMonths(-8), Phone = "01234567894", Email = "<EMAIL>", Salary = 16000 });
            SaveEmployees();
        }

        private void SaveEmployees()
        {
            try
            {
                var lines = new List<string>();
                foreach (var emp in employees)
                {
                    lines.Add($"{emp.Name}|{emp.NationalId}|{emp.JobTitle}|{emp.Department}|{emp.HireDate:yyyy-MM-dd}|{emp.Phone}|{emp.Email}|{emp.Salary}");
                }
                File.WriteAllLines(dataFile, lines);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void ShowEmployeesList(object? sender, EventArgs e)
        {
            var form = new Form
            {
                Text = "قائمة الموظفين - Employees List",
                Size = new Size(1000, 600),
                StartPosition = FormStartPosition.CenterParent,
                MinimumSize = new Size(800, 500)
            };

            var listView = new ListView
            {
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Dock = DockStyle.Fill,
                Font = new Font("Arial", 10)
            };

            listView.Columns.Add("الاسم", 150);
            listView.Columns.Add("الرقم القومي", 120);
            listView.Columns.Add("المنصب", 150);
            listView.Columns.Add("القسم", 120);
            listView.Columns.Add("تاريخ التعيين", 100);
            listView.Columns.Add("الهاتف", 100);
            listView.Columns.Add("البريد الإلكتروني", 150);
            listView.Columns.Add("الراتب", 80);

            foreach (var emp in employees)
            {
                var item = new ListViewItem(emp.Name);
                item.SubItems.Add(emp.NationalId);
                item.SubItems.Add(emp.JobTitle);
                item.SubItems.Add(emp.Department);
                item.SubItems.Add(emp.HireDate.ToString("yyyy-MM-dd"));
                item.SubItems.Add(emp.Phone);
                item.SubItems.Add(emp.Email);
                item.SubItems.Add(emp.Salary.ToString("N0"));
                listView.Items.Add(item);
            }

            form.Controls.Add(listView);
            form.ShowDialog();
        }

        private void ShowAddEmployeeForm(object? sender, EventArgs e)
        {
            var form = new Form
            {
                Text = "إضافة موظف جديد - Add New Employee",
                Size = new Size(600, 500),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false
            };

            var y = 20;
            var spacing = 40;

            // الاسم
            form.Controls.Add(new Label { Text = "الاسم الكامل:", Location = new Point(20, y), Size = new Size(100, 25) });
            var nameText = new TextBox { Location = new Point(130, y), Size = new Size(400, 25) };
            form.Controls.Add(nameText);
            y += spacing;

            // الرقم القومي
            form.Controls.Add(new Label { Text = "الرقم القومي:", Location = new Point(20, y), Size = new Size(100, 25) });
            var idText = new TextBox { Location = new Point(130, y), Size = new Size(400, 25) };
            form.Controls.Add(idText);
            y += spacing;

            // المنصب
            form.Controls.Add(new Label { Text = "المنصب الوظيفي:", Location = new Point(20, y), Size = new Size(100, 25) });
            var jobText = new TextBox { Location = new Point(130, y), Size = new Size(400, 25) };
            form.Controls.Add(jobText);
            y += spacing;

            // القسم
            form.Controls.Add(new Label { Text = "القسم:", Location = new Point(20, y), Size = new Size(100, 25) });
            var deptCombo = new ComboBox { Location = new Point(130, y), Size = new Size(400, 25), DropDownStyle = ComboBoxStyle.DropDownList };
            deptCombo.Items.AddRange(new[] { "الموارد البشرية", "المحاسبة", "تقنية المعلومات", "الإدارة", "المبيعات", "التسويق", "الإنتاج", "الجودة" });
            form.Controls.Add(deptCombo);
            y += spacing;

            // الهاتف
            form.Controls.Add(new Label { Text = "رقم الهاتف:", Location = new Point(20, y), Size = new Size(100, 25) });
            var phoneText = new TextBox { Location = new Point(130, y), Size = new Size(400, 25) };
            form.Controls.Add(phoneText);
            y += spacing;

            // البريد الإلكتروني
            form.Controls.Add(new Label { Text = "البريد الإلكتروني:", Location = new Point(20, y), Size = new Size(100, 25) });
            var emailText = new TextBox { Location = new Point(130, y), Size = new Size(400, 25) };
            form.Controls.Add(emailText);
            y += spacing;

            // الراتب
            form.Controls.Add(new Label { Text = "الراتب الأساسي:", Location = new Point(20, y), Size = new Size(100, 25) });
            var salaryText = new NumericUpDown { Location = new Point(130, y), Size = new Size(400, 25), Maximum = 1000000, Minimum = 1000, Value = 5000 };
            form.Controls.Add(salaryText);
            y += spacing;

            // الأزرار
            var saveBtn = new Button
            {
                Text = "حفظ",
                Location = new Point(130, y + 20),
                Size = new Size(100, 35),
                BackColor = Color.Green,
                ForeColor = Color.White,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };

            var cancelBtn = new Button
            {
                Text = "إلغاء",
                Location = new Point(250, y + 20),
                Size = new Size(100, 35),
                BackColor = Color.Gray,
                ForeColor = Color.White,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };

            saveBtn.Click += (s, ev) => {
                if (string.IsNullOrWhiteSpace(nameText.Text) || string.IsNullOrWhiteSpace(idText.Text))
                {
                    MessageBox.Show("يرجى ملء الحقول المطلوبة (الاسم والرقم القومي)", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // التحقق من عدم تكرار الرقم القومي
                if (employees.Any(emp => emp.NationalId == idText.Text))
                {
                    MessageBox.Show("الرقم القومي موجود مسبقاً", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                employees.Add(new Employee
                {
                    Name = nameText.Text,
                    NationalId = idText.Text,
                    JobTitle = jobText.Text,
                    Department = deptCombo.Text,
                    Phone = phoneText.Text,
                    Email = emailText.Text,
                    Salary = salaryText.Value,
                    HireDate = DateTime.Now
                });

                SaveEmployees();
                MessageBox.Show("تم إضافة الموظف بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                form.Close();
                RefreshMainForm();
            };

            cancelBtn.Click += (s, ev) => form.Close();

            form.Controls.AddRange(new Control[] { saveBtn, cancelBtn });
            form.ShowDialog();
        }

        private void ShowSearchForm(object? sender, EventArgs e)
        {
            var searchText = ShowInputDialog("ادخل كلمة البحث (الاسم، الرقم القومي، المنصب، القسم):", "البحث في الموظفين");
            if (string.IsNullOrWhiteSpace(searchText)) return;

            var results = employees.Where(e =>
                e.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                e.NationalId.Contains(searchText) ||
                e.JobTitle.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                e.Department.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                e.Phone.Contains(searchText) ||
                e.Email.Contains(searchText, StringComparison.OrdinalIgnoreCase)).ToList();

            if (results.Count == 0)
            {
                MessageBox.Show("لم يتم العثور على نتائج", "البحث", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            ShowSearchResults(results, searchText);
        }

        private void ShowSearchResults(List<Employee> results, string searchTerm)
        {
            var form = new Form
            {
                Text = $"نتائج البحث عن: {searchTerm}",
                Size = new Size(1000, 500),
                StartPosition = FormStartPosition.CenterParent
            };

            var listView = new ListView
            {
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Dock = DockStyle.Fill,
                Font = new Font("Arial", 10)
            };

            listView.Columns.Add("الاسم", 150);
            listView.Columns.Add("الرقم القومي", 120);
            listView.Columns.Add("المنصب", 150);
            listView.Columns.Add("القسم", 120);
            listView.Columns.Add("الهاتف", 100);
            listView.Columns.Add("البريد الإلكتروني", 150);
            listView.Columns.Add("الراتب", 80);

            foreach (var emp in results)
            {
                var item = new ListViewItem(emp.Name);
                item.SubItems.Add(emp.NationalId);
                item.SubItems.Add(emp.JobTitle);
                item.SubItems.Add(emp.Department);
                item.SubItems.Add(emp.Phone);
                item.SubItems.Add(emp.Email);
                item.SubItems.Add(emp.Salary.ToString("N0"));
                listView.Items.Add(item);
            }

            form.Controls.Add(listView);
            form.ShowDialog();
        }

        private void ShowReportsMenu(object? sender, EventArgs e)
        {
            var message = $"📊 تقرير الموظفين الشامل\n" +
                         $"================================\n\n" +
                         $"📈 إجمالي الموظفين: {employees.Count}\n\n";

            // توزيع حسب الأقسام
            var departments = employees.GroupBy(e => e.Department)
                                    .ToDictionary(g => g.Key, g => g.Count());

            message += "📋 توزيع الموظفين حسب الأقسام:\n";
            foreach (var dept in departments.OrderByDescending(d => d.Value))
            {
                message += $"• {dept.Key}: {dept.Value} موظف\n";
            }

            // إحصائيات الرواتب
            message += $"\n💰 إحصائيات الرواتب:\n";
            message += $"• متوسط الراتب: {employees.Average(e => e.Salary):N0} جنيه\n";
            message += $"• أعلى راتب: {employees.Max(e => e.Salary):N0} جنيه\n";
            message += $"• أقل راتب: {employees.Min(e => e.Salary):N0} جنيه\n";
            message += $"• إجمالي الرواتب: {employees.Sum(e => e.Salary):N0} جنيه\n";

            // إحصائيات التوظيف
            var thisYear = employees.Count(e => e.HireDate.Year == DateTime.Now.Year);
            var lastYear = employees.Count(e => e.HireDate.Year == DateTime.Now.Year - 1);

            message += $"\n📅 إحصائيات التوظيف:\n";
            message += $"• موظفين جدد هذا العام: {thisYear}\n";
            message += $"• موظفين العام الماضي: {lastYear}\n";

            MessageBox.Show(message, "تقرير الموظفين الشامل", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowEditEmployeeForm(object? sender, EventArgs e)
        {
            var searchText = ShowInputDialog("ادخل الرقم القومي للموظف المراد تعديله:", "تعديل موظف");
            if (string.IsNullOrWhiteSpace(searchText)) return;

            var employee = employees.FirstOrDefault(emp => emp.NationalId == searchText);
            if (employee == null)
            {
                MessageBox.Show("لم يتم العثور على الموظف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // نموذج التعديل (مشابه لنموذج الإضافة ولكن مع البيانات المحملة)
            MessageBox.Show($"تعديل بيانات الموظف: {employee.Name}\n(هذه الوظيفة قيد التطوير)", "تعديل موظف", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowDeleteEmployeeForm(object? sender, EventArgs e)
        {
            var searchText = ShowInputDialog("ادخل الرقم القومي للموظف المراد حذفه:", "حذف موظف");
            if (string.IsNullOrWhiteSpace(searchText)) return;

            var employee = employees.FirstOrDefault(emp => emp.NationalId == searchText);
            if (employee == null)
            {
                MessageBox.Show("لم يتم العثور على الموظف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show($"هل أنت متأكد من حذف الموظف:\n{employee.Name}\n{employee.JobTitle}؟",
                                       "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                employees.Remove(employee);
                SaveEmployees();
                MessageBox.Show("تم حذف الموظف بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                RefreshMainForm();
            }
        }

        private void CreateBackup(object? sender, EventArgs e)
        {
            try
            {
                var backupFile = $"backup_hr_employees_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                File.Copy(dataFile, backupFile, true);
                MessageBox.Show($"تم إنشاء نسخة احتياطية بنجاح!\n\nاسم الملف: {backupFile}\nعدد الموظفين: {employees.Count}",
                               "نسخة احتياطية", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowSettings(object? sender, EventArgs e)
        {
            var message = "⚙️ إعدادات النظام\n" +
                         "==================\n\n" +
                         $"📁 ملف البيانات: {dataFile}\n" +
                         $"👥 عدد الموظفين: {employees.Count}\n" +
                         $"📅 آخر تحديث: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n" +
                         $"💾 حجم الملف: {(File.Exists(dataFile) ? new FileInfo(dataFile).Length : 0)} بايت\n\n" +
                         "🔧 إعدادات متقدمة:\n" +
                         "• تشفير البيانات: غير مفعل\n" +
                         "• النسخ الاحتياطي التلقائي: غير مفعل\n" +
                         "• تسجيل العمليات: غير مفعل";

            MessageBox.Show(message, "إعدادات النظام", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ImportData(object? sender, EventArgs e)
        {
            MessageBox.Show("وظيفة استيراد البيانات\n\nيمكن استيراد البيانات من:\n• ملفات Excel\n• ملفات CSV\n• قواعد بيانات أخرى\n\n(هذه الوظيفة قيد التطوير)",
                           "استيراد البيانات", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ExportData(object? sender, EventArgs e)
        {
            try
            {
                var exportFile = $"export_employees_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                var csvLines = new List<string>();
                csvLines.Add("الاسم,الرقم القومي,المنصب,القسم,تاريخ التعيين,الهاتف,البريد الإلكتروني,الراتب");

                foreach (var emp in employees)
                {
                    csvLines.Add($"{emp.Name},{emp.NationalId},{emp.JobTitle},{emp.Department},{emp.HireDate:yyyy-MM-dd},{emp.Phone},{emp.Email},{emp.Salary}");
                }

                File.WriteAllLines(exportFile, csvLines);
                MessageBox.Show($"تم تصدير البيانات بنجاح!\n\nاسم الملف: {exportFile}\nعدد الموظفين: {employees.Count}",
                               "تصدير البيانات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowHelp(object? sender, EventArgs e)
        {
            var message = "📖 دليل استخدام نظام إدارة الموارد البشرية\n" +
                         "=======================================\n\n" +
                         "🎯 الوظائف الرئيسية:\n" +
                         "👥 عرض الموظفين - عرض قائمة جميع الموظفين\n" +
                         "➕ إضافة موظف - إضافة موظف جديد للنظام\n" +
                         "🔍 البحث - البحث في بيانات الموظفين\n" +
                         "📊 التقارير - عرض تقارير وإحصائيات شاملة\n" +
                         "✏️ تعديل موظف - تعديل بيانات موظف موجود\n" +
                         "🗑️ حذف موظف - حذف موظف من النظام\n" +
                         "💾 نسخة احتياطية - حفظ نسخة من البيانات\n" +
                         "📥 استيراد - استيراد بيانات من ملفات خارجية\n" +
                         "📤 تصدير - تصدير البيانات لملف CSV\n\n" +
                         "💡 نصائح:\n" +
                         "• استخدم الرقم القومي للبحث والتعديل\n" +
                         "• قم بعمل نسخة احتياطية دورياً\n" +
                         "• تأكد من صحة البيانات قبل الحفظ\n\n" +
                         "📞 للدعم التقني: <EMAIL>\n" +
                         "🌐 الموقع الإلكتروني: www.company.com\n\n" +
                         "© 2024 جميع الحقوق محفوظة";

            MessageBox.Show(message, "دليل الاستخدام", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ExitApplication(object? sender, EventArgs e)
        {
            var result = MessageBox.Show("هل تريد الخروج من نظام إدارة الموارد البشرية؟",
                                       "تأكيد الخروج", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void RefreshMainForm()
        {
            // تحديث عداد الموظفين في النافذة الرئيسية
            foreach (Control control in this.Controls)
            {
                if (control is Label label && label.Text.Contains("عدد الموظفين"))
                {
                    label.Text = $"عدد الموظفين: {employees.Count} | آخر تحديث: {DateTime.Now:yyyy-MM-dd HH:mm}";
                    break;
                }
            }
        }

        private string ShowInputDialog(string prompt, string title)
        {
            var form = new Form
            {
                Text = title,
                Size = new Size(500, 180),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var label = new Label
            {
                Text = prompt,
                Location = new Point(20, 20),
                Size = new Size(450, 40),
                Font = new Font("Arial", 10)
            };

            var textBox = new TextBox
            {
                Location = new Point(20, 70),
                Size = new Size(450, 25),
                Font = new Font("Arial", 10)
            };

            var okButton = new Button
            {
                Text = "موافق",
                Location = new Point(300, 110),
                Size = new Size(80, 30),
                DialogResult = DialogResult.OK,
                BackColor = Color.Green,
                ForeColor = Color.White
            };

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(390, 110),
                Size = new Size(80, 30),
                DialogResult = DialogResult.Cancel,
                BackColor = Color.Gray,
                ForeColor = Color.White
            };

            form.Controls.AddRange(new Control[] { label, textBox, okButton, cancelButton });
            form.AcceptButton = okButton;
            form.CancelButton = cancelButton;

            return form.ShowDialog() == DialogResult.OK ? textBox.Text : "";
        }
    }

    class Program
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // رسالة ترحيب
                MessageBox.Show(
                    "🎉 مرحباً بك في نظام إدارة الموارد البشرية\n\n" +
                    "Welcome to HR Management System v5.3.0\n\n" +
                    "✅ النظام جاهز للاستخدام!\n" +
                    "✅ System is ready to use!\n\n" +
                    "المزايا المتاحة:\n" +
                    "• إدارة شاملة للموظفين\n" +
                    "• تقارير وإحصائيات متقدمة\n" +
                    "• بحث سريع ومتقدم\n" +
                    "• نسخ احتياطية آمنة\n" +
                    "• واجهة سهلة الاستخدام",
                    "نظام إدارة الموارد البشرية",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                Application.Run(new HRManagementSystem());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل النظام:\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}",
                              "خطأ في النظام", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
