using System;
using System.Drawing;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    public class AddUserForm : Form
    {
        private TextBox txtUsername;
        private TextBox txtPassword;
        private ComboBox cmbRole;
        private Button btnSave;
        private Button btnCancel;

        public AddUserForm()
        {
            InitializeComponents();
        }

        private void InitializeComponents()
        {
            // إعدادات النافذة
            this.Text = "إضافة مستخدم جديد";
            this.Size = new Size(450, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 248, 255);

            // عنوان النافذة
            Label titleLabel = new Label
            {
                Text = "➕ إضافة مستخدم جديد",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Size = new Size(400, 40),
                Location = new Point(25, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // تسمية اسم المستخدم
            Label lblUsername = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Size = new Size(120, 25),
                Location = new Point(300, 80),
                TextAlign = ContentAlignment.MiddleRight
            };

            // حقل اسم المستخدم
            txtUsername = new TextBox
            {
                Font = new Font("Tahoma", 12),
                Size = new Size(250, 30),
                Location = new Point(40, 80),
                TextAlign = HorizontalAlignment.Center
            };

            // تسمية كلمة المرور
            Label lblPassword = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Size = new Size(120, 25),
                Location = new Point(300, 130),
                TextAlign = ContentAlignment.MiddleRight
            };

            // حقل كلمة المرور
            txtPassword = new TextBox
            {
                Font = new Font("Tahoma", 12),
                Size = new Size(250, 30),
                Location = new Point(40, 130),
                TextAlign = HorizontalAlignment.Center,
                PasswordChar = '*'
            };

            // تسمية الصلاحية
            Label lblRole = new Label
            {
                Text = "الصلاحية:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Size = new Size(120, 25),
                Location = new Point(300, 180),
                TextAlign = ContentAlignment.MiddleRight
            };

            // قائمة الصلاحيات
            cmbRole = new ComboBox
            {
                Font = new Font("Tahoma", 12),
                Size = new Size(250, 30),
                Location = new Point(40, 180),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbRole.Items.AddRange(new string[] { "Admin", "User" });
            cmbRole.SelectedIndex = 1; // User كافتراضي

            // زر الحفظ
            btnSave = new Button
            {
                Text = "💾 حفظ",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(120, 40),
                Location = new Point(200, 240),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            // زر الإلغاء
            btnCancel = new Button
            {
                Text = "❌ إلغاء",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(120, 40),
                Location = new Point(70, 240),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            // ربط الأحداث
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;

            // إضافة العناصر للنافذة
            this.Controls.AddRange(new Control[] {
                titleLabel, lblUsername, txtUsername, lblPassword, txtPassword, 
                lblRole, cmbRole, btnSave, btnCancel
            });

            // تركيز على حقل اسم المستخدم
            txtUsername.Focus();
        }

        private void BtnSave_Click(object? sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    MessageBox.Show("⚠️ يرجى إدخال اسم المستخدم", 
                        "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    MessageBox.Show("⚠️ يرجى إدخال كلمة المرور", 
                        "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return;
                }

                if (txtPassword.Text.Length < 3)
                {
                    MessageBox.Show("⚠️ كلمة المرور يجب أن تكون 3 أحرف على الأقل", 
                        "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return;
                }

                if (cmbRole.SelectedItem == null)
                {
                    MessageBox.Show("⚠️ يرجى اختيار الصلاحية", 
                        "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cmbRole.Focus();
                    return;
                }

                string username = txtUsername.Text.Trim();
                string password = txtPassword.Text;
                string role = cmbRole.SelectedItem.ToString()!;

                // التحقق من عدم وجود المستخدم مسبقاً
                if (DatabaseHelper.UserExists(username))
                {
                    MessageBox.Show($"❌ المستخدم '{username}' موجود بالفعل!\nيرجى اختيار اسم مستخدم آخر.", 
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    txtUsername.Focus();
                    txtUsername.SelectAll();
                    return;
                }

                // إضافة المستخدم
                DatabaseHelper.AddUser(username, password, role);

                MessageBox.Show($"✅ تم إضافة المستخدم '{username}' بنجاح!\n\nالصلاحية: {role}", 
                    "نجح الإضافة", MessageBoxButtons.OK, MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إضافة المستخدم:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
