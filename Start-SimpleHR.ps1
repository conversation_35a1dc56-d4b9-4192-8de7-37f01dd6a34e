Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Simple HR Management System" -ForegroundColor Green
Write-Host "    نظام إدارة الموارد البشرية المبسط" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Set-Location $PSScriptRoot

try {
    Write-Host "Building simple HR application..." -ForegroundColor Yellow
    
    $buildResult = dotnet build SimpleHR.csproj 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Build successful!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Starting application..." -ForegroundColor Yellow
        Write-Host ""
        Write-Host "LOOK FOR THESE WINDOWS:" -ForegroundColor Magenta
        Write-Host "1. Welcome message box (click OK)" -ForegroundColor White
        Write-Host "2. Main HR system window" -ForegroundColor White
        Write-Host ""
        
        dotnet run --project SimpleHR.csproj
    } else {
        Write-Host "❌ Build failed. Trying alternative method..." -ForegroundColor Red
        Write-Host ""
        
        # Try direct compilation
        $cscPath = Get-Command csc -ErrorAction SilentlyContinue
        if ($cscPath) {
            Write-Host "Using C# compiler..." -ForegroundColor Yellow
            csc /target:winexe /reference:System.Windows.Forms.dll /reference:System.Drawing.dll SimpleHR.cs
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Compilation successful!" -ForegroundColor Green
                Write-Host "Starting application..." -ForegroundColor Yellow
                .\SimpleHR.exe
                Remove-Item SimpleHR.exe -ErrorAction SilentlyContinue
            } else {
                Write-Host "❌ Compilation failed" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ No C# compiler found" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Application finished." -ForegroundColor Gray
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
