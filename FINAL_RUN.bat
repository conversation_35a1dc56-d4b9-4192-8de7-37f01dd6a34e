@echo off
title HR Management System v5.3.0 - Final Run
color 0A

echo.
echo ========================================
echo    HR Management System v5.3.0
echo    Final Run - All Errors Fixed
echo ========================================
echo.

cd /d "%~dp0"

echo All errors have been fixed:
echo ✅ NotificationManager duplicate class - FIXED
echo ✅ Timer ambiguous reference - FIXED
echo ✅ Font ambiguous reference - FIXED
echo ✅ COM Reference (ADOX) - REMOVED
echo ✅ NotificationManager methods - FIXED
echo ✅ DateTime nullable operator - FIXED
echo ✅ DatabaseHelper.GetAllEmployees - FIXED
echo ✅ AddEmployeeForm reference - FIXED
echo ✅ PDFReportGenerator warning - FIXED
echo.

echo Starting build and run process...
echo.

echo [1/3] Cleaning...
dotnet clean >nul 2>&1

echo [2/3] Building...
dotnet build

if errorlevel 1 (
    echo.
    echo ❌ BUILD FAILED - Please check errors above
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ BUILD SUCCESSFUL!
echo.

echo [3/3] Starting HR Management System...
echo.
echo Login credentials:
echo   Username: admin
echo   Password: 123456
echo.

dotnet run

echo.
echo Application closed.
echo.
echo Press any key to exit...
pause >nul
