@echo off
title HR Management System - Emergency Run
echo.
echo ========================================
echo    HR Management System v5.3.0
echo    Emergency Run - Direct Execution
echo ========================================
echo.

cd /d "%~dp0"

echo Current directory: %CD%
echo.

echo Checking .NET version:
dotnet --version
echo.

echo Cleaning project:
dotnet clean
echo.

echo Restoring packages:
dotnet restore
echo.

echo Building project:
dotnet build --configuration Release
echo.

if errorlevel 1 (
    echo Build failed. Trying Debug configuration...
    dotnet build --configuration Debug
)

echo.
echo Starting application:
dotnet run --configuration Release

if errorlevel 1 (
    echo Release failed. Trying Debug...
    dotnet run --configuration Debug
)

echo.
echo Application finished.
pause
