@echo off
title Clean HR Management System v5.3.0
color 0A

echo.
echo ========================================
echo    Clean HR Management System v5.3.0
echo    نظام إدارة الموارد البشرية النظيف
echo    Final Clean Version - No Dependencies
echo ========================================
echo.

echo Building Clean HR Management System...
dotnet build CleanHR.csproj --verbosity quiet

if errorlevel 1 (
    echo ❌ Build failed. Check .NET installation.
    echo.
    echo Please ensure .NET 6.0 is installed:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.
echo Starting HR Management System...
echo.
echo 🎯 FEATURES AVAILABLE:
echo • 👥 Complete employee management (5 sample employees)
echo • ➕ Add employees with full validation
echo • 🔍 Advanced search functionality
echo • 📊 Comprehensive reports and statistics
echo • ✏️ Edit employee information
echo • 🗑️ Safe employee deletion with confirmation
echo • 💾 Backup and restore data
echo • 📤 Export to CSV format
echo • 📖 Complete help and documentation
echo • 🔄 Real-time data refresh
echo.
echo 📱 LOOK FOR THESE WINDOWS:
echo 1. 📱 Welcome message - Click OK to continue
echo 2. 🏠 Main HR window with 10 functional buttons
echo 3. 📋 Employee list, forms, and reports
echo.
echo 💾 DATA STORAGE:
echo • File: hr_data.txt (automatically created)
echo • Format: Text file with pipe separation
echo • Backup: Available through backup button
echo • Export: CSV format for Excel compatibility
echo.
echo 🚀 SYSTEM REQUIREMENTS:
echo • Windows 7 or later
echo • .NET 6.0 Runtime
echo • No external dependencies
echo • No database required
echo.

dotnet run --project CleanHR.csproj

echo.
echo Application finished.
pause
