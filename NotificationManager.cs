using System;
using System.Threading.Tasks;
using MailKit.Net.Smtp;
using MimeKit;
using System.IO;
using Newtonsoft.Json;

namespace Ahmedapp_for_work
{
    public class NotificationManager
    {
        private static EmailSettings? emailSettings;
        private static readonly string settingsFile = "email_settings.json";

        // إعدادات البريد الإلكتروني
        public class EmailSettings
        {
            public bool EmailNotificationsEnabled { get; set; } = false;
            public string SmtpServer { get; set; } = "smtp.gmail.com";
            public int SmtpPort { get; set; } = 587;
            public bool UseSSL { get; set; } = true;
            public string SenderEmail { get; set; } = "";
            public string SenderPassword { get; set; } = "";
            public string SenderName { get; set; } = "نظام إدارة الموارد البشرية";
            public string[] AdminEmails { get; set; } = new string[0];
            public bool SendDatabaseErrors { get; set; } = true;
            public bool SendBackupErrors { get; set; } = true;
            public bool SendSyncErrors { get; set; } = true;
            public bool SendEmployeeReports { get; set; } = true;
        }

        // تحميل إعدادات البريد الإلكتروني
        public static void LoadEmailSettings()
        {
            try
            {
                if (File.Exists(settingsFile))
                {
                    string json = File.ReadAllText(settingsFile);
                    emailSettings = JsonConvert.DeserializeObject<EmailSettings>(json);
                    System.Diagnostics.Debug.WriteLine("✅ تم تحميل إعدادات البريد الإلكتروني");
                }
                else
                {
                    // إنشاء إعدادات افتراضية
                    emailSettings = new EmailSettings();
                    SaveEmailSettings();
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء إعدادات البريد الإلكتروني الافتراضية");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل إعدادات البريد: {ex.Message}");
                emailSettings = new EmailSettings();
            }
        }

        // حفظ إعدادات البريد الإلكتروني
        public static void SaveEmailSettings()
        {
            try
            {
                if (emailSettings != null)
                {
                    string json = JsonConvert.SerializeObject(emailSettings, Formatting.Indented);
                    File.WriteAllText(settingsFile, json);
                    System.Diagnostics.Debug.WriteLine("✅ تم حفظ إعدادات البريد الإلكتروني");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ إعدادات البريد: {ex.Message}");
            }
        }

        // الحصول على إعدادات البريد الإلكتروني
        public static EmailSettings GetEmailSettings()
        {
            if (emailSettings == null)
            {
                LoadEmailSettings();
            }
            return emailSettings ?? new EmailSettings();
        }

        // تحديث إعدادات البريد الإلكتروني
        public static void UpdateEmailSettings(EmailSettings newSettings)
        {
            emailSettings = newSettings;
            SaveEmailSettings();
        }

        // إرسال إشعار خطأ
        public static async void SendErrorNotification(string title, string message, Exception? exception = null)
        {
            try
            {
                var settings = GetEmailSettings();
                if (!settings.EmailNotificationsEnabled || settings.AdminEmails.Length == 0)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ إشعارات البريد الإلكتروني معطلة أو لا توجد عناوين بريد");
                    return;
                }

                string fullMessage = $"تاريخ الخطأ: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n\n";
                fullMessage += $"العنوان: {title}\n\n";
                fullMessage += $"الرسالة: {message}\n\n";

                if (exception != null)
                {
                    fullMessage += $"تفاصيل الخطأ:\n{exception.Message}\n\n";
                    fullMessage += $"Stack Trace:\n{exception.StackTrace}\n\n";
                }

                fullMessage += "يرجى مراجعة النظام والتأكد من سلامة قاعدة البيانات.\n\n";
                fullMessage += "تم إرسال هذا الإشعار تلقائياً من نظام إدارة الموارد البشرية.";

                await SendEmail(
                    subject: $"🚨 خطأ في نظام الموارد البشرية - {title}",
                    body: fullMessage,
                    recipients: settings.AdminEmails
                );

                System.Diagnostics.Debug.WriteLine($"✅ تم إرسال إشعار خطأ: {title}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل إرسال إشعار الخطأ: {ex.Message}");
            }
        }

        // إرسال إشعار نجاح النسخ الاحتياطي
        public static async void SendBackupSuccessNotification(string backupName, string location)
        {
            try
            {
                var settings = GetEmailSettings();
                if (!settings.EmailNotificationsEnabled || !settings.SendBackupErrors)
                {
                    return;
                }

                string message = $"تم إنشاء نسخة احتياطية بنجاح:\n\n";
                message += $"اسم النسخة: {backupName}\n";
                message += $"الموقع: {location}\n";
                message += $"التاريخ والوقت: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n\n";
                message += "جميع ملفات قاعدة البيانات تم نسخها بأمان.";

                await SendEmail(
                    subject: "✅ نجح النسخ الاحتياطي - نظام الموارد البشرية",
                    body: message,
                    recipients: settings.AdminEmails
                );
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل إرسال إشعار نجاح النسخ الاحتياطي: {ex.Message}");
            }
        }

        // إرسال تقرير موظف جديد
        public static async void SendNewEmployeeNotification(Employee employee, string pdfPath)
        {
            try
            {
                var settings = GetEmailSettings();
                if (!settings.EmailNotificationsEnabled || !settings.SendEmployeeReports)
                {
                    return;
                }

                string message = $"تم إضافة موظف جديد إلى النظام:\n\n";
                message += $"الاسم: {employee.Name}\n";
                message += $"الرقم القومي: {employee.NationalId}\n";
                message += $"المسمى الوظيفي: {employee.JobTitle}\n";
                message += $"القسم: {employee.Department}\n";
                message += $"تاريخ التعيين: {employee.HireDate?.ToString("yyyy-MM-dd") ?? "غير محدد"}\n";
                message += $"تاريخ الإضافة: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n\n";
                message += "تجد مرفقاً تقرير PDF مفصل عن الموظف.";

                await SendEmailWithAttachment(
                    subject: $"👤 موظف جديد - {employee.Name}",
                    body: message,
                    recipients: settings.AdminEmails,
                    attachmentPath: pdfPath
                );

                System.Diagnostics.Debug.WriteLine($"✅ تم إرسال إشعار موظف جديد: {employee.Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل إرسال إشعار الموظف الجديد: {ex.Message}");
            }
        }

        // إرسال إشعار مزامنة قاعدة البيانات
        public static async void SendSyncNotification(bool success, string details)
        {
            try
            {
                var settings = GetEmailSettings();
                if (!settings.EmailNotificationsEnabled || !settings.SendSyncErrors)
                {
                    return;
                }

                string status = success ? "نجحت" : "فشلت";
                string icon = success ? "✅" : "❌";

                string message = $"عملية مزامنة قاعدة البيانات {status}:\n\n";
                message += $"التاريخ والوقت: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n";
                message += $"التفاصيل: {details}\n\n";

                if (!success)
                {
                    message += "يرجى مراجعة النظام والتأكد من سلامة قواعد البيانات.\n\n";
                }

                await SendEmail(
                    subject: $"{icon} مزامنة قاعدة البيانات - {status}",
                    body: message,
                    recipients: settings.AdminEmails
                );
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل إرسال إشعار المزامنة: {ex.Message}");
            }
        }

        // إرسال بريد إلكتروني
        private static async Task SendEmail(string subject, string body, string[] recipients)
        {
            var settings = GetEmailSettings();
            if (string.IsNullOrEmpty(settings.SenderEmail) || string.IsNullOrEmpty(settings.SenderPassword))
            {
                System.Diagnostics.Debug.WriteLine("⚠️ إعدادات البريد الإلكتروني غير مكتملة");
                return;
            }

            try
            {
                var message = new MimeMessage();
                message.From.Add(new MailboxAddress(settings.SenderName, settings.SenderEmail));

                foreach (var recipient in recipients)
                {
                    if (!string.IsNullOrEmpty(recipient))
                    {
                        message.To.Add(new MailboxAddress("", recipient));
                    }
                }

                message.Subject = subject;
                message.Body = new TextPart("plain") { Text = body };

                using var client = new SmtpClient();
                await client.ConnectAsync(settings.SmtpServer, settings.SmtpPort, settings.UseSSL);
                await client.AuthenticateAsync(settings.SenderEmail, settings.SenderPassword);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);

                System.Diagnostics.Debug.WriteLine($"✅ تم إرسال البريد الإلكتروني: {subject}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل إرسال البريد الإلكتروني: {ex.Message}");
                throw;
            }
        }

        // إرسال بريد إلكتروني مع مرفق
        private static async Task SendEmailWithAttachment(string subject, string body, string[] recipients, string attachmentPath)
        {
            var settings = GetEmailSettings();
            if (string.IsNullOrEmpty(settings.SenderEmail) || string.IsNullOrEmpty(settings.SenderPassword))
            {
                System.Diagnostics.Debug.WriteLine("⚠️ إعدادات البريد الإلكتروني غير مكتملة");
                return;
            }

            try
            {
                var message = new MimeMessage();
                message.From.Add(new MailboxAddress(settings.SenderName, settings.SenderEmail));

                foreach (var recipient in recipients)
                {
                    if (!string.IsNullOrEmpty(recipient))
                    {
                        message.To.Add(new MailboxAddress("", recipient));
                    }
                }

                message.Subject = subject;

                var multipart = new Multipart("mixed");

                // النص
                multipart.Add(new TextPart("plain") { Text = body });

                // المرفق
                if (File.Exists(attachmentPath))
                {
                    var attachment = new MimePart("application", "pdf")
                    {
                        Content = new MimeContent(File.OpenRead(attachmentPath)),
                        ContentDisposition = new ContentDisposition(ContentDisposition.Attachment),
                        ContentTransferEncoding = ContentEncoding.Base64,
                        FileName = Path.GetFileName(attachmentPath)
                    };
                    multipart.Add(attachment);
                }

                message.Body = multipart;

                using var client = new SmtpClient();
                await client.ConnectAsync(settings.SmtpServer, settings.SmtpPort, settings.UseSSL);
                await client.AuthenticateAsync(settings.SenderEmail, settings.SenderPassword);
                await client.SendAsync(message);
                await client.DisconnectAsync(true);

                System.Diagnostics.Debug.WriteLine($"✅ تم إرسال البريد الإلكتروني مع مرفق: {subject}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل إرسال البريد الإلكتروني مع مرفق: {ex.Message}");
                throw;
            }
        }

        // اختبار إعدادات البريد الإلكتروني
        public static async Task<bool> TestEmailSettings()
        {
            try
            {
                var settings = GetEmailSettings();
                if (!settings.EmailNotificationsEnabled)
                {
                    return false;
                }

                await SendEmail(
                    subject: "🧪 اختبار نظام الإشعارات - نظام الموارد البشرية",
                    body: $"هذه رسالة اختبار من نظام إدارة الموارد البشرية.\n\nتاريخ الاختبار: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n\nإذا وصلتك هذه الرسالة، فإن إعدادات البريد الإلكتروني تعمل بشكل صحيح.",
                    recipients: settings.AdminEmails
                );

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل اختبار البريد الإلكتروني: {ex.Message}");
                return false;
            }
        }

        // إرسال تقرير يومي
        public static async void SendDailyReport()
        {
            try
            {
                var settings = GetEmailSettings();
                if (!settings.EmailNotificationsEnabled)
                {
                    return;
                }

                // جمع إحصائيات النظام
                var stats = await GatherSystemStats();

                string message = $"التقرير اليومي لنظام إدارة الموارد البشرية\n";
                message += $"التاريخ: {DateTime.Now:yyyy-MM-dd}\n\n";
                message += $"📊 الإحصائيات:\n";
                message += $"• إجمالي الموظفين: {stats.TotalEmployees}\n";
                message += $"• الموظفين النشطين: {stats.ActiveEmployees}\n";
                message += $"• النسخ الاحتياطية: {stats.BackupCount}\n";
                message += $"• حالة قاعدة البيانات: {(stats.DatabaseHealthy ? "سليمة ✅" : "تحتاج مراجعة ⚠️")}\n\n";
                message += $"🔄 آخر نسخة احتياطية: {stats.LastBackupTime}\n";
                message += $"💾 حجم قاعدة البيانات: {stats.DatabaseSize}\n\n";
                message += "تم إنشاء هذا التقرير تلقائياً.";

                await SendEmail(
                    subject: $"📈 التقرير اليومي - {DateTime.Now:yyyy-MM-dd}",
                    body: message,
                    recipients: settings.AdminEmails
                );

                System.Diagnostics.Debug.WriteLine("✅ تم إرسال التقرير اليومي");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل إرسال التقرير اليومي: {ex.Message}");
            }
        }

        // جمع إحصائيات النظام
        private static async Task<SystemStats> GatherSystemStats()
        {
            var stats = new SystemStats();

            try
            {
                // إحصائيات قاعدة البيانات
                var employeesData = DatabaseHelper.GetAllEmployeesFromAccess();
                stats.TotalEmployees = employeesData.Rows.Count;
                stats.ActiveEmployees = employeesData.Rows.Count; // يمكن تحسينها لاحقاً

                // إحصائيات النسخ الاحتياطية
                var backups = BackupManager.GetAvailableBackups();
                stats.BackupCount = backups.Count;
                stats.LastBackupTime = backups.FirstOrDefault()?.CreationTimeFormatted ?? "لا توجد نسخ";

                // حالة قاعدة البيانات
                stats.DatabaseHealthy = DatabaseHelper.TestAccessConnection();

                // حجم قاعدة البيانات
                if (File.Exists("HRManagement.accdb"))
                {
                    var fileInfo = new FileInfo("HRManagement.accdb");
                    stats.DatabaseSize = FormatFileSize(fileInfo.Length);
                }
                else
                {
                    stats.DatabaseSize = "غير متاح";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جمع الإحصائيات: {ex.Message}");
            }

            return stats;
        }

        // تنسيق حجم الملف
        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    // إحصائيات النظام
    public class SystemStats
    {
        public int TotalEmployees { get; set; }
        public int ActiveEmployees { get; set; }
        public int BackupCount { get; set; }
        public string LastBackupTime { get; set; } = "";
        public bool DatabaseHealthy { get; set; }
        public string DatabaseSize { get; set; } = "";
    }
}
