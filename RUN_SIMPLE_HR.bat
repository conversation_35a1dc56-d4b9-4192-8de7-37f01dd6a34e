@echo off
title Simple HR Management System
color 0A

echo.
echo ========================================
echo    Simple HR Management System
echo    نظام إدارة الموارد البشرية المبسط
echo ========================================
echo.

echo Building simple HR application...
dotnet build SimpleHR.csproj

if errorlevel 1 (
    echo Build failed. Trying alternative method...
    echo.
    csc /target:winexe /reference:System.Windows.Forms.dll /reference:System.Drawing.dll SimpleHR.cs
    if errorlevel 1 (
        echo Both methods failed. Please check .NET installation.
        pause
        exit /b 1
    )
    echo.
    echo Running compiled application...
    SimpleHR.exe
    del SimpleHR.exe
) else (
    echo Build successful! Starting application...
    echo.
    echo LOOK FOR:
    echo 1. Welcome message box first
    echo 2. Main HR system window after clicking OK
    echo.
    dotnet run --project SimpleHR.csproj
)

echo.
echo Application finished.
pause
