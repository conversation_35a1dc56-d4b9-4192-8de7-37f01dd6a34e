<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="DatabaseHelper.cs" />
    <Compile Remove="NotificationManager.cs" />
    <Compile Remove="PDFReportGenerator.cs" />
    <Compile Remove="SystemRepairTool.cs" />
    <Compile Remove="AccessDatabaseManager.cs" />
    <Compile Remove="DatabaseManager.cs" />
    <Compile Remove="Form1.cs" />
    <Compile Remove="LoginForm.cs" />
    <Compile Remove="Program.cs" />
    <Compile Remove="Employee.cs" />
    <Compile Remove="EmployeeManager.cs" />
    <Compile Remove="BackupManager.cs" />
    <Compile Remove="NotesAndTipsManager.cs" />
    <Compile Remove="MinimalTest.cs" />
    <Compile Remove="TestLoginForm.cs" />
    <Compile Remove="SimpleTestForm.cs" />
  </ItemGroup>

</Project>
