using System;
using System.Drawing;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    public partial class TestLoginForm : Form
    {
        public TestLoginForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "HR Management System v5.3.0 - Login Test";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(52, 152, 219);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            // Title
            Label titleLabel = new Label
            {
                Text = "نظام إدارة الموارد البشرية\nHR Management System",
                Font = new Font("Arial", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Size = new Size(400, 60),
                Location = new Point(50, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Username
            Label userLabel = new Label
            {
                Text = "اسم المستخدم / Username:",
                Font = new Font("Arial", 12),
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(50, 120)
            };

            TextBox userTextBox = new TextBox
            {
                Font = new Font("Arial", 12),
                Size = new Size(300, 25),
                Location = new Point(50, 150),
                Text = "admin"
            };

            // Password
            Label passLabel = new Label
            {
                Text = "كلمة المرور / Password:",
                Font = new Font("Arial", 12),
                ForeColor = Color.White,
                Size = new Size(200, 25),
                Location = new Point(50, 190)
            };

            TextBox passTextBox = new TextBox
            {
                Font = new Font("Arial", 12),
                Size = new Size(300, 25),
                Location = new Point(50, 220),
                UseSystemPasswordChar = true,
                Text = "123456"
            };

            // Login Button
            Button loginButton = new Button
            {
                Text = "تسجيل الدخول\nLogin",
                Font = new Font("Arial", 12, FontStyle.Bold),
                Size = new Size(120, 50),
                Location = new Point(50, 270),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // Exit Button
            Button exitButton = new Button
            {
                Text = "خروج\nExit",
                Font = new Font("Arial", 12, FontStyle.Bold),
                Size = new Size(120, 50),
                Location = new Point(230, 270),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            loginButton.Click += (s, e) => {
                if (userTextBox.Text == "admin" && passTextBox.Text == "123456")
                {
                    MessageBox.Show("تم تسجيل الدخول بنجاح!\nLogin successful!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.Hide();
                    var mainForm = new Form1();
                    mainForm.ShowDialog();
                    this.Close();
                }
                else
                {
                    MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة\nInvalid username or password", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            exitButton.Click += (s, e) => {
                this.Close();
            };

            this.Controls.AddRange(new Control[] {
                titleLabel, userLabel, userTextBox, passLabel, passTextBox, loginButton, exitButton
            });
        }
    }
}
