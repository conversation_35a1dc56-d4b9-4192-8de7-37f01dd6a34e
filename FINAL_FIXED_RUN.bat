@echo off
title HR Management System v5.3.0 - All Errors Fixed
color 0A

echo.
echo ========================================
echo    HR Management System v5.3.0
echo    All Build Errors Fixed Successfully
echo ========================================
echo.

cd /d "%~dp0"

echo Fixed errors:
echo ✅ DateTime nullable operator in NotificationManager.cs
echo ✅ DateTime nullable operator in Form1.cs  
echo ✅ Null reference warnings in NotesAndTipsManager.cs
echo ✅ Async method warning in NotificationManager.cs
echo ✅ Unreachable code warning in Form1.cs
echo.

echo Building and running application...
echo.

dotnet clean >nul 2>&1
dotnet restore >nul 2>&1
dotnet build

if errorlevel 1 (
    echo.
    echo ❌ Build failed. Check errors above.
    pause
    exit /b 1
)

echo.
echo ✅ Build successful! Starting application...
echo.
echo Login credentials:
echo   Username: admin
echo   Password: 123456
echo.

dotnet run

echo.
echo Application closed.
pause
