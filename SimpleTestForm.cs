using System;
using System.Drawing;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    public partial class SimpleTestForm : Form
    {
        public SimpleTestForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "HR Management System v5.3.0 - Test";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.LightBlue;

            Label lblTest = new Label
            {
                Text = "HR Management System is working!\n\nClick OK to continue to login.",
                Font = new Font("Arial", 12, FontStyle.Bold),
                Size = new Size(350, 100),
                Location = new Point(25, 50),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Button btnOK = new Button
            {
                Text = "OK - Go to Login",
                Size = new Size(150, 40),
                Location = new Point(125, 180),
                BackColor = Color.Green,
                ForeColor = Color.White,
                Font = new Font("Arial", 10, FontStyle.Bold)
            };

            btnOK.Click += (s, e) => {
                this.Hide();
                var loginForm = new LoginForm();
                loginForm.ShowDialog();
                this.Close();
            };

            this.Controls.Add(lblTest);
            this.Controls.Add(btnOK);
        }
    }
}
