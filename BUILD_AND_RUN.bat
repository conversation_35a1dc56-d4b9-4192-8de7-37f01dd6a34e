@echo off
title HR Management System - Build and Run
color 0A

echo.
echo ========================================
echo    HR Management System v5.3.0
echo    Build and Run Tool
echo ========================================
echo.

cd /d "%~dp0"

echo [1/5] Cleaning previous builds...
dotnet clean >nul 2>&1

echo [2/5] Restoring packages...
dotnet restore

if errorlevel 1 (
    echo ERROR: Package restore failed
    pause
    exit /b 1
)

echo [3/5] Building project...
dotnet build

if errorlevel 1 (
    echo ERROR: Build failed. Check errors above.
    pause
    exit /b 1
)

echo [4/5] Build successful!
echo.

echo [5/5] Starting application...
echo.

dotnet run

echo.
echo Application closed.
pause
