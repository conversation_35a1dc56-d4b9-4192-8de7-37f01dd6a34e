E:\test\Ahmedapp for work\obj\Debug\net6.0-windows\Ahmedapp for work.csproj.AssemblyReference.cache
E:\test\Ahmedapp for work\obj\Debug\net6.0-windows\Ahmedapp for work.GeneratedMSBuildEditorConfig.editorconfig
E:\test\Ahmedapp for work\obj\Debug\net6.0-windows\Ahmedapp for work.AssemblyInfoInputs.cache
E:\test\Ahmedapp for work\obj\Debug\net6.0-windows\Ahmedapp for work.AssemblyInfo.cs
E:\test\Ahmedapp for work\obj\Debug\net6.0-windows\Ahmedapp for work.csproj.CoreCompileInputs.cache
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\Ahmedapp for work.exe
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\Ahmedapp for work.dll.config
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\Ahmedapp for work.deps.json
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\<PERSON>app for work.runtimeconfig.json
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\Ahmedapp for work.dll
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\ref\Ahmedapp for work.dll
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\Ahmedapp for work.pdb
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\System.Configuration.ConfigurationManager.dll
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\System.Data.SqlClient.dll
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\System.Security.Cryptography.ProtectedData.dll
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\runtimes\win-arm64\native\sni.dll
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\runtimes\win-x64\native\sni.dll
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\runtimes\win-x86\native\sni.dll
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\runtimes\unix\lib\net6.0\System.Data.SqlClient.dll
E:\test\Ahmedapp for work\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.Data.SqlClient.dll
E:\test\Ahmedapp for work\obj\Debug\net6.0-windows\Ahmedapp for work.csproj.CopyComplete
E:\test\Ahmedapp for work\obj\Debug\net6.0-windows\Ahmedapp for work.dll
E:\test\Ahmedapp for work\obj\Debug\net6.0-windows\ref\Ahmedapp for work.dll
E:\test\Ahmedapp for work\obj\Debug\net6.0-windows\Ahmedapp for work.pdb
E:\test\Ahmedapp for work\obj\Debug\net6.0-windows\Ahmedapp for work.genruntimeconfig.cache
E:\HR_Management_System\bin\Debug\net6.0-windows\Ahmedapp for work.exe
E:\HR_Management_System\bin\Debug\net6.0-windows\Ahmedapp for work.dll.config
E:\HR_Management_System\bin\Debug\net6.0-windows\Ahmedapp for work.deps.json
E:\HR_Management_System\bin\Debug\net6.0-windows\Ahmedapp for work.runtimeconfig.json
E:\HR_Management_System\bin\Debug\net6.0-windows\Ahmedapp for work.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\ref\Ahmedapp for work.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\Ahmedapp for work.pdb
E:\HR_Management_System\bin\Debug\net6.0-windows\System.Configuration.ConfigurationManager.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\System.Data.SqlClient.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\System.Security.Cryptography.ProtectedData.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\runtimes\win-arm64\native\sni.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\runtimes\win-x64\native\sni.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\runtimes\win-x86\native\sni.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\runtimes\unix\lib\net6.0\System.Data.SqlClient.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.Data.SqlClient.dll
E:\HR_Management_System\obj\Debug\net6.0-windows\Ahmedapp for work.csproj.AssemblyReference.cache
E:\HR_Management_System\obj\Debug\net6.0-windows\Ahmedapp for work.GeneratedMSBuildEditorConfig.editorconfig
E:\HR_Management_System\obj\Debug\net6.0-windows\Ahmedapp for work.AssemblyInfoInputs.cache
E:\HR_Management_System\obj\Debug\net6.0-windows\Ahmedapp for work.AssemblyInfo.cs
E:\HR_Management_System\obj\Debug\net6.0-windows\Ahmedapp for work.csproj.CoreCompileInputs.cache
E:\HR_Management_System\obj\Debug\net6.0-windows\Ahmedapp for work.csproj.CopyComplete
E:\HR_Management_System\obj\Debug\net6.0-windows\Ahmedapp for work.dll
E:\HR_Management_System\obj\Debug\net6.0-windows\ref\Ahmedapp for work.dll
E:\HR_Management_System\obj\Debug\net6.0-windows\Ahmedapp for work.pdb
E:\HR_Management_System\obj\Debug\net6.0-windows\Ahmedapp for work.genruntimeconfig.cache
E:\HR_Management_System\bin\Debug\net6.0-windows\Newtonsoft.Json.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\EntityFramework.SqlServer.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\EntityFramework.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\System.Data.SQLite.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\System.Data.SQLite.EF6.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\runtimes\linux-x64\native\SQLite.Interop.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\runtimes\osx-x64\native\SQLite.Interop.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\runtimes\win-x64\native\SQLite.Interop.dll
E:\HR_Management_System\bin\Debug\net6.0-windows\runtimes\win-x86\native\SQLite.Interop.dll
