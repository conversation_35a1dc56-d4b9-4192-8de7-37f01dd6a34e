using System;
using System.IO;
using System.Windows.Forms;
using System.Reflection;

namespace Ahmedapp_for_work
{
    public class PortableManager
    {
        private static bool isPortableMode = false;
        private static string? portableDataPath;
        private static readonly string portableConfigFile = "portable.config";

        // إعدادات النسخة المحمولة
        public static class PortableSettings
        {
            public static bool IsPortableMode => isPortableMode;
            public static string DataPath => portableDataPath ?? GetDefaultDataPath();
            public static bool CreatePortableStructure { get; set; } = true;
            public static bool UseRelativePaths { get; set; } = true;
            public static bool AutoDetectUSB { get; set; } = true;
        }

        // تهيئة النسخة المحمولة
        public static void InitializePortableMode()
        {
            try
            {
                // التحقق من وجود ملف التكوين المحمول
                CheckPortableConfiguration();

                // إعداد مسارات البيانات
                SetupDataPaths();

                // إنشاء هيكل المجلدات المطلوب
                if (PortableSettings.CreatePortableStructure)
                {
                    CreatePortableStructure();
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تهيئة النسخة المحمولة - الوضع: {(isPortableMode ? "محمول" : "عادي")}");
                System.Diagnostics.Debug.WriteLine($"📁 مسار البيانات: {PortableSettings.DataPath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة النسخة المحمولة: {ex.Message}");
                NotificationManager.SendErrorNotification("خطأ في النسخة المحمولة", ex.Message, ex);
            }
        }

        // التحقق من تكوين النسخة المحمولة
        private static void CheckPortableConfiguration()
        {
            try
            {
                string appDirectory = GetApplicationDirectory();
                string configPath = Path.Combine(appDirectory, portableConfigFile);

                if (File.Exists(configPath))
                {
                    // قراءة إعدادات النسخة المحمولة
                    string[] configLines = File.ReadAllLines(configPath);
                    foreach (string line in configLines)
                    {
                        if (line.StartsWith("PORTABLE=", StringComparison.OrdinalIgnoreCase))
                        {
                            isPortableMode = line.Split('=')[1].Trim().Equals("true", StringComparison.OrdinalIgnoreCase);
                        }
                        else if (line.StartsWith("DATA_PATH=", StringComparison.OrdinalIgnoreCase))
                        {
                            portableDataPath = line.Split('=')[1].Trim();
                        }
                    }
                }
                else
                {
                    // التحقق التلقائي من النسخة المحمولة
                    AutoDetectPortableMode();
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم فحص تكوين النسخة المحمولة: {(isPortableMode ? "محمول" : "عادي")}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فحص تكوين النسخة المحمولة: {ex.Message}");
            }
        }

        // الكشف التلقائي عن النسخة المحمولة
        private static void AutoDetectPortableMode()
        {
            try
            {
                string appDirectory = GetApplicationDirectory();
                DriveInfo appDrive = new DriveInfo(Path.GetPathRoot(appDirectory) ?? "C:");

                // التحقق من نوع القرص
                if (appDrive.DriveType == DriveType.Removable)
                {
                    isPortableMode = true;
                    portableDataPath = Path.Combine(appDirectory, "Data");
                    
                    // إنشاء ملف التكوين
                    CreatePortableConfig();
                    
                    System.Diagnostics.Debug.WriteLine("✅ تم اكتشاف النسخة المحمولة تلقائياً (USB/قرص قابل للإزالة)");
                }
                else
                {
                    // التحقق من وجود مجلد Data في نفس مجلد التطبيق
                    string dataFolder = Path.Combine(appDirectory, "Data");
                    if (Directory.Exists(dataFolder))
                    {
                        isPortableMode = true;
                        portableDataPath = dataFolder;
                        CreatePortableConfig();
                        
                        System.Diagnostics.Debug.WriteLine("✅ تم اكتشاف النسخة المحمولة (مجلد Data موجود)");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الكشف التلقائي: {ex.Message}");
            }
        }

        // إنشاء ملف تكوين النسخة المحمولة
        private static void CreatePortableConfig()
        {
            try
            {
                string appDirectory = GetApplicationDirectory();
                string configPath = Path.Combine(appDirectory, portableConfigFile);

                string configContent = $"# تكوين النسخة المحمولة لنظام إدارة الموارد البشرية\n";
                configContent += $"# تم الإنشاء تلقائياً في: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n\n";
                configContent += $"PORTABLE={isPortableMode.ToString().ToLower()}\n";
                configContent += $"DATA_PATH={portableDataPath}\n";
                configContent += $"VERSION=4.3.0\n";
                configContent += $"AUTO_CREATED=true\n";

                File.WriteAllText(configPath, configContent);
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء ملف تكوين النسخة المحمولة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء ملف التكوين: {ex.Message}");
            }
        }

        // إعداد مسارات البيانات
        private static void SetupDataPaths()
        {
            try
            {
                if (isPortableMode)
                {
                    // استخدام مسارات نسبية في النسخة المحمولة
                    if (string.IsNullOrEmpty(portableDataPath))
                    {
                        portableDataPath = Path.Combine(GetApplicationDirectory(), "Data");
                    }

                    // تحديث مسارات قواعد البيانات
                    UpdateDatabasePaths();
                }
                else
                {
                    // استخدام مسارات النظام العادية
                    portableDataPath = GetDefaultDataPath();
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إعداد مسارات البيانات: {portableDataPath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعداد مسارات البيانات: {ex.Message}");
            }
        }

        // تحديث مسارات قواعد البيانات
        private static void UpdateDatabasePaths()
        {
            try
            {
                if (isPortableMode && !string.IsNullOrEmpty(portableDataPath))
                {
                    // تحديث مسارات قواعد البيانات لتكون نسبية
                    string dbPath = Path.Combine(portableDataPath, "Databases");
                    Directory.CreateDirectory(dbPath);

                    // يمكن تحديث DatabaseHelper هنا لاستخدام المسارات الجديدة
                    System.Diagnostics.Debug.WriteLine($"✅ تم تحديث مسارات قواعد البيانات: {dbPath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث مسارات قواعد البيانات: {ex.Message}");
            }
        }

        // إنشاء هيكل المجلدات المحمولة
        private static void CreatePortableStructure()
        {
            try
            {
                if (!isPortableMode || string.IsNullOrEmpty(portableDataPath))
                {
                    return;
                }

                // إنشاء المجلدات الأساسية
                string[] folders = {
                    portableDataPath,
                    Path.Combine(portableDataPath, "Databases"),
                    Path.Combine(portableDataPath, "Backups"),
                    Path.Combine(portableDataPath, "Reports"),
                    Path.Combine(portableDataPath, "EmployeeFiles"),
                    Path.Combine(portableDataPath, "Logs"),
                    Path.Combine(portableDataPath, "Config"),
                    Path.Combine(portableDataPath, "Temp")
                };

                foreach (string folder in folders)
                {
                    if (!Directory.Exists(folder))
                    {
                        Directory.CreateDirectory(folder);
                        System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء مجلد: {Path.GetFileName(folder)}");
                    }
                }

                // إنشاء ملف README
                CreatePortableReadme();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء هيكل المجلدات المحمولة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء هيكل المجلدات: {ex.Message}");
            }
        }

        // إنشاء ملف README للنسخة المحمولة
        private static void CreatePortableReadme()
        {
            try
            {
                if (string.IsNullOrEmpty(portableDataPath))
                {
                    return;
                }

                string readmePath = Path.Combine(portableDataPath, "README.txt");
                if (File.Exists(readmePath))
                {
                    return; // الملف موجود بالفعل
                }

                string readmeContent = "نظام إدارة الموارد البشرية - النسخة المحمولة\n";
                readmeContent += "================================================\n\n";
                readmeContent += $"تاريخ الإنشاء: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\n";
                readmeContent += $"الإصدار: v4.3.0\n\n";
                readmeContent += "هيكل المجلدات:\n";
                readmeContent += "├── Databases/     - ملفات قواعد البيانات\n";
                readmeContent += "├── Backups/       - النسخ الاحتياطية\n";
                readmeContent += "├── Reports/       - التقارير المُنشأة\n";
                readmeContent += "├── EmployeeFiles/ - ملفات الموظفين\n";
                readmeContent += "├── Logs/          - ملفات السجلات\n";
                readmeContent += "├── Config/        - ملفات التكوين\n";
                readmeContent += "└── Temp/          - ملفات مؤقتة\n\n";
                readmeContent += "ملاحظات مهمة:\n";
                readmeContent += "• هذه نسخة محمولة من النظام\n";
                readmeContent += "• جميع البيانات محفوظة في هذا المجلد\n";
                readmeContent += "• يمكن نقل المجلد بالكامل إلى أي جهاز\n";
                readmeContent += "• تأكد من عمل نسخ احتياطية دورية\n";
                readmeContent += "• لا تحذف أي ملفات من مجلد Databases\n\n";
                readmeContent += "للدعم التقني: راجع ملفات التوثيق المرفقة\n";

                File.WriteAllText(readmePath, readmeContent);
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء ملف README للنسخة المحمولة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء ملف README: {ex.Message}");
            }
        }

        // الحصول على مجلد التطبيق
        private static string GetApplicationDirectory()
        {
            try
            {
                return Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? Environment.CurrentDirectory;
            }
            catch
            {
                return Environment.CurrentDirectory;
            }
        }

        // الحصول على مسار البيانات الافتراضي
        private static string GetDefaultDataPath()
        {
            try
            {
                return Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "HR Management System");
            }
            catch
            {
                return Path.Combine(GetApplicationDirectory(), "Data");
            }
        }

        // إنشاء نسخة محمولة من التطبيق
        public static bool CreatePortableVersion(string destinationPath)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 بدء إنشاء النسخة المحمولة في: {destinationPath}");

                // إنشاء مجلد الوجهة
                Directory.CreateDirectory(destinationPath);

                // نسخ ملفات التطبيق
                string appDirectory = GetApplicationDirectory();
                CopyApplicationFiles(appDirectory, destinationPath);

                // إنشاء ملف التكوين المحمول
                string configPath = Path.Combine(destinationPath, portableConfigFile);
                string configContent = "PORTABLE=true\n";
                configContent += "DATA_PATH=Data\n";
                configContent += $"VERSION=4.3.0\n";
                configContent += $"CREATED={DateTime.Now:yyyy-MM-dd HH:mm:ss}\n";
                File.WriteAllText(configPath, configContent);

                // إنشاء هيكل مجلدات البيانات
                string dataPath = Path.Combine(destinationPath, "Data");
                string oldPortableDataPath = portableDataPath;
                bool oldPortableMode = isPortableMode;

                portableDataPath = dataPath;
                isPortableMode = true;
                CreatePortableStructure();

                // استعادة الإعدادات الأصلية
                portableDataPath = oldPortableDataPath;
                isPortableMode = oldPortableMode;

                // نسخ البيانات الحالية إذا كانت موجودة
                CopyExistingData(destinationPath);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء النسخة المحمولة بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل إنشاء النسخة المحمولة: {ex.Message}");
                NotificationManager.SendErrorNotification("فشل إنشاء النسخة المحمولة", ex.Message, ex);
                return false;
            }
        }

        // نسخ ملفات التطبيق
        private static void CopyApplicationFiles(string sourceDir, string destDir)
        {
            try
            {
                string[] filesToCopy = {
                    "*.exe", "*.dll", "*.config", "*.json"
                };

                foreach (string pattern in filesToCopy)
                {
                    string[] files = Directory.GetFiles(sourceDir, pattern);
                    foreach (string file in files)
                    {
                        string fileName = Path.GetFileName(file);
                        string destFile = Path.Combine(destDir, fileName);
                        File.Copy(file, destFile, true);
                    }
                }

                System.Diagnostics.Debug.WriteLine("✅ تم نسخ ملفات التطبيق");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ ملفات التطبيق: {ex.Message}");
                throw;
            }
        }

        // نسخ البيانات الموجودة
        private static void CopyExistingData(string destinationPath)
        {
            try
            {
                string destDataPath = Path.Combine(destinationPath, "Data", "Databases");

                // نسخ قواعد البيانات الموجودة
                string[] dbFiles = { "HRManagement.accdb", "hr_management.db", "users.db" };
                foreach (string dbFile in dbFiles)
                {
                    if (File.Exists(dbFile))
                    {
                        string destFile = Path.Combine(destDataPath, dbFile);
                        File.Copy(dbFile, destFile, true);
                        System.Diagnostics.Debug.WriteLine($"✅ تم نسخ قاعدة البيانات: {dbFile}");
                    }
                }

                // نسخ مجلد ملفات الموظفين
                if (Directory.Exists("EmployeeFiles"))
                {
                    string destEmployeeFiles = Path.Combine(destinationPath, "Data", "EmployeeFiles");
                    CopyDirectory("EmployeeFiles", destEmployeeFiles);
                    System.Diagnostics.Debug.WriteLine("✅ تم نسخ ملفات الموظفين");
                }

                System.Diagnostics.Debug.WriteLine("✅ تم نسخ البيانات الموجودة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ البيانات: {ex.Message}");
            }
        }

        // نسخ مجلد بالكامل
        private static void CopyDirectory(string sourceDir, string destDir)
        {
            Directory.CreateDirectory(destDir);

            foreach (string file in Directory.GetFiles(sourceDir))
            {
                string destFile = Path.Combine(destDir, Path.GetFileName(file));
                File.Copy(file, destFile, true);
            }

            foreach (string subDir in Directory.GetDirectories(sourceDir))
            {
                string destSubDir = Path.Combine(destDir, Path.GetFileName(subDir));
                CopyDirectory(subDir, destSubDir);
            }
        }

        // التحقق من صحة النسخة المحمولة
        public static bool ValidatePortableInstallation()
        {
            try
            {
                if (!isPortableMode)
                {
                    return true; // النسخة العادية
                }

                // التحقق من وجود المجلدات المطلوبة
                string[] requiredFolders = {
                    portableDataPath,
                    Path.Combine(portableDataPath, "Databases"),
                    Path.Combine(portableDataPath, "Backups")
                };

                foreach (string folder in requiredFolders)
                {
                    if (!Directory.Exists(folder))
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ مجلد مطلوب مفقود: {folder}");
                        return false;
                    }
                }

                System.Diagnostics.Debug.WriteLine("✅ النسخة المحمولة صحيحة");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من النسخة المحمولة: {ex.Message}");
                return false;
            }
        }

        // الحصول على معلومات النسخة المحمولة
        public static PortableInfo GetPortableInfo()
        {
            return new PortableInfo
            {
                IsPortableMode = isPortableMode,
                DataPath = portableDataPath ?? "",
                ApplicationPath = GetApplicationDirectory(),
                ConfigExists = File.Exists(Path.Combine(GetApplicationDirectory(), portableConfigFile)),
                IsValid = ValidatePortableInstallation(),
                DriveType = GetDriveType(),
                TotalSpace = GetTotalSpace(),
                FreeSpace = GetFreeSpace()
            };
        }

        // الحصول على نوع القرص
        private static string GetDriveType()
        {
            try
            {
                string appPath = GetApplicationDirectory();
                DriveInfo drive = new DriveInfo(Path.GetPathRoot(appPath) ?? "C:");
                return drive.DriveType.ToString();
            }
            catch
            {
                return "Unknown";
            }
        }

        // الحصول على المساحة الإجمالية
        private static long GetTotalSpace()
        {
            try
            {
                string appPath = GetApplicationDirectory();
                DriveInfo drive = new DriveInfo(Path.GetPathRoot(appPath) ?? "C:");
                return drive.TotalSize;
            }
            catch
            {
                return 0;
            }
        }

        // الحصول على المساحة الفارغة
        private static long GetFreeSpace()
        {
            try
            {
                string appPath = GetApplicationDirectory();
                DriveInfo drive = new DriveInfo(Path.GetPathRoot(appPath) ?? "C:");
                return drive.AvailableFreeSpace;
            }
            catch
            {
                return 0;
            }
        }
    }

    // معلومات النسخة المحمولة
    public class PortableInfo
    {
        public bool IsPortableMode { get; set; }
        public string DataPath { get; set; } = "";
        public string ApplicationPath { get; set; } = "";
        public bool ConfigExists { get; set; }
        public bool IsValid { get; set; }
        public string DriveType { get; set; } = "";
        public long TotalSpace { get; set; }
        public long FreeSpace { get; set; }

        public string TotalSpaceFormatted => FormatBytes(TotalSpace);
        public string FreeSpaceFormatted => FormatBytes(FreeSpace);

        private static string FormatBytes(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
