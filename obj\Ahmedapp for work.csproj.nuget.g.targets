﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)system.security.cryptography.protecteddata\9.0.5\buildTransitive\netcoreapp2.0\System.Security.Cryptography.ProtectedData.targets" Condition="Exists('$(NuGetPackageRoot)system.security.cryptography.protecteddata\9.0.5\buildTransitive\netcoreapp2.0\System.Security.Cryptography.ProtectedData.targets')" />
    <Import Project="$(NuGetPackageRoot)system.configuration.configurationmanager\9.0.5\buildTransitive\netcoreapp2.0\System.Configuration.ConfigurationManager.targets" Condition="Exists('$(NuGetPackageRoot)system.configuration.configurationmanager\9.0.5\buildTransitive\netcoreapp2.0\System.Configuration.ConfigurationManager.targets')" />
    <Import Project="$(NuGetPackageRoot)entityframework\6.4.4\buildTransitive\netcoreapp3.0\EntityFramework.targets" Condition="Exists('$(NuGetPackageRoot)entityframework\6.4.4\buildTransitive\netcoreapp3.0\EntityFramework.targets')" />
    <Import Project="$(NuGetPackageRoot)system.data.oledb\9.0.0\buildTransitive\netcoreapp2.0\System.Data.OleDb.targets" Condition="Exists('$(NuGetPackageRoot)system.data.oledb\9.0.0\buildTransitive\netcoreapp2.0\System.Data.OleDb.targets')" />
  </ImportGroup>
</Project>