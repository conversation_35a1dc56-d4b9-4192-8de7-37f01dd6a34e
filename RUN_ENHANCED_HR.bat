@echo off
title Enhanced HR Management System v6.0.0 - With SQL & JSON Support
color 0A

echo.
echo ========================================
echo    Enhanced HR Management System v6.0.0
echo    نظام إدارة الموارد البشرية المحسن
echo    With SQL Server & JSON Support
echo ========================================
echo.

echo 🧹 Cleaning previous build...
dotnet clean HRSystem.csproj

echo.
echo 🔧 Installing required packages...
dotnet restore HRSystem.csproj

echo.
echo 🏗️ Building Enhanced HR System...
dotnet build HRSystem.csproj --verbosity minimal

if errorlevel 1 (
    echo ❌ Build failed. Please check the error messages above.
    echo.
    echo Common fixes:
    echo • Make sure .NET 6.0 SDK is installed
    echo • Check for duplicate file references
    echo • Verify all using statements are correct
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.
echo 🚀 Starting Enhanced HR Management System...
echo.
echo 🎯 NEW ENHANCED FEATURES:
echo • 📦 System.Data.SqlClient - SQL Server database support
echo • 📄 Newtonsoft.Json - Advanced JSON data handling
echo • 🔧 HRManagementSystem.Helpers - Employee management classes
echo • ⚡ Null-safe programming with nullable reference types
echo • 🛡️ Enhanced error handling and data validation
echo • 💾 Professional database integration ready
echo • 🔄 Improved data persistence and backup systems
echo.
echo 📱 SYSTEM CAPABILITIES:
echo • Complete employee management with advanced features
echo • SQL Server database connectivity (when configured)
echo • JSON-based data storage and export
echo • Professional error handling and logging
echo • Enhanced search and reporting capabilities
echo • Automatic data validation and integrity checks
echo.

dotnet run --project HRSystem.csproj

echo.
echo Enhanced HR System finished.
pause
