{"format": 1, "restore": {"E:\\HR_Management_System\\Ahmedapp for work.csproj": {}}, "projects": {"E:\\HR_Management_System\\Ahmedapp for work.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\HR_Management_System\\Ahmedapp for work.csproj", "projectName": "<PERSON><PERSON><PERSON> for work", "projectPath": "E:\\HR_Management_System\\Ahmedapp for work.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\HR_Management_System\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Configuration.ConfigurationManager": {"target": "Package", "version": "[9.0.5, )"}, "System.Data.SQLite": {"target": "Package", "version": "[1.0.119, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.9.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.136\\RuntimeIdentifierGraph.json"}}}}}