@echo off
title HR System Status Check
color 0A

echo.
echo ========================================
echo    HR Management System Status Check
echo    فحص حالة نظام إدارة الموارد البشرية
echo ========================================
echo.

echo 🔍 Checking system status...
echo.

echo 📁 Project Files:
if exist "Program.cs" (
    echo ✅ Program.cs - Found
) else (
    echo ❌ Program.cs - Missing
)

if exist "EmployeeManager.cs" (
    echo ✅ EmployeeManager.cs - Found
) else (
    echo ❌ EmployeeManager.cs - Missing
)

if exist "HRSystem.csproj" (
    echo ✅ HRSystem.csproj - Found
) else (
    echo ❌ HRSystem.csproj - Missing
)

echo.
echo 🔧 Testing build...
dotnet build HRSystem.csproj --verbosity quiet

if errorlevel 1 (
    echo ❌ Build failed
    echo.
    echo 🔍 Common issues:
    echo • Check .NET 6.0 SDK installation
    echo • Verify package references
    echo • Check for syntax errors
) else (
    echo ✅ Build successful
    echo.
    echo 🚀 System is ready to run!
    echo.
    echo 📱 To start the application:
    echo • Double-click: FIXED_HR_SYSTEM.bat
    echo • Or run: dotnet run --project HRSystem.csproj
)

echo.
echo 📊 Current running processes:
tasklist /FI "IMAGENAME eq dotnet.exe" 2>nul | find "dotnet.exe" >nul
if errorlevel 1 (
    echo ❌ No .NET processes running
) else (
    echo ✅ .NET processes are running
    echo 💡 HR System may already be running
)

echo.
echo Status check completed.
pause
