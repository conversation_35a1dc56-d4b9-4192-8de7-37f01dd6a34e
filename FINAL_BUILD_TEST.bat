@echo off
title HR Management System - Final Build Test
color 0A

echo.
echo ========================================
echo    HR Management System v5.3.0
echo    Final Build Test - All Errors Fixed
echo ========================================
echo.

cd /d "%~dp0"

echo Testing build after fixing all errors:
echo.
echo 1. Fixed NotificationManager duplicate class
echo 2. Fixed Timer ambiguous reference in BackupManager
echo 3. Fixed Font ambiguous reference in PDFReportGenerator
echo 4. Removed COM Reference (ADOX)
echo.

echo Starting build test...
echo.

dotnet clean
echo Clean completed.
echo.

dotnet restore
echo Restore completed.
echo.

echo Building project...
dotnet build

if errorlevel 1 (
    echo.
    echo ========================================
    echo    BUILD FAILED - CHECK ERRORS ABOVE
    echo ========================================
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo    BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo All errors have been fixed:
    echo ✅ NotificationManager duplicate - FIXED
    echo ✅ Timer ambiguous reference - FIXED  
    echo ✅ Font ambiguous reference - FIXED
    echo ✅ COM Reference removed - FIXED
    echo.
    echo Ready to run the application!
    echo.
    
    set /p choice="Do you want to run the application now? (Y/N): "
    if /i "%choice%"=="Y" (
        echo.
        echo Starting HR Management System...
        echo.
        dotnet run
    )
)

echo.
echo Press any key to exit...
pause >nul
