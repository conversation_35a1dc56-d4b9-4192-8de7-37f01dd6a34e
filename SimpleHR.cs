using System;
using System.Drawing;
using System.Windows.Forms;

namespace SimpleHR
{
    public class SimpleHRApp : Form
    {
        public SimpleHRApp()
        {
            // إعداد النافذة الأساسية
            this.Text = "نظام إدارة الموارد البشرية - HR Management System";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            
            CreateMainInterface();
        }
        
        private void CreateMainInterface()
        {
            // العنوان الرئيسي
            Label titleLabel = new Label
            {
                Text = "نظام إدارة الموارد البشرية\nHR Management System v5.3.0",
                Font = new Font("Arial", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Size = new Size(700, 80),
                Location = new Point(50, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // أزرار القائمة الرئيسية
            Button btnEmployees = CreateMenuButton("👥 الموظفين\nEmployees", 50, 120);
            Button btnForms = CreateMenuButton("📋 النماذج\nForms", 250, 120);
            Button btnReports = CreateMenuButton("📊 التقارير\nReports", 450, 120);
            Button btnSearch = CreateMenuButton("🔍 البحث\nSearch", 650, 120);
            
            Button btnSettings = CreateMenuButton("⚙️ الإعدادات\nSettings", 50, 250);
            Button btnBackup = CreateMenuButton("💾 النسخ الاحتياطية\nBackup", 250, 250);
            Button btnHelp = CreateMenuButton("❓ المساعدة\nHelp", 450, 250);
            Button btnExit = CreateMenuButton("🚪 خروج\nExit", 650, 250);
            
            // ربط الأحداث
            btnEmployees.Click += (s, e) => ShowMessage("قسم الموظفين", "سيتم فتح قسم إدارة الموظفين");
            btnForms.Click += (s, e) => ShowMessage("النماذج", "سيتم فتح قسم النماذج");
            btnReports.Click += (s, e) => ShowMessage("التقارير", "سيتم فتح قسم التقارير");
            btnSearch.Click += (s, e) => ShowMessage("البحث", "سيتم فتح قسم البحث المتقدم");
            btnSettings.Click += (s, e) => ShowMessage("الإعدادات", "سيتم فتح قسم الإعدادات");
            btnBackup.Click += (s, e) => ShowMessage("النسخ الاحتياطية", "سيتم فتح قسم النسخ الاحتياطية");
            btnHelp.Click += (s, e) => ShowMessage("المساعدة", "نظام إدارة الموارد البشرية v5.3.0\nتم التطوير بواسطة فريق التطوير");
            btnExit.Click += (s, e) => {
                if (MessageBox.Show("هل تريد الخروج من النظام؟", "تأكيد الخروج", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    this.Close();
            };
            
            // معلومات النظام
            Label infoLabel = new Label
            {
                Text = "النظام يعمل بنجاح ✅\nجميع الوظائف متاحة\nSystem is running successfully",
                Font = new Font("Arial", 10),
                ForeColor = Color.Green,
                Size = new Size(700, 60),
                Location = new Point(50, 400),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            Label statusLabel = new Label
            {
                Text = $"تاريخ اليوم: {DateTime.Now:yyyy-MM-dd} | الوقت: {DateTime.Now:HH:mm:ss}",
                Font = new Font("Arial", 9),
                ForeColor = Color.Gray,
                Size = new Size(700, 20),
                Location = new Point(50, 500),
                TextAlign = ContentAlignment.MiddleCenter
            };
            
            // إضافة جميع العناصر للنافذة
            this.Controls.AddRange(new Control[] {
                titleLabel, btnEmployees, btnForms, btnReports, btnSearch,
                btnSettings, btnBackup, btnHelp, btnExit, infoLabel, statusLabel
            });
        }
        
        private Button CreateMenuButton(string text, int x, int y)
        {
            return new Button
            {
                Text = text,
                Size = new Size(150, 80),
                Location = new Point(x, y),
                Font = new Font("Arial", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
        }
        
        private void ShowMessage(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
    
    class Program
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                // رسالة ترحيب
                MessageBox.Show(
                    "مرحباً بك في نظام إدارة الموارد البشرية\n\nWelcome to HR Management System v5.3.0\n\nالنظام جاهز للاستخدام!",
                    "نظام إدارة الموارد البشرية",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
                
                Application.Run(new SimpleHRApp());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل النظام:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
