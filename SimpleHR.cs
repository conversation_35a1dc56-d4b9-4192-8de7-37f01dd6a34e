using System;
using System.Drawing;
using System.Windows.Forms;
using System.Collections.Generic;
using System.IO;

namespace SimpleHR
{
    public class SimpleHRApp : Form
    {
        private List<Employee> employees;
        private string dataFile = "employees.txt";

        public SimpleHRApp()
        {
            // إعداد النافذة الأساسية
            this.Text = "نظام إدارة الموارد البشرية - HR Management System v5.3.0";
            this.Size = new Size(900, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.Icon = SystemIcons.Application;

            // تحميل البيانات
            LoadEmployees();

            CreateMainInterface();
        }

        private void LoadEmployees()
        {
            employees = new List<Employee>();

            if (File.Exists(dataFile))
            {
                try
                {
                    var lines = File.ReadAllLines(dataFile);
                    foreach (var line in lines)
                    {
                        var parts = line.Split('|');
                        if (parts.Length >= 4)
                        {
                            employees.Add(new Employee
                            {
                                Name = parts[0],
                                NationalId = parts[1],
                                JobTitle = parts[2],
                                Department = parts[3],
                                HireDate = parts.Length > 4 ? DateTime.Parse(parts[4]) : DateTime.Now
                            });
                        }
                    }
                }
                catch
                {
                    // إذا فشل التحميل، ننشئ بيانات تجريبية
                    CreateSampleData();
                }
            }
            else
            {
                CreateSampleData();
            }
        }

        private void CreateSampleData()
        {
            employees.Add(new Employee { Name = "أحمد محمد علي", NationalId = "12345678901", JobTitle = "مدير الموارد البشرية", Department = "الموارد البشرية", HireDate = DateTime.Now.AddYears(-2) });
            employees.Add(new Employee { Name = "فاطمة أحمد حسن", NationalId = "12345678902", JobTitle = "محاسب", Department = "المحاسبة", HireDate = DateTime.Now.AddYears(-1) });
            employees.Add(new Employee { Name = "محمد عبدالله سالم", NationalId = "12345678903", JobTitle = "مطور برمجيات", Department = "تقنية المعلومات", HireDate = DateTime.Now.AddMonths(-6) });
            employees.Add(new Employee { Name = "سارة محمود عبدالرحمن", NationalId = "12345678904", JobTitle = "مساعد إداري", Department = "الإدارة", HireDate = DateTime.Now.AddMonths(-3) });
            SaveEmployees();
        }

        private void SaveEmployees()
        {
            try
            {
                var lines = new List<string>();
                foreach (var emp in employees)
                {
                    lines.Add($"{emp.Name}|{emp.NationalId}|{emp.JobTitle}|{emp.Department}|{emp.HireDate:yyyy-MM-dd}");
                }
                File.WriteAllLines(dataFile, lines);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void CreateMainInterface()
        {
            // العنوان الرئيسي
            Label titleLabel = new Label
            {
                Text = "نظام إدارة الموارد البشرية\nHR Management System v5.3.0",
                Font = new Font("Arial", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Size = new Size(700, 80),
                Location = new Point(50, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // أزرار القائمة الرئيسية
            Button btnEmployees = CreateMenuButton("👥 الموظفين\nEmployees", 50, 120);
            Button btnForms = CreateMenuButton("📋 النماذج\nForms", 250, 120);
            Button btnReports = CreateMenuButton("📊 التقارير\nReports", 450, 120);
            Button btnSearch = CreateMenuButton("🔍 البحث\nSearch", 650, 120);

            Button btnSettings = CreateMenuButton("⚙️ الإعدادات\nSettings", 50, 250);
            Button btnBackup = CreateMenuButton("💾 النسخ الاحتياطية\nBackup", 250, 250);
            Button btnHelp = CreateMenuButton("❓ المساعدة\nHelp", 450, 250);
            Button btnExit = CreateMenuButton("🚪 خروج\nExit", 650, 250);

            // ربط الأحداث
            btnEmployees.Click += (s, e) => ShowEmployeesList();
            btnForms.Click += (s, e) => ShowAddEmployeeForm();
            btnReports.Click += (s, e) => ShowReportsMenu();
            btnSearch.Click += (s, e) => ShowSearchForm();
            btnSettings.Click += (s, e) => ShowSettings();
            btnBackup.Click += (s, e) => CreateBackup();
            btnHelp.Click += (s, e) => ShowHelp();
            btnExit.Click += (s, e) => {
                if (MessageBox.Show("هل تريد الخروج من النظام؟", "تأكيد الخروج", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    this.Close();
            };

            // معلومات النظام
            Label infoLabel = new Label
            {
                Text = "النظام يعمل بنجاح ✅\nجميع الوظائف متاحة\nSystem is running successfully",
                Font = new Font("Arial", 10),
                ForeColor = Color.Green,
                Size = new Size(700, 60),
                Location = new Point(50, 400),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label statusLabel = new Label
            {
                Text = $"تاريخ اليوم: {DateTime.Now:yyyy-MM-dd} | الوقت: {DateTime.Now:HH:mm:ss}",
                Font = new Font("Arial", 9),
                ForeColor = Color.Gray,
                Size = new Size(700, 20),
                Location = new Point(50, 500),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // إضافة جميع العناصر للنافذة
            this.Controls.AddRange(new Control[] {
                titleLabel, btnEmployees, btnForms, btnReports, btnSearch,
                btnSettings, btnBackup, btnHelp, btnExit, infoLabel, statusLabel
            });
        }

        private Button CreateMenuButton(string text, int x, int y)
        {
            return new Button
            {
                Text = text,
                Size = new Size(150, 80),
                Location = new Point(x, y),
                Font = new Font("Arial", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
        }

        private void ShowEmployeesList()
        {
            var form = new Form
            {
                Text = "قائمة الموظفين - Employees List",
                Size = new Size(800, 500),
                StartPosition = FormStartPosition.CenterParent
            };

            var listView = new ListView
            {
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Dock = DockStyle.Fill
            };

            listView.Columns.Add("الاسم", 200);
            listView.Columns.Add("الرقم القومي", 120);
            listView.Columns.Add("المنصب", 150);
            listView.Columns.Add("القسم", 150);
            listView.Columns.Add("تاريخ التعيين", 100);

            foreach (var emp in employees)
            {
                var item = new ListViewItem(emp.Name);
                item.SubItems.Add(emp.NationalId);
                item.SubItems.Add(emp.JobTitle);
                item.SubItems.Add(emp.Department);
                item.SubItems.Add(emp.HireDate.ToString("yyyy-MM-dd"));
                listView.Items.Add(item);
            }

            form.Controls.Add(listView);
            form.ShowDialog();
        }

        private void ShowAddEmployeeForm()
        {
            var form = new Form
            {
                Text = "إضافة موظف جديد - Add New Employee",
                Size = new Size(500, 400),
                StartPosition = FormStartPosition.CenterParent
            };

            var nameLabel = new Label { Text = "الاسم:", Location = new Point(20, 30), Size = new Size(80, 25) };
            var nameText = new TextBox { Location = new Point(120, 30), Size = new Size(300, 25) };

            var idLabel = new Label { Text = "الرقم القومي:", Location = new Point(20, 70), Size = new Size(80, 25) };
            var idText = new TextBox { Location = new Point(120, 70), Size = new Size(300, 25) };

            var jobLabel = new Label { Text = "المنصب:", Location = new Point(20, 110), Size = new Size(80, 25) };
            var jobText = new TextBox { Location = new Point(120, 110), Size = new Size(300, 25) };

            var deptLabel = new Label { Text = "القسم:", Location = new Point(20, 150), Size = new Size(80, 25) };
            var deptText = new TextBox { Location = new Point(120, 150), Size = new Size(300, 25) };

            var saveBtn = new Button
            {
                Text = "حفظ",
                Location = new Point(120, 200),
                Size = new Size(100, 35),
                BackColor = Color.Green,
                ForeColor = Color.White
            };

            var cancelBtn = new Button
            {
                Text = "إلغاء",
                Location = new Point(240, 200),
                Size = new Size(100, 35),
                BackColor = Color.Gray,
                ForeColor = Color.White
            };

            saveBtn.Click += (s, e) => {
                if (string.IsNullOrWhiteSpace(nameText.Text) || string.IsNullOrWhiteSpace(idText.Text))
                {
                    MessageBox.Show("يرجى ملء جميع الحقول المطلوبة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                employees.Add(new Employee
                {
                    Name = nameText.Text,
                    NationalId = idText.Text,
                    JobTitle = jobText.Text,
                    Department = deptText.Text,
                    HireDate = DateTime.Now
                });

                SaveEmployees();
                MessageBox.Show("تم إضافة الموظف بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                form.Close();
            };

            cancelBtn.Click += (s, e) => form.Close();

            form.Controls.AddRange(new Control[] { nameLabel, nameText, idLabel, idText, jobLabel, jobText, deptLabel, deptText, saveBtn, cancelBtn });
            form.ShowDialog();
        }

        private void ShowSearchForm()
        {
            var searchText = ShowInputDialog("ادخل كلمة البحث:", "البحث في الموظفين");
            if (string.IsNullOrWhiteSpace(searchText)) return;

            var results = employees.FindAll(e =>
                e.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                e.NationalId.Contains(searchText) ||
                e.JobTitle.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                e.Department.Contains(searchText, StringComparison.OrdinalIgnoreCase));

            if (results.Count == 0)
            {
                MessageBox.Show("لم يتم العثور على نتائج", "البحث", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var message = $"تم العثور على {results.Count} نتيجة:\n\n";
            foreach (var emp in results)
            {
                message += $"• {emp.Name} - {emp.JobTitle} - {emp.Department}\n";
            }

            MessageBox.Show(message, "نتائج البحث", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowReportsMenu()
        {
            var message = $"تقرير الموظفين\n" +
                         $"==================\n" +
                         $"إجمالي الموظفين: {employees.Count}\n\n";

            var departments = new Dictionary<string, int>();
            foreach (var emp in employees)
            {
                if (departments.ContainsKey(emp.Department))
                    departments[emp.Department]++;
                else
                    departments[emp.Department] = 1;
            }

            message += "توزيع الموظفين حسب الأقسام:\n";
            foreach (var dept in departments)
            {
                message += $"• {dept.Key}: {dept.Value} موظف\n";
            }

            MessageBox.Show(message, "تقرير الموظفين", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowSettings()
        {
            MessageBox.Show("إعدادات النظام\n\n" +
                           "• ملف البيانات: employees.txt\n" +
                           "• عدد الموظفين: " + employees.Count + "\n" +
                           "• آخر تحديث: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                           "إعدادات النظام", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void CreateBackup()
        {
            try
            {
                var backupFile = $"backup_employees_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                File.Copy(dataFile, backupFile, true);
                MessageBox.Show($"تم إنشاء نسخة احتياطية بنجاح!\n\nاسم الملف: {backupFile}",
                               "نسخة احتياطية", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowHelp()
        {
            MessageBox.Show("نظام إدارة الموارد البشرية v5.3.0\n" +
                           "HR Management System\n\n" +
                           "المزايا:\n" +
                           "• إدارة الموظفين\n" +
                           "• البحث في البيانات\n" +
                           "• التقارير الأساسية\n" +
                           "• النسخ الاحتياطية\n\n" +
                           "تم التطوير بواسطة فريق التطوير\n" +
                           "جميع الحقوق محفوظة © 2024",
                           "حول النظام", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private string ShowInputDialog(string prompt, string title)
        {
            var form = new Form
            {
                Text = title,
                Size = new Size(400, 150),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var label = new Label
            {
                Text = prompt,
                Location = new Point(20, 20),
                Size = new Size(350, 25)
            };

            var textBox = new TextBox
            {
                Location = new Point(20, 50),
                Size = new Size(350, 25)
            };

            var okButton = new Button
            {
                Text = "موافق",
                Location = new Point(200, 80),
                Size = new Size(80, 30),
                DialogResult = DialogResult.OK
            };

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(290, 80),
                Size = new Size(80, 30),
                DialogResult = DialogResult.Cancel
            };

            form.Controls.AddRange(new Control[] { label, textBox, okButton, cancelButton });
            form.AcceptButton = okButton;
            form.CancelButton = cancelButton;

            return form.ShowDialog() == DialogResult.OK ? textBox.Text : "";
        }
    }

    public class Employee
    {
        public string Name { get; set; } = "";
        public string NationalId { get; set; } = "";
        public string JobTitle { get; set; } = "";
        public string Department { get; set; } = "";
        public DateTime HireDate { get; set; }
    }

    class Program
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // رسالة ترحيب
                MessageBox.Show(
                    "مرحباً بك في نظام إدارة الموارد البشرية\n\nWelcome to HR Management System v5.3.0\n\nالنظام جاهز للاستخدام!",
                    "نظام إدارة الموارد البشرية",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                Application.Run(new SimpleHRApp());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل النظام:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
