using System;
using System.IO;
using System.Threading.Tasks;
using System.Timers;
using System.IO.Compression;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    public class BackupManager
    {
        private static Timer? backupTimer;
        private static readonly string backupFolder = "Backups";
        private static readonly string usbBackupFolder = "USB_Backups";
        private static bool isBackupRunning = false;

        // إعدادات النسخ الاحتياطي
        public static class BackupSettings
        {
            public static bool AutoBackupEnabled { get; set; } = true;
            public static int BackupIntervalMinutes { get; set; } = 30; // كل 30 دقيقة
            public static int MaxBackupFiles { get; set; } = 10; // الاحتفاظ بـ 10 نسخ
            public static bool BackupToUSB { get; set; } = true;
            public static bool CompressBackups { get; set; } = true;
        }

        // بدء نظام النسخ الاحتياطي التلقائي
        public static void StartAutoBackup()
        {
            try
            {
                if (!BackupSettings.AutoBackupEnabled)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ النسخ الاحتياطي التلقائي معطل");
                    return;
                }

                // إنشاء مجلد النسخ الاحتياطي
                CreateBackupDirectories();

                // إعداد المؤقت
                backupTimer = new Timer(BackupSettings.BackupIntervalMinutes * 60 * 1000); // تحويل إلى ميلي ثانية
                backupTimer.Elapsed += async (sender, e) => await PerformAutoBackup();
                backupTimer.AutoReset = true;
                backupTimer.Start();

                System.Diagnostics.Debug.WriteLine($"✅ تم بدء النسخ الاحتياطي التلقائي - كل {BackupSettings.BackupIntervalMinutes} دقيقة");

                // عمل نسخة احتياطية فورية
                Task.Run(async () => await PerformAutoBackup());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في بدء النسخ الاحتياطي التلقائي: {ex.Message}");
                NotificationManager.SendErrorNotification("خطأ في النسخ الاحتياطي", ex.Message);
            }
        }

        // إيقاف النسخ الاحتياطي التلقائي
        public static void StopAutoBackup()
        {
            try
            {
                if (backupTimer != null)
                {
                    backupTimer.Stop();
                    backupTimer.Dispose();
                    backupTimer = null;
                    System.Diagnostics.Debug.WriteLine("✅ تم إيقاف النسخ الاحتياطي التلقائي");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إيقاف النسخ الاحتياطي: {ex.Message}");
            }
        }

        // تنفيذ النسخ الاحتياطي التلقائي
        private static async Task PerformAutoBackup()
        {
            if (isBackupRunning)
            {
                System.Diagnostics.Debug.WriteLine("⚠️ النسخ الاحتياطي قيد التشغيل بالفعل");
                return;
            }

            isBackupRunning = true;

            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء النسخ الاحتياطي التلقائي...");

                string timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
                string backupName = $"HR_Backup_{timestamp}";

                // نسخ احتياطي محلي
                await CreateLocalBackup(backupName);

                // نسخ احتياطي على USB إذا كان متاحاً
                if (BackupSettings.BackupToUSB)
                {
                    await CreateUSBBackup(backupName);
                }

                // تنظيف النسخ القديمة
                CleanOldBackups();

                System.Diagnostics.Debug.WriteLine("✅ تم إنجاز النسخ الاحتياطي التلقائي بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في النسخ الاحتياطي التلقائي: {ex.Message}");
                NotificationManager.SendErrorNotification("فشل النسخ الاحتياطي", ex.Message);
            }
            finally
            {
                isBackupRunning = false;
            }
        }

        // إنشاء نسخة احتياطية محلية
        private static async Task CreateLocalBackup(string backupName)
        {
            try
            {
                string backupPath = Path.Combine(backupFolder, backupName);
                Directory.CreateDirectory(backupPath);

                // نسخ ملفات قاعدة البيانات
                await CopyDatabaseFiles(backupPath);

                // ضغط النسخة الاحتياطية إذا كان مطلوباً
                if (BackupSettings.CompressBackups)
                {
                    string zipPath = $"{backupPath}.zip";
                    ZipFile.CreateFromDirectory(backupPath, zipPath);
                    Directory.Delete(backupPath, true);
                    System.Diagnostics.Debug.WriteLine($"✅ تم ضغط النسخة الاحتياطية: {zipPath}");
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء نسخة احتياطية محلية: {backupName}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في النسخة الاحتياطية المحلية: {ex.Message}");
                throw;
            }
        }

        // إنشاء نسخة احتياطية على USB
        private static async Task CreateUSBBackup(string backupName)
        {
            try
            {
                var usbDrives = GetUSBDrives();
                if (usbDrives.Length == 0)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد أجهزة USB متاحة للنسخ الاحتياطي");
                    return;
                }

                foreach (var usbDrive in usbDrives)
                {
                    try
                    {
                        string usbBackupPath = Path.Combine(usbDrive.RootDirectory.FullName, usbBackupFolder, backupName);
                        Directory.CreateDirectory(usbBackupPath);

                        await CopyDatabaseFiles(usbBackupPath);

                        if (BackupSettings.CompressBackups)
                        {
                            string zipPath = $"{usbBackupPath}.zip";
                            ZipFile.CreateFromDirectory(usbBackupPath, zipPath);
                            Directory.Delete(usbBackupPath, true);
                        }

                        System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء نسخة احتياطية على USB: {usbDrive.Name}");
                        break; // نجح النسخ على أول USB متاح
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ فشل النسخ على USB {usbDrive.Name}: {ex.Message}");
                        continue; // جرب USB التالي
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في النسخة الاحتياطية على USB: {ex.Message}");
            }
        }

        // نسخ ملفات قاعدة البيانات
        private static async Task CopyDatabaseFiles(string destinationPath)
        {
            try
            {
                var filesToBackup = new[]
                {
                    "HRManagement.accdb",
                    "hr_management.db",
                    "users.db"
                };

                foreach (var fileName in filesToBackup)
                {
                    if (File.Exists(fileName))
                    {
                        string destFile = Path.Combine(destinationPath, fileName);
                        await Task.Run(() => File.Copy(fileName, destFile, true));
                        System.Diagnostics.Debug.WriteLine($"✅ تم نسخ: {fileName}");
                    }
                }

                // نسخ مجلد ملفات الموظفين إذا كان موجوداً
                string employeeFilesFolder = "EmployeeFiles";
                if (Directory.Exists(employeeFilesFolder))
                {
                    string destFolder = Path.Combine(destinationPath, employeeFilesFolder);
                    await Task.Run(() => CopyDirectory(employeeFilesFolder, destFolder));
                    System.Diagnostics.Debug.WriteLine($"✅ تم نسخ مجلد: {employeeFilesFolder}");
                }

                // إنشاء ملف معلومات النسخة الاحتياطية
                string infoFile = Path.Combine(destinationPath, "backup_info.txt");
                string backupInfo = $"تاريخ النسخة الاحتياطية: {DateTime.Now}\n" +
                                   $"إصدار النظام: v4.3.0\n" +
                                   $"نوع النسخة: تلقائية\n" +
                                   $"الملفات المنسوخة: {string.Join(", ", filesToBackup)}";
                
                await File.WriteAllTextAsync(infoFile, backupInfo);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ ملفات قاعدة البيانات: {ex.Message}");
                throw;
            }
        }

        // نسخ مجلد بالكامل
        private static void CopyDirectory(string sourceDir, string destDir)
        {
            Directory.CreateDirectory(destDir);

            foreach (string file in Directory.GetFiles(sourceDir))
            {
                string destFile = Path.Combine(destDir, Path.GetFileName(file));
                File.Copy(file, destFile, true);
            }

            foreach (string subDir in Directory.GetDirectories(sourceDir))
            {
                string destSubDir = Path.Combine(destDir, Path.GetFileName(subDir));
                CopyDirectory(subDir, destSubDir);
            }
        }

        // الحصول على أجهزة USB المتاحة
        private static DriveInfo[] GetUSBDrives()
        {
            try
            {
                return Array.FindAll(DriveInfo.GetDrives(), 
                    drive => drive.DriveType == DriveType.Removable && drive.IsReady);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن أجهزة USB: {ex.Message}");
                return new DriveInfo[0];
            }
        }

        // إنشاء مجلدات النسخ الاحتياطي
        private static void CreateBackupDirectories()
        {
            try
            {
                if (!Directory.Exists(backupFolder))
                {
                    Directory.CreateDirectory(backupFolder);
                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء مجلد النسخ الاحتياطي: {backupFolder}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء مجلدات النسخ الاحتياطي: {ex.Message}");
                throw;
            }
        }

        // تنظيف النسخ القديمة
        private static void CleanOldBackups()
        {
            try
            {
                CleanOldBackupsInDirectory(backupFolder);

                // تنظيف النسخ على USB
                var usbDrives = GetUSBDrives();
                foreach (var usbDrive in usbDrives)
                {
                    string usbBackupPath = Path.Combine(usbDrive.RootDirectory.FullName, usbBackupFolder);
                    if (Directory.Exists(usbBackupPath))
                    {
                        CleanOldBackupsInDirectory(usbBackupPath);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنظيف النسخ القديمة: {ex.Message}");
            }
        }

        // تنظيف النسخ القديمة في مجلد محدد
        private static void CleanOldBackupsInDirectory(string directory)
        {
            try
            {
                if (!Directory.Exists(directory)) return;

                var backupFiles = new List<FileSystemInfo>();
                
                // إضافة الملفات المضغوطة
                backupFiles.AddRange(new DirectoryInfo(directory).GetFiles("HR_Backup_*.zip"));
                
                // إضافة المجلدات
                backupFiles.AddRange(new DirectoryInfo(directory).GetDirectories("HR_Backup_*"));

                // ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
                backupFiles = backupFiles.OrderByDescending(f => f.CreationTime).ToList();

                // حذف النسخ الزائدة
                for (int i = BackupSettings.MaxBackupFiles; i < backupFiles.Count; i++)
                {
                    try
                    {
                        if (backupFiles[i] is FileInfo file)
                        {
                            file.Delete();
                        }
                        else if (backupFiles[i] is DirectoryInfo dir)
                        {
                            dir.Delete(true);
                        }
                        
                        System.Diagnostics.Debug.WriteLine($"🗑️ تم حذف نسخة احتياطية قديمة: {backupFiles[i].Name}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ فشل حذف النسخة القديمة {backupFiles[i].Name}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنظيف المجلد {directory}: {ex.Message}");
            }
        }

        // نسخة احتياطية يدوية
        public static async Task<bool> CreateManualBackup(string? customName = null)
        {
            try
            {
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
                string backupName = customName ?? $"HR_Manual_Backup_{timestamp}";

                await CreateLocalBackup(backupName);

                if (BackupSettings.BackupToUSB)
                {
                    await CreateUSBBackup(backupName);
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء نسخة احتياطية يدوية: {backupName}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل النسخة الاحتياطية اليدوية: {ex.Message}");
                NotificationManager.SendErrorNotification("فشل النسخة الاحتياطية اليدوية", ex.Message);
                return false;
            }
        }

        // استعادة من نسخة احتياطية
        public static async Task<bool> RestoreFromBackup(string backupPath)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 بدء استعادة النسخة الاحتياطية من: {backupPath}");

                // إيقاف النسخ الاحتياطي التلقائي مؤقتاً
                StopAutoBackup();

                // استعادة الملفات
                if (backupPath.EndsWith(".zip"))
                {
                    // استخراج الملف المضغوط
                    string tempDir = Path.Combine(Path.GetTempPath(), "HR_Restore_" + Guid.NewGuid().ToString("N")[..8]);
                    ZipFile.ExtractToDirectory(backupPath, tempDir);
                    
                    await RestoreFiles(tempDir);
                    
                    Directory.Delete(tempDir, true);
                }
                else
                {
                    await RestoreFiles(backupPath);
                }

                // إعادة بدء النسخ الاحتياطي التلقائي
                StartAutoBackup();

                System.Diagnostics.Debug.WriteLine("✅ تم استعادة النسخة الاحتياطية بنجاح");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل استعادة النسخة الاحتياطية: {ex.Message}");
                NotificationManager.SendErrorNotification("فشل استعادة النسخة الاحتياطية", ex.Message);
                
                // إعادة بدء النسخ الاحتياطي التلقائي حتى لو فشلت الاستعادة
                StartAutoBackup();
                return false;
            }
        }

        // استعادة الملفات
        private static async Task RestoreFiles(string sourcePath)
        {
            var filesToRestore = new[]
            {
                "HRManagement.accdb",
                "hr_management.db",
                "users.db"
            };

            foreach (var fileName in filesToRestore)
            {
                string sourceFile = Path.Combine(sourcePath, fileName);
                if (File.Exists(sourceFile))
                {
                    await Task.Run(() => File.Copy(sourceFile, fileName, true));
                    System.Diagnostics.Debug.WriteLine($"✅ تم استعادة: {fileName}");
                }
            }

            // استعادة مجلد ملفات الموظفين
            string sourceEmployeeFiles = Path.Combine(sourcePath, "EmployeeFiles");
            if (Directory.Exists(sourceEmployeeFiles))
            {
                if (Directory.Exists("EmployeeFiles"))
                {
                    Directory.Delete("EmployeeFiles", true);
                }
                await Task.Run(() => CopyDirectory(sourceEmployeeFiles, "EmployeeFiles"));
                System.Diagnostics.Debug.WriteLine("✅ تم استعادة مجلد ملفات الموظفين");
            }
        }

        // الحصول على قائمة النسخ الاحتياطية المتاحة
        public static List<BackupInfo> GetAvailableBackups()
        {
            var backups = new List<BackupInfo>();

            try
            {
                // النسخ المحلية
                if (Directory.Exists(backupFolder))
                {
                    AddBackupsFromDirectory(backups, backupFolder, "محلي");
                }

                // النسخ على USB
                var usbDrives = GetUSBDrives();
                foreach (var usbDrive in usbDrives)
                {
                    string usbBackupPath = Path.Combine(usbDrive.RootDirectory.FullName, usbBackupFolder);
                    if (Directory.Exists(usbBackupPath))
                    {
                        AddBackupsFromDirectory(backups, usbBackupPath, $"USB ({usbDrive.Name})");
                    }
                }

                return backups.OrderByDescending(b => b.CreationTime).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب النسخ الاحتياطية: {ex.Message}");
                return backups;
            }
        }

        // إضافة النسخ الاحتياطية من مجلد
        private static void AddBackupsFromDirectory(List<BackupInfo> backups, string directory, string location)
        {
            try
            {
                var dirInfo = new DirectoryInfo(directory);

                // إضافة الملفات المضغوطة
                foreach (var file in dirInfo.GetFiles("HR_*Backup_*.zip"))
                {
                    backups.Add(new BackupInfo
                    {
                        Name = Path.GetFileNameWithoutExtension(file.Name),
                        Path = file.FullName,
                        CreationTime = file.CreationTime,
                        Size = file.Length,
                        Location = location,
                        IsCompressed = true
                    });
                }

                // إضافة المجلدات
                foreach (var dir in dirInfo.GetDirectories("HR_*Backup_*"))
                {
                    long size = GetDirectorySize(dir.FullName);
                    backups.Add(new BackupInfo
                    {
                        Name = dir.Name,
                        Path = dir.FullName,
                        CreationTime = dir.CreationTime,
                        Size = size,
                        Location = location,
                        IsCompressed = false
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة النسخ من {directory}: {ex.Message}");
            }
        }

        // حساب حجم المجلد
        private static long GetDirectorySize(string directory)
        {
            try
            {
                return new DirectoryInfo(directory)
                    .GetFiles("*", SearchOption.AllDirectories)
                    .Sum(file => file.Length);
            }
            catch
            {
                return 0;
            }
        }
    }

    // معلومات النسخة الاحتياطية
    public class BackupInfo
    {
        public string Name { get; set; } = "";
        public string Path { get; set; } = "";
        public DateTime CreationTime { get; set; }
        public long Size { get; set; }
        public string Location { get; set; } = "";
        public bool IsCompressed { get; set; }

        public string SizeFormatted => FormatFileSize(Size);
        public string CreationTimeFormatted => CreationTime.ToString("yyyy-MM-dd HH:mm:ss");

        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
