@echo off
title HR System - Window Diagnosis
color 0E

echo.
echo ========================================
echo    HR Management System v5.3.0
echo    Window Display Diagnosis
echo ========================================
echo.

cd /d "%~dp0"

echo Checking system requirements...
echo.

echo 1. Checking .NET version:
dotnet --version
echo.

echo 2. Checking Windows Forms support:
dotnet --list-runtimes | findstr "WindowsDesktop"
echo.

echo 3. Building application:
dotnet build --verbosity quiet

if errorlevel 1 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo ✅ Build successful
echo.

echo 4. Starting application with console output:
echo.
echo Looking for these messages:
echo - "Starting HR Management System..."
echo - "Application configuration initialized"
echo - "About to show MinimalTest..."
echo.
echo If you see these messages but no window appears:
echo - Check taskbar (bottom of screen)
echo - Press Alt+Tab to cycle through windows
echo - Check if window is behind other windows
echo - Look in Task Manager for "Ahmedapp for work"
echo.

echo Starting now...
echo.

dotnet run

echo.
echo Application process ended.
echo.

echo 5. Checking if process is still running:
tasklist | findstr "Ahmedapp"

echo.
echo Diagnosis complete.
pause
