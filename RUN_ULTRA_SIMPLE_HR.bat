@echo off
title Ultra Simple HR Management System v5.3.0
color 0A

echo.
echo ========================================
echo    Ultra Simple HR Management System
echo    نظام إدارة الموارد البشرية المبسط
echo    v5.3.0 - Final Working Version
echo ========================================
echo.

echo Building HR Management System...
dotnet build UltraSimpleHR.csproj --verbosity quiet

if errorlevel 1 (
    echo ❌ Build failed. Check .NET installation.
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.
echo Starting HR Management System...
echo.
echo 🎯 FEATURES AVAILABLE:
echo • 👥 Complete employee management (4 sample employees)
echo • ➕ Add employees with full validation
echo • 🔍 Advanced search functionality
echo • 📊 Comprehensive reports and statistics
echo • ✏️ Edit employee information
echo • 🗑️ Safe employee deletion with confirmation
echo • 💾 Backup and restore data
echo • 📤 Export to CSV format
echo • 📖 Complete help and documentation
echo • 🔄 Real-time data refresh
echo.
echo 📱 LOOK FOR THESE WINDOWS:
echo 1. 📱 Welcome message - Click OK to continue
echo 2. 🏠 Main HR window with 10 functional buttons
echo 3. 📋 Employee list, forms, and reports
echo.
echo 💾 DATA STORAGE:
echo • File: hr_data.txt (automatically created)
echo • Format: Text file with pipe separation
echo • Backup: Available through backup button
echo.

dotnet run --project UltraSimpleHR.csproj

echo.
echo Application finished.
pause
