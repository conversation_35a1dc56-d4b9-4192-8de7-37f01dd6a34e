@echo off
title HR System - Direct Compile
color 0B

echo.
echo ========================================
echo    HR Management System
echo    Direct Compilation Method
echo ========================================
echo.

echo Method 1: Using C# Compiler directly...
echo.

where csc >nul 2>&1
if errorlevel 1 (
    echo C# compiler not found in PATH. Adding Visual Studio paths...
    
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe" (
        set "CSC=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\Roslyn\csc.exe" (
        set "CSC=C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\Roslyn\csc.exe"
    ) else if exist "C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe" (
        set "CSC=C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
    ) else (
        echo C# compiler not found. Trying dotnet method...
        goto DOTNET_METHOD
    )
    
    echo Found C# compiler: %CSC%
    "%CSC%" /target:winexe /reference:System.Windows.Forms.dll /reference:System.Drawing.dll SimpleHR.cs
) else (
    echo Using system C# compiler...
    csc /target:winexe /reference:System.Windows.Forms.dll /reference:System.Drawing.dll SimpleHR.cs
)

if errorlevel 1 (
    echo Compilation failed. Trying dotnet method...
    goto DOTNET_METHOD
)

echo.
echo Compilation successful! Running application...
echo.
echo IMPORTANT: Look for these windows:
echo 1. Welcome message box
echo 2. Main HR system window
echo.

SimpleHR.exe
del SimpleHR.exe
goto END

:DOTNET_METHOD
echo.
echo Method 2: Using dotnet...
dotnet run --project SimpleHR.csproj

:END
echo.
echo Application finished.
pause
