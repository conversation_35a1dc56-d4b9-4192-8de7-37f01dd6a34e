using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;

namespace Ahmedapp_for_work
{
    public class NoteManager
    {
        private readonly DatabaseManager dbManager;

        public NoteManager()
        {
            dbManager = new DatabaseManager();
        }

        public List<Note> GetAllNotes()
        {
            var notes = new List<Note>();
            try
            {
                string query = @"SELECT * FROM Notes ORDER BY CreatedDate DESC";
                DataTable dt = dbManager.ExecuteQuery(query);
                foreach (DataRow row in dt.Rows)
                {
                    notes.Add(MapRowToNote(row));
                }
            }
            catch (Exception)
            {
                // في حالة عدم وجود قاعدة البيانات، إرجاع بيانات تجريبية
                notes.AddRange(GetSampleNotes());
            }
            return notes;
        }

        public List<Note> GetNotesByCategory(string category)
        {
            var notes = new List<Note>();
            try
            {
                string query = @"SELECT * FROM Notes WHERE Category = @Category ORDER BY CreatedDate DESC";
                SqlParameter[] parameters = { new SqlParameter("@Category", category) };
                DataTable dt = dbManager.ExecuteQuery(query, parameters);
                foreach (DataRow row in dt.Rows)
                {
                    notes.Add(MapRowToNote(row));
                }
            }
            catch
            {
                notes.AddRange(GetSampleNotes().FindAll(n => n.Category == category));
            }
            return notes;
        }

        public bool AddNote(Note note)
        {
            try
            {
                string query = @"INSERT INTO Notes (Title, Content, Category, RelatedEntityType, RelatedEntityID, IsPrivate, CreatedBy, Tags)
                               VALUES (@Title, @Content, @Category, @RelatedEntityType, @RelatedEntityID, @IsPrivate, @CreatedBy, @Tags)";

                SqlParameter[] parameters = {
                    new SqlParameter("@Title", note.Title),
                    new SqlParameter("@Content", note.Content),
                    new SqlParameter("@Category", note.Category),
                    new SqlParameter("@RelatedEntityType", note.RelatedEntityType),
                    new SqlParameter("@RelatedEntityID", note.RelatedEntityID.HasValue ? (object)note.RelatedEntityID.Value : DBNull.Value),
                    new SqlParameter("@IsPrivate", note.IsPrivate),
                    new SqlParameter("@CreatedBy", note.CreatedBy),
                    new SqlParameter("@Tags", note.Tags)
                };

                return dbManager.ExecuteNonQuery(query, parameters) > 0;
            }
            catch
            {
                return false;
            }
        }

        public bool UpdateNote(Note note)
        {
            try
            {
                string query = @"UPDATE Notes SET Title = @Title, Content = @Content, Category = @Category,
                               RelatedEntityType = @RelatedEntityType, RelatedEntityID = @RelatedEntityID,
                               IsPrivate = @IsPrivate, Tags = @Tags, LastModified = GETDATE()
                               WHERE NoteID = @NoteID";

                SqlParameter[] parameters = {
                    new SqlParameter("@NoteID", note.NoteID),
                    new SqlParameter("@Title", note.Title),
                    new SqlParameter("@Content", note.Content),
                    new SqlParameter("@Category", note.Category),
                    new SqlParameter("@RelatedEntityType", note.RelatedEntityType),
                    new SqlParameter("@RelatedEntityID", note.RelatedEntityID.HasValue ? (object)note.RelatedEntityID.Value : DBNull.Value),
                    new SqlParameter("@IsPrivate", note.IsPrivate),
                    new SqlParameter("@Tags", note.Tags)
                };

                return dbManager.ExecuteNonQuery(query, parameters) > 0;
            }
            catch
            {
                return false;
            }
        }

        public bool DeleteNote(int noteId)
        {
            try
            {
                string query = "DELETE FROM Notes WHERE NoteID = @NoteID";
                SqlParameter[] parameters = { new SqlParameter("@NoteID", noteId) };
                return dbManager.ExecuteNonQuery(query, parameters) > 0;
            }
            catch
            {
                return false;
            }
        }

        private Note MapRowToNote(DataRow row)
        {
            return new Note
            {
                NoteID = Convert.ToInt32(row["NoteID"]),
                Title = row["Title"].ToString() ?? "",
                Content = row["Content"].ToString() ?? "",
                Category = row["Category"].ToString() ?? "General",
                RelatedEntityType = row["RelatedEntityType"].ToString() ?? "",
                RelatedEntityID = row["RelatedEntityID"] == DBNull.Value ? null : Convert.ToInt32(row["RelatedEntityID"]),
                IsPrivate = Convert.ToBoolean(row["IsPrivate"]),
                CreatedBy = row["CreatedBy"].ToString() ?? "",
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                LastModified = Convert.ToDateTime(row["LastModified"]),
                Tags = row["Tags"].ToString() ?? ""
            };
        }

        private List<Note> GetSampleNotes()
        {
            return new List<Note>
            {
                new Note
                {
                    NoteID = 1,
                    Title = "ملاحظة حول الموظف أحمد",
                    Content = "موظف متميز ويحتاج إلى دورات تدريبية إضافية في الذكاء الاصطناعي",
                    Category = "Employee",
                    RelatedEntityType = "Employee",
                    RelatedEntityID = 1,
                    CreatedBy = "مدير الموارد البشرية",
                    Tags = "تدريب,ذكاء اصطناعي,موظف متميز"
                },
                new Note
                {
                    NoteID = 2,
                    Title = "تحديث إجراءات الإجازات",
                    Content = "يجب مراجعة إجراءات الإجازات بدون مرتب وتحديث النماذج المطلوبة لتسهيل العملية على الموظفين",
                    Category = "Process",
                    RelatedEntityType = "Leave",
                    CreatedBy = "مدير العمليات",
                    Tags = "إجراءات,إجازات,تحديث"
                },
                new Note
                {
                    NoteID = 3,
                    Title = "تذكير مهم - ملفات إصابات العمل",
                    Content = "مراجعة ملفات إصابات العمل شهرياً وتحديث حالة العلاج والمتابعة مع المصابين",
                    Category = "Reminder",
                    RelatedEntityType = "Injury",
                    CreatedBy = "مسؤول السلامة",
                    Tags = "إصابات,متابعة,تذكير شهري"
                }
            };
        }
    }

    public class TipManager
    {
        private readonly DatabaseManager dbManager;

        public TipManager()
        {
            dbManager = new DatabaseManager();
        }

        public List<Tip> GetActiveTips()
        {
            var tips = new List<Tip>();
            try
            {
                string query = @"SELECT * FROM Tips WHERE IsActive = 1 ORDER BY DisplayOrder, CreatedDate DESC";
                DataTable dt = dbManager.ExecuteQuery(query);
                foreach (DataRow row in dt.Rows)
                {
                    tips.Add(MapRowToTip(row));
                }
            }
            catch
            {
                tips.AddRange(GetSampleTips());
            }
            return tips;
        }

        public List<Tip> GetTipsByCategory(string category)
        {
            var tips = new List<Tip>();
            try
            {
                string query = @"SELECT * FROM Tips WHERE Category = @Category AND IsActive = 1 ORDER BY DisplayOrder, CreatedDate DESC";
                SqlParameter[] parameters = { new SqlParameter("@Category", category) };
                DataTable dt = dbManager.ExecuteQuery(query, parameters);
                foreach (DataRow row in dt.Rows)
                {
                    tips.Add(MapRowToTip(row));
                }
            }
            catch
            {
                tips.AddRange(GetSampleTips().FindAll(t => t.Category == category));
            }
            return tips;
        }

        public bool AddTip(Tip tip)
        {
            try
            {
                string query = @"INSERT INTO Tips (Title, Content, Category, TipType, IsActive, DisplayOrder, CreatedBy)
                               VALUES (@Title, @Content, @Category, @TipType, @IsActive, @DisplayOrder, @CreatedBy)";

                SqlParameter[] parameters = {
                    new SqlParameter("@Title", tip.Title),
                    new SqlParameter("@Content", tip.Content),
                    new SqlParameter("@Category", tip.Category),
                    new SqlParameter("@TipType", tip.TipType),
                    new SqlParameter("@IsActive", tip.IsActive),
                    new SqlParameter("@DisplayOrder", tip.DisplayOrder),
                    new SqlParameter("@CreatedBy", tip.CreatedBy)
                };

                return dbManager.ExecuteNonQuery(query, parameters) > 0;
            }
            catch
            {
                return false;
            }
        }

        private Tip MapRowToTip(DataRow row)
        {
            return new Tip
            {
                TipID = Convert.ToInt32(row["TipID"]),
                Title = row["Title"].ToString() ?? "",
                Content = row["Content"].ToString() ?? "",
                Category = row["Category"].ToString() ?? "General",
                TipType = row["TipType"].ToString() ?? "Tip",
                IsActive = Convert.ToBoolean(row["IsActive"]),
                DisplayOrder = Convert.ToInt32(row["DisplayOrder"]),
                CreatedBy = row["CreatedBy"].ToString() ?? "",
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                LastModified = Convert.ToDateTime(row["LastModified"])
            };
        }

        private List<Tip> GetSampleTips()
        {
            return new List<Tip>
            {
                new Tip
                {
                    TipID = 1,
                    Title = "إدارة الموظفين بفعالية",
                    Content = "تأكد من تحديث بيانات الموظفين بانتظام وإجراء تقييمات دورية لضمان الأداء الأمثل",
                    Category = "Employee Management",
                    TipType = "Best Practice",
                    CreatedBy = "مدير الموارد البشرية"
                },
                new Tip
                {
                    TipID = 2,
                    Title = "معالجة النماذج",
                    Content = "يجب مراجعة جميع النماذج خلال 48 ساعة من تاريخ التقديم لضمان سرعة الاستجابة",
                    Category = "Forms",
                    TipType = "Guideline",
                    CreatedBy = "مدير العمليات"
                },
                new Tip
                {
                    TipID = 3,
                    Title = "إدارة الإجازات",
                    Content = "تأكد من توثيق جميع الإجازات بدون مرتب مع المستندات المطلوبة قبل الموافقة",
                    Category = "Leave",
                    TipType = "Warning",
                    CreatedBy = "مدير الموارد البشرية"
                }
            };
        }
    }
}
