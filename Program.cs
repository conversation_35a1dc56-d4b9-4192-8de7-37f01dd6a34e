using System;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                Console.WriteLine("🚀 Starting HR Management System...");

                ApplicationConfiguration.Initialize();
                Console.WriteLine("✅ Application configuration initialized.");

                // اختبار بسيط أولاً
                Console.WriteLine("📋 About to show test message...");
                MessageBox.Show("HR Management System v5.3.0\n\nStarting application...",
                               "HR System", MessageBoxButtons.OK, MessageBoxIcon.Information);

                Console.WriteLine("🔧 Initializing database...");
                DatabaseHelper.CreateDatabaseAndTable();
                Console.WriteLine("✅ Database initialized.");

                // إضافة معالج للأخطاء غير المعالجة
                Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                Application.ThreadException += Application_ThreadException;
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

                Console.WriteLine("🔐 About to show SimpleTestForm...");
                Application.Run(new SimpleTestForm());

                Console.WriteLine("✅ Application closed normally.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"📋 Stack trace: {ex.StackTrace}");

                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}",
                              "خطأ في التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            MessageBox.Show($"خطأ في Thread: {e.Exception.Message}\n\nتفاصيل الخطأ:\n{e.Exception.StackTrace}",
                          "خطأ في التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Exception ex = (Exception)e.ExceptionObject;
            MessageBox.Show($"خطأ غير معالج: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}",
                          "خطأ في التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
