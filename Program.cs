using System;
using System.Drawing;
using System.Windows.Forms;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace HRSystem
{
    public class Employee
    {
        public string Name { get; set; } = "";
        public string NationalId { get; set; } = "";
        public string JobTitle { get; set; } = "";
        public string Department { get; set; } = "";
        public DateTime HireDate { get; set; }
        public string Phone { get; set; } = "";
        public decimal Salary { get; set; }
    }

    public class MainForm : Form
    {
        private List<Employee> employees = new List<Employee>();
        private string dataFile = "hr_employees.txt";

        public MainForm()
        {
            InitializeSystem();
            LoadEmployees();
            CreateInterface();
        }

        private void InitializeSystem()
        {
            this.Text = "نظام إدارة الموارد البشرية - HR Management System v5.3.0";
            this.Size = new Size(900, 650);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.MinimumSize = new Size(800, 600);
            this.Icon = SystemIcons.Application;
        }

        private void LoadEmployees()
        {
            if (File.Exists(dataFile))
            {
                try
                {
                    var lines = File.ReadAllLines(dataFile);
                    foreach (var line in lines)
                    {
                        var parts = line.Split('|');
                        if (parts.Length >= 7)
                        {
                            employees.Add(new Employee
                            {
                                Name = parts[0],
                                NationalId = parts[1],
                                JobTitle = parts[2],
                                Department = parts[3],
                                HireDate = DateTime.Parse(parts[4]),
                                Phone = parts[5],
                                Salary = decimal.Parse(parts[6])
                            });
                        }
                    }
                }
                catch { CreateSampleData(); }
            }
            else { CreateSampleData(); }
        }

        private void CreateSampleData()
        {
            employees.Clear();
            employees.Add(new Employee { Name = "أحمد محمد علي", NationalId = "12345678901", JobTitle = "مدير الموارد البشرية", Department = "الموارد البشرية", HireDate = DateTime.Now.AddYears(-2), Phone = "01234567890", Salary = 15000 });
            employees.Add(new Employee { Name = "فاطمة أحمد حسن", NationalId = "12345678902", JobTitle = "محاسب أول", Department = "المحاسبة", HireDate = DateTime.Now.AddYears(-1), Phone = "01234567891", Salary = 12000 });
            employees.Add(new Employee { Name = "محمد عبدالله سالم", NationalId = "12345678903", JobTitle = "مطور برمجيات", Department = "تقنية المعلومات", HireDate = DateTime.Now.AddMonths(-6), Phone = "01234567892", Salary = 18000 });
            employees.Add(new Employee { Name = "سارة محمود عبدالرحمن", NationalId = "12345678904", JobTitle = "مساعد إداري", Department = "الإدارة", HireDate = DateTime.Now.AddMonths(-3), Phone = "01234567893", Salary = 8000 });
            employees.Add(new Employee { Name = "خالد أحمد محمد", NationalId = "12345678905", JobTitle = "مهندس شبكات", Department = "تقنية المعلومات", HireDate = DateTime.Now.AddMonths(-8), Phone = "01234567894", Salary = 16000 });
            SaveEmployees();
        }

        private void SaveEmployees()
        {
            try
            {
                var lines = new List<string>();
                foreach (var emp in employees)
                {
                    lines.Add($"{emp.Name}|{emp.NationalId}|{emp.JobTitle}|{emp.Department}|{emp.HireDate:yyyy-MM-dd}|{emp.Phone}|{emp.Salary}");
                }
                File.WriteAllLines(dataFile, lines);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void CreateInterface()
        {
            // العنوان الرئيسي
            var title = new Label
            {
                Text = "نظام إدارة الموارد البشرية\nHR Management System v5.3.0",
                Font = new Font("Arial", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Size = new Size(850, 70),
                Location = new Point(25, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // الأزرار الرئيسية
            var btnList = CreateButton("👥 عرض الموظفين", 50, 120, ShowEmployeesList);
            var btnAdd = CreateButton("➕ إضافة موظف", 250, 120, AddEmployee);
            var btnSearch = CreateButton("🔍 البحث", 450, 120, SearchEmployees);
            var btnReports = CreateButton("📊 التقارير", 650, 120, ShowReports);

            var btnEdit = CreateButton("✏️ تعديل", 50, 200, EditEmployee);
            var btnDelete = CreateButton("🗑️ حذف", 250, 200, DeleteEmployee);
            var btnBackup = CreateButton("💾 نسخة احتياطية", 450, 200, CreateBackup);
            var btnExport = CreateButton("📤 تصدير", 650, 200, ExportData);

            var btnHelp = CreateButton("❓ المساعدة", 250, 280, ShowHelp);
            var btnExit = CreateButton("🚪 خروج", 450, 280, ExitApp);

            // معلومات النظام
            var info = new Label
            {
                Text = $"عدد الموظفين: {employees.Count} | آخر تحديث: {DateTime.Now:yyyy-MM-dd HH:mm}",
                Font = new Font("Arial", 12),
                ForeColor = Color.Green,
                Size = new Size(850, 30),
                Location = new Point(25, 380),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var status = new Label
            {
                Text = "النظام جاهز للاستخدام ✅ | System Ready",
                Font = new Font("Arial", 10),
                ForeColor = Color.Gray,
                Size = new Size(850, 25),
                Location = new Point(25, 420),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // إضافة جميع العناصر
            this.Controls.AddRange(new Control[] { title, btnList, btnAdd, btnSearch, btnReports, btnEdit, btnDelete, btnBackup, btnExport, btnHelp, btnExit, info, status });
        }

        private Button CreateButton(string text, int x, int y, EventHandler handler)
        {
            var btn = new Button
            {
                Text = text,
                Size = new Size(150, 60),
                Location = new Point(x, y),
                Font = new Font("Arial", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                UseVisualStyleBackColor = false
            };
            btn.Click += handler;
            return btn;
        }

        private void ShowEmployeesList(object? sender, EventArgs e)
        {
            var form = new Form { Text = "قائمة الموظفين", Size = new Size(900, 500), StartPosition = FormStartPosition.CenterParent };
            var list = new ListView { View = View.Details, FullRowSelect = true, GridLines = true, Dock = DockStyle.Fill };

            list.Columns.Add("الاسم", 150);
            list.Columns.Add("الرقم القومي", 120);
            list.Columns.Add("المنصب", 150);
            list.Columns.Add("القسم", 120);
            list.Columns.Add("تاريخ التعيين", 100);
            list.Columns.Add("الهاتف", 100);
            list.Columns.Add("الراتب", 80);

            foreach (var emp in employees)
            {
                var item = new ListViewItem(emp.Name);
                item.SubItems.Add(emp.NationalId);
                item.SubItems.Add(emp.JobTitle);
                item.SubItems.Add(emp.Department);
                item.SubItems.Add(emp.HireDate.ToString("yyyy-MM-dd"));
                item.SubItems.Add(emp.Phone);
                item.SubItems.Add(emp.Salary.ToString("N0"));
                list.Items.Add(item);
            }

            form.Controls.Add(list);
            form.ShowDialog();
        }

        private void AddEmployee(object? sender, EventArgs e)
        {
            var form = new Form { Text = "إضافة موظف جديد", Size = new Size(500, 400), StartPosition = FormStartPosition.CenterParent, FormBorderStyle = FormBorderStyle.FixedDialog };

            var nameLabel = new Label { Text = "الاسم:", Location = new Point(20, 30), Size = new Size(80, 25) };
            var nameText = new TextBox { Location = new Point(110, 30), Size = new Size(350, 25) };

            var idLabel = new Label { Text = "الرقم القومي:", Location = new Point(20, 70), Size = new Size(80, 25) };
            var idText = new TextBox { Location = new Point(110, 70), Size = new Size(350, 25) };

            var jobLabel = new Label { Text = "المنصب:", Location = new Point(20, 110), Size = new Size(80, 25) };
            var jobText = new TextBox { Location = new Point(110, 110), Size = new Size(350, 25) };

            var deptLabel = new Label { Text = "القسم:", Location = new Point(20, 150), Size = new Size(80, 25) };
            var deptCombo = new ComboBox { Location = new Point(110, 150), Size = new Size(350, 25), DropDownStyle = ComboBoxStyle.DropDownList };
            deptCombo.Items.AddRange(new[] { "الموارد البشرية", "المحاسبة", "تقنية المعلومات", "الإدارة", "المبيعات", "التسويق" });

            var phoneLabel = new Label { Text = "الهاتف:", Location = new Point(20, 190), Size = new Size(80, 25) };
            var phoneText = new TextBox { Location = new Point(110, 190), Size = new Size(350, 25) };

            var salaryLabel = new Label { Text = "الراتب:", Location = new Point(20, 230), Size = new Size(80, 25) };
            var salaryNum = new NumericUpDown { Location = new Point(110, 230), Size = new Size(350, 25), Maximum = 100000, Minimum = 1000, Value = 5000 };

            var saveBtn = new Button { Text = "حفظ", Location = new Point(110, 280), Size = new Size(100, 35), BackColor = Color.Green, ForeColor = Color.White };
            var cancelBtn = new Button { Text = "إلغاء", Location = new Point(230, 280), Size = new Size(100, 35), BackColor = Color.Gray, ForeColor = Color.White };

            saveBtn.Click += (s, ev) => {
                if (string.IsNullOrWhiteSpace(nameText.Text) || string.IsNullOrWhiteSpace(idText.Text))
                {
                    MessageBox.Show("يرجى ملء الحقول المطلوبة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (employees.Any(emp => emp.NationalId == idText.Text))
                {
                    MessageBox.Show("الرقم القومي موجود مسبقاً", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                employees.Add(new Employee
                {
                    Name = nameText.Text,
                    NationalId = idText.Text,
                    JobTitle = jobText.Text,
                    Department = deptCombo.Text,
                    Phone = phoneText.Text,
                    Salary = salaryNum.Value,
                    HireDate = DateTime.Now
                });

                SaveEmployees();
                MessageBox.Show("تم إضافة الموظف بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                form.Close();
                RefreshInfo();
            };

            cancelBtn.Click += (s, ev) => form.Close();

            form.Controls.AddRange(new Control[] { nameLabel, nameText, idLabel, idText, jobLabel, jobText, deptLabel, deptCombo, phoneLabel, phoneText, salaryLabel, salaryNum, saveBtn, cancelBtn });
            form.ShowDialog();
        }

        private void SearchEmployees(object? sender, EventArgs e)
        {
            var searchText = InputBox("ادخل كلمة البحث:", "البحث في الموظفين");
            if (string.IsNullOrWhiteSpace(searchText)) return;

            var results = employees.Where(e =>
                e.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                e.NationalId.Contains(searchText) ||
                e.JobTitle.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
                e.Department.Contains(searchText, StringComparison.OrdinalIgnoreCase)).ToList();

            if (results.Count == 0)
            {
                MessageBox.Show("لم يتم العثور على نتائج", "البحث", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var message = $"تم العثور على {results.Count} نتيجة:\n\n";
            foreach (var emp in results)
            {
                message += $"• {emp.Name} - {emp.JobTitle} - {emp.Department}\n";
            }

            MessageBox.Show(message, "نتائج البحث", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowReports(object? sender, EventArgs e)
        {
            var message = $"📊 تقرير الموظفين الشامل\n" +
                         $"========================\n\n" +
                         $"📈 إجمالي الموظفين: {employees.Count}\n\n";

            var departments = employees.GroupBy(e => e.Department).ToDictionary(g => g.Key, g => g.Count());

            message += "📋 توزيع الموظفين حسب الأقسام:\n";
            foreach (var dept in departments.OrderByDescending(d => d.Value))
            {
                message += $"• {dept.Key}: {dept.Value} موظف\n";
            }

            message += $"\n💰 إحصائيات الرواتب:\n";
            message += $"• متوسط الراتب: {employees.Average(e => e.Salary):N0} جنيه\n";
            message += $"• أعلى راتب: {employees.Max(e => e.Salary):N0} جنيه\n";
            message += $"• أقل راتب: {employees.Min(e => e.Salary):N0} جنيه\n";
            message += $"• إجمالي الرواتب: {employees.Sum(e => e.Salary):N0} جنيه\n";

            MessageBox.Show(message, "تقرير الموظفين", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void EditEmployee(object? sender, EventArgs e)
        {
            var searchText = InputBox("ادخل الرقم القومي للموظف المراد تعديله:", "تعديل موظف");
            if (string.IsNullOrWhiteSpace(searchText)) return;

            var employee = employees.FirstOrDefault(emp => emp.NationalId == searchText);
            if (employee == null)
            {
                MessageBox.Show("لم يتم العثور على الموظف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show($"تعديل بيانات الموظف: {employee.Name}\n(هذه الوظيفة قيد التطوير)", "تعديل موظف", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteEmployee(object? sender, EventArgs e)
        {
            var searchText = InputBox("ادخل الرقم القومي للموظف المراد حذفه:", "حذف موظف");
            if (string.IsNullOrWhiteSpace(searchText)) return;

            var employee = employees.FirstOrDefault(emp => emp.NationalId == searchText);
            if (employee == null)
            {
                MessageBox.Show("لم يتم العثور على الموظف", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show($"هل أنت متأكد من حذف الموظف:\n{employee.Name}\n{employee.JobTitle}؟",
                                       "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                employees.Remove(employee);
                SaveEmployees();
                MessageBox.Show("تم حذف الموظف بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                RefreshInfo();
            }
        }

        private void CreateBackup(object? sender, EventArgs e)
        {
            try
            {
                var backupFile = $"backup_hr_{DateTime.Now:yyyyMMdd_HHmmss}.txt";
                File.Copy(dataFile, backupFile, true);
                MessageBox.Show($"تم إنشاء نسخة احتياطية بنجاح!\n\nاسم الملف: {backupFile}\nعدد الموظفين: {employees.Count}",
                               "نسخة احتياطية", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportData(object? sender, EventArgs e)
        {
            try
            {
                var exportFile = $"export_employees_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                var csvLines = new List<string>();
                csvLines.Add("الاسم,الرقم القومي,المنصب,القسم,تاريخ التعيين,الهاتف,الراتب");

                foreach (var emp in employees)
                {
                    csvLines.Add($"{emp.Name},{emp.NationalId},{emp.JobTitle},{emp.Department},{emp.HireDate:yyyy-MM-dd},{emp.Phone},{emp.Salary}");
                }

                File.WriteAllLines(exportFile, csvLines);
                MessageBox.Show($"تم تصدير البيانات بنجاح!\n\nاسم الملف: {exportFile}\nعدد الموظفين: {employees.Count}",
                               "تصدير البيانات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowHelp(object? sender, EventArgs e)
        {
            var message = "📖 دليل استخدام نظام إدارة الموارد البشرية\n" +
                         "=======================================\n\n" +
                         "🎯 الوظائف الرئيسية:\n" +
                         "👥 عرض الموظفين - عرض قائمة جميع الموظفين\n" +
                         "➕ إضافة موظف - إضافة موظف جديد للنظام\n" +
                         "🔍 البحث - البحث في بيانات الموظفين\n" +
                         "📊 التقارير - عرض تقارير وإحصائيات شاملة\n" +
                         "✏️ تعديل - تعديل بيانات موظف موجود\n" +
                         "🗑️ حذف - حذف موظف من النظام\n" +
                         "💾 نسخة احتياطية - حفظ نسخة من البيانات\n" +
                         "📤 تصدير - تصدير البيانات لملف CSV\n\n" +
                         "💡 نصائح:\n" +
                         "• استخدم الرقم القومي للبحث والتعديل\n" +
                         "• قم بعمل نسخة احتياطية دورياً\n" +
                         "• تأكد من صحة البيانات قبل الحفظ\n\n" +
                         "📞 للدعم التقني: <EMAIL>\n" +
                         "© 2024 جميع الحقوق محفوظة";

            MessageBox.Show(message, "دليل الاستخدام", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ExitApp(object? sender, EventArgs e)
        {
            var result = MessageBox.Show("هل تريد الخروج من نظام إدارة الموارد البشرية؟",
                                       "تأكيد الخروج", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void RefreshInfo()
        {
            foreach (Control control in this.Controls)
            {
                if (control is Label label && label.Text.Contains("عدد الموظفين"))
                {
                    label.Text = $"عدد الموظفين: {employees.Count} | آخر تحديث: {DateTime.Now:yyyy-MM-dd HH:mm}";
                    break;
                }
            }
        }

        private string InputBox(string prompt, string title)
        {
            var form = new Form
            {
                Text = title,
                Size = new Size(400, 150),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var label = new Label { Text = prompt, Location = new Point(20, 20), Size = new Size(350, 25) };
            var textBox = new TextBox { Location = new Point(20, 50), Size = new Size(350, 25) };
            var okButton = new Button { Text = "موافق", Location = new Point(200, 80), Size = new Size(80, 30), DialogResult = DialogResult.OK };
            var cancelButton = new Button { Text = "إلغاء", Location = new Point(290, 80), Size = new Size(80, 30), DialogResult = DialogResult.Cancel };

            form.Controls.AddRange(new Control[] { label, textBox, okButton, cancelButton });
            form.AcceptButton = okButton;
            form.CancelButton = cancelButton;

            return form.ShowDialog() == DialogResult.OK ? textBox.Text : "";
        }
    }

    class Program
    {
        [STAThread]
        static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                MessageBox.Show(
                    "🎉 مرحباً بك في نظام إدارة الموارد البشرية\n\n" +
                    "Welcome to HR Management System v5.3.0\n\n" +
                    "✅ النظام جاهز للاستخدام!\n" +
                    "✅ System is ready to use!\n\n" +
                    "المزايا المتاحة:\n" +
                    "• إدارة شاملة للموظفين (5 موظفين تجريبيين)\n" +
                    "• تقارير وإحصائيات متقدمة\n" +
                    "• بحث سريع ومتقدم\n" +
                    "• نسخ احتياطية آمنة\n" +
                    "• تصدير البيانات CSV\n" +
                    "• واجهة سهلة الاستخدام\n\n" +
                    "🚀 النظام لا يحتاج مكتبات خارجية!",
                    "نظام إدارة الموارد البشرية",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);

                Application.Run(new MainForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل النظام: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
