using System;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                Console.WriteLine("🚀 Starting HR Management System...");
                Console.WriteLine($"📅 Current time: {DateTime.Now}");
                Console.WriteLine($"💻 OS: {Environment.OSVersion}");
                Console.WriteLine($"🔧 .NET Version: {Environment.Version}");

                Console.WriteLine("📋 Initializing application configuration...");
                ApplicationConfiguration.Initialize();
                Console.WriteLine("✅ Application configuration initialized.");

                Console.WriteLine("📋 About to show test MessageBox...");
                var result = MessageBox.Show("HR Management System v5.3.0\n\nTest MessageBox - Can you see this?\n\nClick Yes if you can see this message.",
                               "HR System Test", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                Console.WriteLine($"✅ MessageBox result: {result}");

                Console.WriteLine("🔧 Skipping database initialization for test...");
                // DatabaseHelper.CreateDatabaseAndTable();
                Console.WriteLine("✅ Database step skipped.");

                // إضافة معالج للأخطاء غير المعالجة
                Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                Application.ThreadException += Application_ThreadException;
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

                Console.WriteLine("🔐 Creating MinimalTest form...");
                var testForm = new MinimalTest();
                Console.WriteLine("✅ MinimalTest form created.");

                Console.WriteLine("📱 About to show MinimalTest form...");
                Console.WriteLine("👀 Look for a green window on your screen!");

                Application.Run(testForm);

                Console.WriteLine("✅ Application closed normally.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"📋 Stack trace: {ex.StackTrace}");

                try
                {
                    MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}",
                                  "خطأ في التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                catch
                {
                    Console.WriteLine("❌ Could not show error MessageBox");
                }
            }
            finally
            {
                Console.WriteLine("🔚 Main method finished.");
                Console.WriteLine("Press any key to exit...");
                Console.ReadKey();
            }
        }

        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            MessageBox.Show($"خطأ في Thread: {e.Exception.Message}\n\nتفاصيل الخطأ:\n{e.Exception.StackTrace}",
                          "خطأ في التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Exception ex = (Exception)e.ExceptionObject;
            MessageBox.Show($"خطأ غير معالج: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}",
                          "خطأ في التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
