using System;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                DatabaseHelper.CreateDatabaseAndTable();  // هذا السطر ينشئ القاعدة والجداول

                ApplicationConfiguration.Initialize();

                // إضافة معالج للأخطاء غير المعالجة
                Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                Application.ThreadException += Application_ThreadException;
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

                Application.Run(new LoginForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل التطبيق: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}",
                              "خطأ في التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            MessageBox.Show($"خطأ في Thread: {e.Exception.Message}\n\nتفاصيل الخطأ:\n{e.Exception.StackTrace}",
                          "خطأ في التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Exception ex = (Exception)e.ExceptionObject;
            MessageBox.Show($"خطأ غير معالج: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}",
                          "خطأ في التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
