using System;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            Console.WriteLine("🚀 Starting HR Management System...");
            Console.WriteLine($"📅 Current time: {DateTime.Now}");
            Console.WriteLine($"💻 OS: {Environment.OSVersion}");
            Console.WriteLine($"🔧 .NET Version: {Environment.Version}");

            try
            {
                Console.WriteLine("📋 Testing basic functionality...");

                // اختبار أساسي بدون Windows Forms أولاً
                Console.WriteLine("✅ Console application is working!");
                Console.WriteLine("✅ .NET runtime is working!");

                Console.WriteLine("📋 Initializing Windows Forms...");
                ApplicationConfiguration.Initialize();
                Console.WriteLine("✅ Windows Forms initialized successfully!");

                Console.WriteLine("📋 Testing MessageBox...");
                Console.WriteLine("👀 LOOK FOR A MESSAGE BOX ON YOUR SCREEN!");
                Console.WriteLine("📱 A dialog should appear asking if you can see it...");

                var result = MessageBox.Show(
                    "🎉 HR Management System v5.3.0\n\n" +
                    "✅ Application is working!\n" +
                    "✅ Windows Forms is working!\n" +
                    "✅ MessageBox is working!\n\n" +
                    "Can you see this message?\n\n" +
                    "Click YES if you can see this dialog box.",
                    "🚀 HR System - SUCCESS TEST",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Information);

                Console.WriteLine($"✅ MessageBox closed with result: {result}");

                if (result == DialogResult.Yes)
                {
                    Console.WriteLine("🎉 GREAT! Windows are working!");
                    Console.WriteLine("📱 Now showing the main application window...");

                    // إضافة معالج للأخطاء
                    Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                    Application.ThreadException += Application_ThreadException;
                    AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

                    Console.WriteLine("🔐 Creating test form...");
                    var testForm = new MinimalTest();
                    Console.WriteLine("✅ Test form created successfully!");

                    Console.WriteLine("📱 Showing test form...");
                    Console.WriteLine("👀 LOOK FOR A GREEN WINDOW!");

                    Application.Run(testForm);

                    Console.WriteLine("✅ Application closed normally.");
                }
                else
                {
                    Console.WriteLine("❌ MessageBox not visible - there may be a display issue");
                    Console.WriteLine("💡 Try checking your display settings or running as administrator");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ ERROR: {ex.Message}");
                Console.WriteLine($"📋 Details: {ex.StackTrace}");

                try
                {
                    MessageBox.Show($"Error: {ex.Message}", "Application Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
                catch
                {
                    Console.WriteLine("❌ Could not show error dialog");
                }
            }
            finally
            {
                Console.WriteLine("🔚 Application finished.");
                Console.WriteLine("📋 Press any key to exit...");
                Console.ReadKey();
            }
        }

        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            MessageBox.Show($"خطأ في Thread: {e.Exception.Message}\n\nتفاصيل الخطأ:\n{e.Exception.StackTrace}",
                          "خطأ في التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Exception ex = (Exception)e.ExceptionObject;
            MessageBox.Show($"خطأ غير معالج: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.StackTrace}",
                          "خطأ في التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
