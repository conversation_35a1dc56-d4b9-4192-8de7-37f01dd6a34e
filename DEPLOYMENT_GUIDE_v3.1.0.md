# دليل النشر والتشغيل - Deployment Guide v3.1.0
## نظام إدارة الموارد البشرية - HR Management System

### 📅 الإصدار: 3.1.0 | ديسمبر 2024
### 👨‍💻 المطور: <PERSON> (<EMAIL>)

---

## 🚀 طرق تشغيل التطبيق - Application Launch Methods

### الطريقة الأولى: تشغيل مباشر من الكود المصدري
```bash
# التنقل إلى مجلد المشروع
cd E:\HR_Management_System

# بناء المشروع
dotnet build

# تشغيل التطبيق
dotnet run
```

### الطريقة الثانية: تشغيل من الملف التنفيذي (Debug)
```bash
# تشغيل إصدار Debug
.\bin\Debug\net6.0-windows\Ahmedapp` for` work.exe
```

### الطريقة الثالثة: تشغيل من الملف التنفيذي (Release) ⭐ الأفضل
```bash
# بناء إصدار Release
dotnet build --configuration Release

# تشغيل إصدار Release
.\bin\Release\net6.0-windows\Ahmedapp` for` work.exe
```

### الطريقة الرابعة: تشغيل باستخدام PowerShell
```powershell
# تشغيل في نافذة منفصلة
Start-Process -FilePath ".\bin\Release\net6.0-windows\Ahmedapp for work.exe" -WindowStyle Normal
```

---

## 🔧 متطلبات النظام - System Requirements

### البرمجيات المطلوبة:
- **Windows 10/11** (64-bit)
- **.NET 6.0 Runtime** أو أحدث
- **SQL Server LocalDB** (اختياري - التطبيق يعمل بدونه)
- **Visual Studio 2022** (للتطوير فقط)

### مواصفات الأجهزة الدنيا:
- **المعالج**: Intel Core i3 أو AMD equivalent
- **الذاكرة**: 4 GB RAM
- **التخزين**: 500 MB مساحة فارغة
- **الشاشة**: 1024x768 (الأفضل: 1920x1080)

---

## 📁 هيكل الملفات - File Structure

```
HR_Management_System/
├── bin/
│   ├── Debug/net6.0-windows/
│   │   ├── Ahmedapp for work.exe          # إصدار Debug
│   │   ├── Ahmedapp for work.dll.config   # إعدادات Debug
│   │   └── ...
│   └── Release/net6.0-windows/
│       ├── Ahmedapp for work.exe          # إصدار Release ⭐
│       ├── Ahmedapp for work.dll.config   # إعدادات Release
│       └── ...
├── HRManagementSystem/
│   ├── Database/
│   │   └── CreateTables.sql               # سكريبت قاعدة البيانات
│   └── App.config                         # إعدادات إضافية
├── App.config                             # الإعدادات الرئيسية
├── Program.cs                             # نقطة البداية
├── Form1.cs                               # الواجهة الرئيسية
├── DatabaseManager.cs                     # إدارة قاعدة البيانات
├── EmployeeManager.cs                     # إدارة الموظفين
├── NotesAndTipsManager.cs                 # إدارة الملاحظات والتلميحات
├── README.md                              # دليل المشروع
├── CHANGELOG_v3.1.0.md                   # سجل التغييرات
├── DEPLOYMENT_GUIDE_v3.1.0.md            # هذا الملف
└── USAGE_GUIDE.md                         # دليل الاستخدام
```

---

## 🗄️ إعداد قاعدة البيانات - Database Setup

### الخيار الأول: تشغيل تلقائي (مستحسن)
التطبيق سينشئ قاعدة البيانات تلقائياً عند التشغيل الأول.

### الخيار الثاني: إعداد يدوي
1. افتح **SQL Server Management Studio**
2. اتصل بـ `(LocalDB)\MSSQLLocalDB`
3. شغل السكريبت من: `HRManagementSystem/Database/CreateTables.sql`

### الخيار الثالث: بدون قاعدة بيانات
التطبيق يعمل بالبيانات التجريبية حتى لو لم تكن قاعدة البيانات متاحة.

---

## ⚙️ إعدادات الاتصال - Connection Settings

### ملف App.config:
```xml
<connectionStrings>
  <add name="DefaultConnection" 
       connectionString="Data Source=(LocalDB)\MSSQLLocalDB;Initial Catalog=HRManagementDB;Integrated Security=True;Connect Timeout=30"
       providerName="System.Data.SqlClient" />
</connectionStrings>
```

### تخصيص سلسلة الاتصال:
لتغيير قاعدة البيانات، عدل `connectionString` في `App.config`:

```xml
<!-- للاتصال بـ SQL Server Express -->
<add name="DefaultConnection" 
     connectionString="Data Source=.\SQLEXPRESS;Initial Catalog=HRManagementDB;Integrated Security=True"
     providerName="System.Data.SqlClient" />

<!-- للاتصال بـ SQL Server عادي -->
<add name="DefaultConnection" 
     connectionString="Data Source=ServerName;Initial Catalog=HRManagementDB;User ID=username;Password=password"
     providerName="System.Data.SqlClient" />
```

---

## 🔍 استكشاف الأخطاء - Troubleshooting

### المشكلة: التطبيق لا يبدأ
**الحلول:**
1. تأكد من تثبيت .NET 6.0 Runtime
2. شغل Command Prompt كمدير
3. استخدم: `dotnet --list-runtimes` للتحقق من الإصدارات

### المشكلة: خطأ في قاعدة البيانات
**الحلول:**
1. التطبيق سيعرض تحذير ويستمر بالبيانات التجريبية
2. تأكد من تشغيل SQL Server LocalDB:
   ```bash
   sqllocaldb start MSSQLLocalDB
   ```
3. أعد إنشاء قاعدة البيانات باستخدام السكريبت

### المشكلة: النافذة لا تظهر
**الحلول:**
1. تحقق من شريط المهام
2. استخدم Alt+Tab للتنقل بين النوافذ
3. تحقق من العمليات الجارية:
   ```powershell
   Get-Process | Where-Object {$_.ProcessName -eq "Ahmedapp for work"}
   ```

### المشكلة: أداء بطيء
**الحلول:**
1. استخدم إصدار Release بدلاً من Debug
2. أغلق التطبيقات الأخرى لتوفير الذاكرة
3. تأكد من وجود مساحة كافية على القرص الصلب

---

## 📊 مراقبة الأداء - Performance Monitoring

### إحصائيات التطبيق:
- **استهلاك الذاكرة**: ~49 MB (Release)
- **وقت البدء**: 2-5 ثواني
- **استهلاك المعالج**: منخفض (<5%)

### مراقبة العمليات:
```powershell
# عرض معلومات العملية
Get-Process "Ahmedapp for work" | Select-Object Name, CPU, WorkingSet, VirtualMemorySize

# مراقبة مستمرة
while($true) {
    Get-Process "Ahmedapp for work" -ErrorAction SilentlyContinue | 
    Select-Object Name, @{Name="Memory(MB)";Expression={[math]::Round($_.WorkingSet/1MB,2)}}
    Start-Sleep 5
}
```

---

## 🔒 الأمان والصلاحيات - Security & Permissions

### صلاحيات التشغيل:
- **المستخدم العادي**: يمكنه تشغيل التطبيق
- **قاعدة البيانات**: تحتاج صلاحيات LocalDB
- **الملفات**: صلاحيات قراءة/كتابة في مجلد التطبيق

### حماية البيانات:
- البيانات محفوظة محلياً في قاعدة البيانات
- لا يتم إرسال بيانات عبر الإنترنت
- النسخ الاحتياطية تلقائية لقاعدة البيانات

---

## 📦 النشر للمستخدمين النهائيين - End-User Deployment

### إنشاء حزمة النشر:
```bash
# إنشاء إصدار Release
dotnet build --configuration Release

# نسخ الملفات المطلوبة
# bin/Release/net6.0-windows/ -> مجلد النشر
```

### الملفات المطلوبة للنشر:
- `Ahmedapp for work.exe`
- `Ahmedapp for work.dll`
- `Ahmedapp for work.dll.config`
- `Ahmedapp for work.runtimeconfig.json`
- جميع ملفات .dll المرافقة

### تعليمات للمستخدم النهائي:
1. تثبيت .NET 6.0 Runtime
2. نسخ مجلد التطبيق إلى الموقع المطلوب
3. تشغيل `Ahmedapp for work.exe`

---

## 📞 الدعم الفني - Technical Support

### للمساعدة والدعم:
- **المطور**: Ahmed Ibrahim
- **البريد الإلكتروني**: <EMAIL>
- **الإصدار**: 3.1.0
- **تاريخ الإصدار**: ديسمبر 2024

### تقديم تقرير خطأ:
عند مواجهة مشكلة، يرجى تضمين:
1. رقم الإصدار (3.1.0)
2. نظام التشغيل
3. رسالة الخطأ (إن وجدت)
4. خطوات إعادة إنتاج المشكلة

---

## ✅ قائمة التحقق - Deployment Checklist

### قبل النشر:
- [ ] تم بناء إصدار Release بنجاح
- [ ] تم اختبار التطبيق على النظام المستهدف
- [ ] تم التحقق من متطلبات النظام
- [ ] تم إعداد قاعدة البيانات (إن لزم الأمر)

### بعد النشر:
- [ ] تم تشغيل التطبيق بنجاح
- [ ] تم التحقق من جميع الميزات
- [ ] تم إنشاء نسخة احتياطية من البيانات
- [ ] تم توثيق أي إعدادات خاصة

---

**التطبيق جاهز للنشر والاستخدام الإنتاجي!** 🚀
