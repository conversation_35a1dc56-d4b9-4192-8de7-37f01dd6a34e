using System;
using System.IO;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.Threading.Tasks;

namespace Ahmedapp_for_work
{
    public class PDFReportGenerator
    {
        private static readonly string reportsFolder = "Reports";
        private static readonly string fontsFolder = "Fonts";

        // إنشاء تقرير PDF للموظف
        public static async Task<string> GenerateEmployeeReport(Employee employee)
        {
            try
            {
                // إنشاء مجلد التقارير إذا لم يكن موجوداً
                CreateReportsDirectory();

                string fileName = $"Employee_Report_{employee.NationalId}_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.pdf";
                string filePath = Path.Combine(reportsFolder, fileName);

                await Task.Run(() => CreateEmployeePDF(employee, filePath));

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء تقرير PDF للموظف: {employee.Name}");
                return filePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء تقرير PDF: {ex.Message}");
                NotificationManager.SendErrorNotification("خطأ في إنشاء تقرير PDF", ex.Message, ex);
                throw;
            }
        }

        // إنشاء ملف PDF للموظف
        private static void CreateEmployeePDF(Employee employee, string filePath)
        {
            Document document = new Document(PageSize.A4, 50, 50, 50, 50);
            PdfWriter writer = PdfWriter.GetInstance(document, new FileStream(filePath, FileMode.Create));

            document.Open();

            try
            {
                // إعداد الخطوط
                BaseFont arabicFont = GetArabicFont();
                Font titleFont = new Font(arabicFont, 18, Font.BOLD);
                Font headerFont = new Font(arabicFont, 14, Font.BOLD);
                Font normalFont = new Font(arabicFont, 12, Font.NORMAL);
                Font smallFont = new Font(arabicFont, 10, Font.NORMAL);

                // عنوان التقرير
                Paragraph title = new Paragraph("تقرير بيانات الموظف", titleFont);
                title.Alignment = Element.ALIGN_CENTER;
                title.SpacingAfter = 20;
                document.Add(title);

                // معلومات النظام
                Paragraph systemInfo = new Paragraph($"نظام إدارة الموارد البشرية - تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm:ss}", smallFont);
                systemInfo.Alignment = Element.ALIGN_CENTER;
                systemInfo.SpacingAfter = 30;
                document.Add(systemInfo);

                // البيانات الأساسية
                AddSectionHeader(document, "البيانات الأساسية", headerFont);
                AddEmployeeBasicInfo(document, employee, normalFont);

                // بيانات التوظيف
                AddSectionHeader(document, "بيانات التوظيف", headerFont);
                AddEmploymentInfo(document, employee, normalFont);

                // بيانات الانتداب (إذا كانت متوفرة)
                if (!string.IsNullOrEmpty(employee.DecisionNumber))
                {
                    AddSectionHeader(document, "بيانات الانتداب", headerFont);
                    AddDelegationInfo(document, employee, normalFont);
                }

                // الاشتراكات التأمينية
                if (employee.TotalInsuranceContributions.HasValue && employee.TotalInsuranceContributions > 0)
                {
                    AddSectionHeader(document, "الاشتراكات التأمينية", headerFont);
                    AddInsuranceInfo(document, employee, normalFont);
                }

                // بيانات إصابة العمل (إذا كانت متوفرة)
                if (!string.IsNullOrEmpty(employee.InjuryType))
                {
                    AddSectionHeader(document, "بيانات إصابة العمل", headerFont);
                    AddInjuryInfo(document, employee, normalFont);
                }

                // بيانات الإجازة بدون مرتب (إذا كانت متوفرة)
                if (!string.IsNullOrEmpty(employee.UnpaidLeaveType))
                {
                    AddSectionHeader(document, "بيانات الإجازة بدون مرتب", headerFont);
                    AddUnpaidLeaveInfo(document, employee, normalFont);
                }

                // الملاحظات
                if (!string.IsNullOrEmpty(employee.Notes))
                {
                    AddSectionHeader(document, "الملاحظات", headerFont);
                    Paragraph notes = new Paragraph(employee.Notes, normalFont);
                    notes.Alignment = Element.ALIGN_RIGHT;
                    notes.SpacingAfter = 15;
                    document.Add(notes);
                }

                // تذييل التقرير
                AddReportFooter(document, smallFont);
            }
            finally
            {
                document.Close();
                writer.Close();
            }
        }

        // إضافة عنوان قسم
        private static void AddSectionHeader(Document document, string title, Font font)
        {
            Paragraph header = new Paragraph(title, font);
            header.Alignment = Element.ALIGN_RIGHT;
            header.SpacingBefore = 20;
            header.SpacingAfter = 10;
            header.BackgroundColor = new BaseColor(240, 240, 240);
            document.Add(header);
        }

        // إضافة البيانات الأساسية
        private static void AddEmployeeBasicInfo(Document document, Employee employee, Font font)
        {
            PdfPTable table = new PdfPTable(2);
            table.WidthPercentage = 100;
            table.SetWidths(new float[] { 1, 2 });
            table.RunDirection = PdfWriter.RUN_DIRECTION_RTL;

            AddTableRow(table, "الاسم الكامل:", employee.Name ?? "", font);
            AddTableRow(table, "الرقم القومي:", employee.NationalId ?? "", font);
            AddTableRow(table, "رقم التأمين:", employee.InsuranceNumber ?? "", font);
            AddTableRow(table, "رقم الهاتف:", employee.ContactInfo ?? "", font);

            document.Add(table);
        }

        // إضافة بيانات التوظيف
        private static void AddEmploymentInfo(Document document, Employee employee, Font font)
        {
            PdfPTable table = new PdfPTable(2);
            table.WidthPercentage = 100;
            table.SetWidths(new float[] { 1, 2 });
            table.RunDirection = PdfWriter.RUN_DIRECTION_RTL;

            AddTableRow(table, "المسمى الوظيفي:", employee.JobTitle ?? "", font);
            AddTableRow(table, "القسم:", employee.Department ?? "", font);
            AddTableRow(table, "تاريخ التعيين:", employee.HireDate?.ToString("yyyy-MM-dd") ?? "", font);
            AddTableRow(table, "حالة الموظف:", employee.EmployeeType ?? "", font);

            document.Add(table);
        }

        // إضافة بيانات الانتداب
        private static void AddDelegationInfo(Document document, Employee employee, Font font)
        {
            PdfPTable table = new PdfPTable(2);
            table.WidthPercentage = 100;
            table.SetWidths(new float[] { 1, 2 });
            table.RunDirection = PdfWriter.RUN_DIRECTION_RTL;

            AddTableRow(table, "رقم القرار:", employee.DecisionNumber ?? "", font);
            AddTableRow(table, "تاريخ القرار:", employee.DecisionDate?.ToString("yyyy-MM-dd") ?? "", font);
            AddTableRow(table, "من/إلى:", employee.FromToEntity ?? "", font);
            AddTableRow(table, "إدارة العمل:", employee.WorkDepartment ?? "", font);
            AddTableRow(table, "الإدارة:", employee.Management ?? "", font);
            AddTableRow(table, "تاريخ انتهاء الإجازة:", employee.LeaveEndDate?.ToString("yyyy-MM-dd") ?? "", font);
            AddTableRow(table, "نوع التعيين:", employee.AppointmentType ?? "", font);

            document.Add(table);
        }

        // إضافة بيانات التأمين
        private static void AddInsuranceInfo(Document document, Employee employee, Font font)
        {
            PdfPTable table = new PdfPTable(2);
            table.WidthPercentage = 100;
            table.SetWidths(new float[] { 1, 2 });
            table.RunDirection = PdfWriter.RUN_DIRECTION_RTL;

            AddTableRow(table, "إجمالي الاشتراكات:", FormatCurrency(employee.TotalInsuranceContributions), font);
            AddTableRow(table, "معدل 12%:", FormatCurrency(employee.Rate12Percent), font);
            AddTableRow(table, "معدل 9%:", FormatCurrency(employee.Rate9Percent), font);
            AddTableRow(table, "معدل 3%:", FormatCurrency(employee.Rate3Percent), font);
            AddTableRow(table, "معدل 1% (1):", FormatCurrency(employee.Rate1Percent_1), font);
            AddTableRow(table, "معدل 1% (2):", FormatCurrency(employee.Rate1Percent_2), font);
            AddTableRow(table, "معدل 1% (3):", FormatCurrency(employee.Rate1Percent_3), font);
            AddTableRow(table, "معدل 1% (4):", FormatCurrency(employee.Rate1Percent_4), font);
            AddTableRow(table, "معدل 0.25%:", FormatCurrency(employee.Rate025Percent), font);

            document.Add(table);
        }

        // إضافة بيانات إصابة العمل
        private static void AddInjuryInfo(Document document, Employee employee, Font font)
        {
            PdfPTable table = new PdfPTable(2);
            table.WidthPercentage = 100;
            table.SetWidths(new float[] { 1, 2 });
            table.RunDirection = PdfWriter.RUN_DIRECTION_RTL;

            AddTableRow(table, "نوع الإصابة:", employee.InjuryType ?? "", font);
            AddTableRow(table, "تاريخ الإصابة:", employee.InjuryDate?.ToString("yyyy-MM-dd") ?? "", font);

            document.Add(table);
        }

        // إضافة بيانات الإجازة بدون مرتب
        private static void AddUnpaidLeaveInfo(Document document, Employee employee, Font font)
        {
            PdfPTable table = new PdfPTable(2);
            table.WidthPercentage = 100;
            table.SetWidths(new float[] { 1, 2 });
            table.RunDirection = PdfWriter.RUN_DIRECTION_RTL;

            AddTableRow(table, "نوع الإجازة:", employee.UnpaidLeaveType ?? "", font);
            AddTableRow(table, "حالة الإجازة:", employee.LeaveStatus ?? "", font);
            AddTableRow(table, "تاريخ بداية الإجازة:", employee.LeaveStartDate?.ToString("yyyy-MM-dd") ?? "", font);
            AddTableRow(table, "تاريخ نهاية الإجازة:", employee.UnpaidLeaveEndDate?.ToString("yyyy-MM-dd") ?? "", font);

            document.Add(table);
        }

        // إضافة صف للجدول
        private static void AddTableRow(PdfPTable table, string label, string value, Font font)
        {
            PdfPCell labelCell = new PdfPCell(new Phrase(label, font));
            labelCell.BackgroundColor = new BaseColor(245, 245, 245);
            labelCell.HorizontalAlignment = Element.ALIGN_RIGHT;
            labelCell.Padding = 8;
            table.AddCell(labelCell);

            PdfPCell valueCell = new PdfPCell(new Phrase(value, font));
            valueCell.HorizontalAlignment = Element.ALIGN_RIGHT;
            valueCell.Padding = 8;
            table.AddCell(valueCell);
        }

        // إضافة تذييل التقرير
        private static void AddReportFooter(Document document, Font font)
        {
            Paragraph footer = new Paragraph($"\n\nتم إنشاء هذا التقرير تلقائياً بواسطة نظام إدارة الموارد البشرية\nتاريخ الإنشاء: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\nرقم التقرير: {Guid.NewGuid().ToString("N")[..8].ToUpper()}", font);
            footer.Alignment = Element.ALIGN_CENTER;
            footer.SpacingBefore = 30;
            document.Add(footer);
        }

        // تنسيق العملة
        private static string FormatCurrency(decimal? amount)
        {
            return amount.HasValue ? $"{amount:N2} جنيه" : "غير محدد";
        }

        // الحصول على خط عربي
        private static BaseFont GetArabicFont()
        {
            try
            {
                // محاولة استخدام خط عربي من النظام
                string fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Fonts), "arial.ttf");
                if (File.Exists(fontPath))
                {
                    return BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                }

                // استخدام خط افتراضي
                return BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل الخط العربي: {ex.Message}");
                return BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
            }
        }

        // إنشاء مجلد التقارير
        private static void CreateReportsDirectory()
        {
            try
            {
                if (!Directory.Exists(reportsFolder))
                {
                    Directory.CreateDirectory(reportsFolder);
                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء مجلد التقارير: {reportsFolder}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء مجلد التقارير: {ex.Message}");
                throw;
            }
        }

        // إنشاء تقرير شامل لجميع الموظفين
        public static async Task<string> GenerateAllEmployeesReport()
        {
            try
            {
                CreateReportsDirectory();

                string fileName = $"All_Employees_Report_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}.pdf";
                string filePath = Path.Combine(reportsFolder, fileName);

                await Task.Run(() => CreateAllEmployeesPDF(filePath));

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء تقرير شامل لجميع الموظفين");
                return filePath;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء التقرير الشامل: {ex.Message}");
                throw;
            }
        }

        // إنشاء PDF لجميع الموظفين
        private static void CreateAllEmployeesPDF(string filePath)
        {
            Document document = new Document(PageSize.A4.Rotate(), 30, 30, 30, 30); // أفقي للجدول الكبير
            PdfWriter writer = PdfWriter.GetInstance(document, new FileStream(filePath, FileMode.Create));

            document.Open();

            try
            {
                BaseFont arabicFont = GetArabicFont();
                Font titleFont = new Font(arabicFont, 16, Font.BOLD);
                Font headerFont = new Font(arabicFont, 10, Font.BOLD);
                Font normalFont = new Font(arabicFont, 8, Font.NORMAL);

                // عنوان التقرير
                Paragraph title = new Paragraph("تقرير شامل لجميع الموظفين", titleFont);
                title.Alignment = Element.ALIGN_CENTER;
                title.SpacingAfter = 20;
                document.Add(title);

                // جلب بيانات الموظفين
                var employeesData = DatabaseHelper.GetAllEmployeesFromAccess();

                // إنشاء جدول
                PdfPTable table = new PdfPTable(6);
                table.WidthPercentage = 100;
                table.SetWidths(new float[] { 2, 1.5f, 2, 1.5f, 1.5f, 1.5f });
                table.RunDirection = PdfWriter.RUN_DIRECTION_RTL;

                // رؤوس الأعمدة
                AddHeaderCell(table, "الاسم الكامل", headerFont);
                AddHeaderCell(table, "الرقم القومي", headerFont);
                AddHeaderCell(table, "المسمى الوظيفي", headerFont);
                AddHeaderCell(table, "القسم", headerFont);
                AddHeaderCell(table, "تاريخ التعيين", headerFont);
                AddHeaderCell(table, "حالة الموظف", headerFont);

                // بيانات الموظفين
                foreach (System.Data.DataRow row in employeesData.Rows)
                {
                    AddDataCell(table, row["FullName"]?.ToString() ?? "", normalFont);
                    AddDataCell(table, row["NationalId"]?.ToString() ?? "", normalFont);
                    AddDataCell(table, row["JobTitle"]?.ToString() ?? "", normalFont);
                    AddDataCell(table, row["Department"]?.ToString() ?? "", normalFont);
                    AddDataCell(table, row["HireDate"] != DBNull.Value ? Convert.ToDateTime(row["HireDate"]).ToString("yyyy-MM-dd") : "", normalFont);
                    AddDataCell(table, row["EmployeeType"]?.ToString() ?? "", normalFont);
                }

                document.Add(table);

                // إحصائيات
                Paragraph stats = new Paragraph($"\n\nإجمالي عدد الموظفين: {employeesData.Rows.Count}\nتاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm:ss}", normalFont);
                stats.Alignment = Element.ALIGN_CENTER;
                document.Add(stats);
            }
            finally
            {
                document.Close();
                writer.Close();
            }
        }

        // إضافة خلية رأس
        private static void AddHeaderCell(PdfPTable table, string text, Font font)
        {
            PdfPCell cell = new PdfPCell(new Phrase(text, font));
            cell.BackgroundColor = new BaseColor(52, 152, 219);
            cell.HorizontalAlignment = Element.ALIGN_CENTER;
            cell.Padding = 8;
            table.AddCell(cell);
        }

        // إضافة خلية بيانات
        private static void AddDataCell(PdfPTable table, string text, Font font)
        {
            PdfPCell cell = new PdfPCell(new Phrase(text, font));
            cell.HorizontalAlignment = Element.ALIGN_RIGHT;
            cell.Padding = 5;
            table.AddCell(cell);
        }

        // حذف التقارير القديمة
        public static void CleanOldReports(int daysToKeep = 30)
        {
            try
            {
                if (!Directory.Exists(reportsFolder)) return;

                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var files = Directory.GetFiles(reportsFolder, "*.pdf");

                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                        System.Diagnostics.Debug.WriteLine($"🗑️ تم حذف تقرير قديم: {fileInfo.Name}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنظيف التقارير القديمة: {ex.Message}");
            }
        }
    }
}
