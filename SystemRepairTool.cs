using System;
using System.IO;
using System.Data.SQLite;
using System.Windows.Forms;
using System.Diagnostics;
using System.Threading.Tasks;

namespace Ahmedapp_for_work
{
    public class SystemRepairTool
    {
        // إصلاح شامل لجميع مشاكل النظام
        public static async Task<bool> RepairAllSystemIssues()
        {
            try
            {
                Debug.WriteLine("🔧 بدء الإصلاح الشامل للنظام...");

                bool allFixed = true;

                // 1. إصلاح قاعدة بيانات SQLite
                if (!await RepairSQLiteDatabase())
                {
                    Debug.WriteLine("❌ فشل إصلاح قاعدة بيانات SQLite");
                    allFixed = false;
                }

                // 2. إصلاح قاعدة بيانات Access
                if (!await RepairAccessDatabase())
                {
                    Debug.WriteLine("⚠️ تحذير: مشاكل في قاعدة بيانات Access");
                    // لا نعتبرها فشل كامل
                }

                // 3. إصلاح الملفات المفقودة
                if (!RepairMissingFiles())
                {
                    Debug.WriteLine("⚠️ تحذير: بعض الملفات لا تزال مفقودة");
                }

                // 4. إصلاح الصلاحيات
                if (!RepairFilePermissions())
                {
                    Debug.WriteLine("⚠️ تحذير: مشاكل في صلاحيات الملفات");
                }

                // 5. تنظيف الملفات المؤقتة
                CleanupTempFiles();

                // 6. إنشاء نسخة احتياطية طارئة
                await CreateEmergencyBackup();

                Debug.WriteLine($"✅ انتهى الإصلاح الشامل - النتيجة: {(allFixed ? "نجح" : "نجح جزئياً")}");
                return allFixed;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في الإصلاح الشامل: {ex.Message}");
                return false;
            }
        }

        // إصلاح قاعدة بيانات SQLite
        public static async Task<bool> RepairSQLiteDatabase()
        {
            try
            {
                Debug.WriteLine("🔧 إصلاح قاعدة بيانات SQLite...");

                string dbPath = Path.Combine(Environment.CurrentDirectory, "hr_management.db");
                string backupPath = $"{dbPath}.repair_backup_{DateTime.Now:yyyyMMdd_HHmmss}";

                // إنشاء نسخة احتياطية إذا كان الملف موجوداً
                if (File.Exists(dbPath))
                {
                    try
                    {
                        File.Copy(dbPath, backupPath);
                        Debug.WriteLine($"✅ تم إنشاء نسخة احتياطية: {backupPath}");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"⚠️ تحذير: فشل إنشاء نسخة احتياطية: {ex.Message}");
                    }
                }

                // محاولة إصلاح قاعدة البيانات
                bool repaired = false;
                
                // الطريقة 1: محاولة فتح قاعدة البيانات الحالية
                if (File.Exists(dbPath))
                {
                    try
                    {
                        using (var connection = new SQLiteConnection($"Data Source={dbPath};Version=3;"))
                        {
                            connection.Open();
                            
                            // اختبار سلامة قاعدة البيانات
                            using (var cmd = new SQLiteCommand("PRAGMA integrity_check;", connection))
                            {
                                var result = cmd.ExecuteScalar()?.ToString();
                                if (result == "ok")
                                {
                                    Debug.WriteLine("✅ قاعدة البيانات سليمة");
                                    repaired = true;
                                }
                                else
                                {
                                    Debug.WriteLine($"⚠️ مشاكل في قاعدة البيانات: {result}");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"❌ قاعدة البيانات تالفة: {ex.Message}");
                    }
                }

                // الطريقة 2: إنشاء قاعدة بيانات جديدة إذا فشلت الطريقة الأولى
                if (!repaired)
                {
                    try
                    {
                        if (File.Exists(dbPath))
                        {
                            File.Delete(dbPath);
                        }

                        // إنشاء قاعدة بيانات جديدة
                        using (var connection = new SQLiteConnection($"Data Source={dbPath};Version=3;"))
                        {
                            connection.Open();
                            
                            // إنشاء جدول المستخدمين
                            string createUsersTable = @"
                                CREATE TABLE Users (
                                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                                    Username TEXT NOT NULL UNIQUE,
                                    PasswordHash TEXT NOT NULL,
                                    PasswordSalt TEXT NOT NULL,
                                    Role TEXT NOT NULL,
                                    IsActive INTEGER NOT NULL DEFAULT 1
                                );";

                            using (var cmd = new SQLiteCommand(createUsersTable, connection))
                            {
                                cmd.ExecuteNonQuery();
                            }

                            Debug.WriteLine("✅ تم إنشاء قاعدة بيانات جديدة");
                        }

                        // إنشاء المستخدمين الافتراضيين
                        await CreateDefaultUsersAsync();
                        repaired = true;
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"❌ فشل إنشاء قاعدة بيانات جديدة: {ex.Message}");
                    }
                }

                return repaired;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في إصلاح SQLite: {ex.Message}");
                return false;
            }
        }

        // إصلاح قاعدة بيانات Access
        public static async Task<bool> RepairAccessDatabase()
        {
            try
            {
                Debug.WriteLine("🔧 إصلاح قاعدة بيانات Access...");

                // فحص إصدارات Office المتاحة
                var officeVersions = OfficeDiagnostics.DiagnoseAllOfficeVersions();
                bool hasWorkingOffice = false;

                foreach (var version in officeVersions)
                {
                    if (version.IsWorking)
                    {
                        hasWorkingOffice = true;
                        Debug.WriteLine($"✅ تم العثور على إصدار Office يعمل: {version.DisplayName}");
                        break;
                    }
                }

                if (!hasWorkingOffice)
                {
                    Debug.WriteLine("⚠️ لا توجد إصدارات Office تعمل - سيتم استخدام SQLite فقط");
                    return false;
                }

                // محاولة إنشاء/إصلاح قاعدة بيانات Access
                try
                {
                    var accessManager = new AccessDatabaseManager();
                    bool success = accessManager.InitializeAccessDatabase();
                    
                    if (success)
                    {
                        Debug.WriteLine("✅ تم إصلاح قاعدة بيانات Access");
                        return true;
                    }
                    else
                    {
                        Debug.WriteLine("❌ فشل إصلاح قاعدة بيانات Access");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"❌ خطأ في إصلاح Access: {ex.Message}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ عام في إصلاح Access: {ex.Message}");
                return false;
            }
        }

        // إصلاح الملفات المفقودة
        public static bool RepairMissingFiles()
        {
            try
            {
                Debug.WriteLine("🔧 فحص وإصلاح الملفات المفقودة...");

                // إنشاء المجلدات المطلوبة
                string[] requiredFolders = {
                    "Backups",
                    "Reports", 
                    "EmployeeFiles",
                    "Logs",
                    "Temp"
                };

                foreach (string folder in requiredFolders)
                {
                    if (!Directory.Exists(folder))
                    {
                        Directory.CreateDirectory(folder);
                        Debug.WriteLine($"✅ تم إنشاء مجلد: {folder}");
                    }
                }

                // إنشاء ملفات التكوين الافتراضية
                CreateDefaultConfigFiles();

                Debug.WriteLine("✅ تم فحص وإصلاح الملفات المفقودة");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في إصلاح الملفات: {ex.Message}");
                return false;
            }
        }

        // إصلاح صلاحيات الملفات
        public static bool RepairFilePermissions()
        {
            try
            {
                Debug.WriteLine("🔧 فحص وإصلاح صلاحيات الملفات...");

                string[] criticalFiles = {
                    "hr_management.db",
                    "HRManagement.accdb"
                };

                foreach (string file in criticalFiles)
                {
                    if (File.Exists(file))
                    {
                        try
                        {
                            var fileInfo = new FileInfo(file);
                            if (fileInfo.IsReadOnly)
                            {
                                fileInfo.IsReadOnly = false;
                                Debug.WriteLine($"✅ تم إصلاح صلاحيات: {file}");
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"⚠️ تحذير: لا يمكن تعديل صلاحيات {file}: {ex.Message}");
                        }
                    }
                }

                Debug.WriteLine("✅ تم فحص صلاحيات الملفات");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في إصلاح الصلاحيات: {ex.Message}");
                return false;
            }
        }

        // تنظيف الملفات المؤقتة
        public static void CleanupTempFiles()
        {
            try
            {
                Debug.WriteLine("🔧 تنظيف الملفات المؤقتة...");

                string tempFolder = "Temp";
                if (Directory.Exists(tempFolder))
                {
                    var tempFiles = Directory.GetFiles(tempFolder);
                    foreach (string file in tempFiles)
                    {
                        try
                        {
                            File.Delete(file);
                        }
                        catch
                        {
                            // تجاهل أخطاء حذف الملفات المؤقتة
                        }
                    }
                }

                // حذف ملفات النسخ الاحتياطية القديمة (أكثر من 30 يوم)
                string[] backupPatterns = { "*.backup_*", "*.repair_backup_*" };
                foreach (string pattern in backupPatterns)
                {
                    var oldBackups = Directory.GetFiles(".", pattern);
                    foreach (string backup in oldBackups)
                    {
                        try
                        {
                            var fileInfo = new FileInfo(backup);
                            if (fileInfo.CreationTime < DateTime.Now.AddDays(-30))
                            {
                                File.Delete(backup);
                                Debug.WriteLine($"✅ تم حذف نسخة احتياطية قديمة: {backup}");
                            }
                        }
                        catch
                        {
                            // تجاهل أخطاء حذف النسخ القديمة
                        }
                    }
                }

                Debug.WriteLine("✅ تم تنظيف الملفات المؤقتة");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ تحذير في تنظيف الملفات: {ex.Message}");
            }
        }

        // إنشاء نسخة احتياطية طارئة
        public static async Task CreateEmergencyBackup()
        {
            try
            {
                Debug.WriteLine("🔧 إنشاء نسخة احتياطية طارئة...");

                string backupFolder = Path.Combine("Backups", $"Emergency_{DateTime.Now:yyyyMMdd_HHmmss}");
                Directory.CreateDirectory(backupFolder);

                // نسخ قواعد البيانات
                string[] dbFiles = { "hr_management.db", "HRManagement.accdb" };
                foreach (string dbFile in dbFiles)
                {
                    if (File.Exists(dbFile))
                    {
                        string destFile = Path.Combine(backupFolder, dbFile);
                        File.Copy(dbFile, destFile);
                        Debug.WriteLine($"✅ تم نسخ: {dbFile}");
                    }
                }

                Debug.WriteLine($"✅ تم إنشاء نسخة احتياطية طارئة في: {backupFolder}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ تحذير في النسخة الاحتياطية الطارئة: {ex.Message}");
            }
        }

        // إنشاء المستخدمين الافتراضيين (async)
        private static async Task CreateDefaultUsersAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    DatabaseHelper.CreateDefaultUsers();
                });
                Debug.WriteLine("✅ تم إنشاء المستخدمين الافتراضيين");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ خطأ في إنشاء المستخدمين: {ex.Message}");
            }
        }

        // إنشاء ملفات التكوين الافتراضية
        private static void CreateDefaultConfigFiles()
        {
            try
            {
                // ملف إعدادات البريد الإلكتروني
                string emailConfigPath = "email_settings.json";
                if (!File.Exists(emailConfigPath))
                {
                    string defaultEmailConfig = @"{
  ""EmailNotificationsEnabled"": false,
  ""SmtpServer"": ""smtp.gmail.com"",
  ""SmtpPort"": 587,
  ""UseSSL"": true,
  ""SenderEmail"": """",
  ""SenderPassword"": """",
  ""AdminEmails"": [],
  ""SendDatabaseErrors"": true,
  ""SendBackupErrors"": true,
  ""SendSyncErrors"": true,
  ""SendEmployeeReports"": false
}";
                    File.WriteAllText(emailConfigPath, defaultEmailConfig);
                    Debug.WriteLine("✅ تم إنشاء ملف إعدادات البريد الإلكتروني");
                }

                // ملف README
                string readmePath = "README.txt";
                if (!File.Exists(readmePath))
                {
                    string readmeContent = $@"نظام إدارة الموارد البشرية v5.1.0
=====================================

تاريخ الإنشاء: {DateTime.Now:yyyy-MM-dd HH:mm:ss}

معلومات تسجيل الدخول:
- المدير: admin / 123456
- مستخدم عادي: user / user123

المجلدات:
- Backups: النسخ الاحتياطية
- Reports: تقارير PDF
- EmployeeFiles: ملفات الموظفين
- Logs: ملفات السجلات

للدعم التقني: راجع ملفات التوثيق المرفقة
";
                    File.WriteAllText(readmePath, readmeContent);
                    Debug.WriteLine("✅ تم إنشاء ملف README");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ تحذير في إنشاء ملفات التكوين: {ex.Message}");
            }
        }

        // عرض نافذة إصلاح تفاعلية
        public static void ShowRepairDialog()
        {
            try
            {
                var repairForm = new Form
                {
                    Text = "أداة إصلاح النظام",
                    Size = new System.Drawing.Size(600, 500),
                    StartPosition = FormStartPosition.CenterScreen,
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    MaximizeBox = false
                };

                var progressBar = new ProgressBar
                {
                    Location = new System.Drawing.Point(20, 20),
                    Size = new System.Drawing.Size(540, 30),
                    Style = ProgressBarStyle.Marquee
                };

                var statusLabel = new Label
                {
                    Location = new System.Drawing.Point(20, 60),
                    Size = new System.Drawing.Size(540, 30),
                    Text = "جاري فحص النظام...",
                    Font = new System.Drawing.Font("Tahoma", 10)
                };

                var logTextBox = new TextBox
                {
                    Location = new System.Drawing.Point(20, 100),
                    Size = new System.Drawing.Size(540, 300),
                    Multiline = true,
                    ScrollBars = ScrollBars.Vertical,
                    ReadOnly = true,
                    Font = new System.Drawing.Font("Consolas", 9)
                };

                var repairButton = new Button
                {
                    Text = "بدء الإصلاح",
                    Location = new System.Drawing.Point(20, 420),
                    Size = new System.Drawing.Size(100, 30),
                    BackColor = System.Drawing.Color.Green,
                    ForeColor = System.Drawing.Color.White
                };

                var closeButton = new Button
                {
                    Text = "إغلاق",
                    Location = new System.Drawing.Point(460, 420),
                    Size = new System.Drawing.Size(100, 30),
                    BackColor = System.Drawing.Color.Gray,
                    ForeColor = System.Drawing.Color.White
                };

                repairButton.Click += async (s, e) =>
                {
                    repairButton.Enabled = false;
                    statusLabel.Text = "جاري الإصلاح...";
                    logTextBox.Text = "";

                    var progress = new Progress<string>(message =>
                    {
                        logTextBox.AppendText(message + Environment.NewLine);
                        logTextBox.ScrollToCaret();
                    });

                    bool success = await RepairAllSystemIssues();
                    
                    statusLabel.Text = success ? "تم الإصلاح بنجاح!" : "تم الإصلاح جزئياً";
                    progressBar.Style = ProgressBarStyle.Blocks;
                    progressBar.Value = 100;
                    repairButton.Enabled = true;
                };

                closeButton.Click += (s, e) => repairForm.Close();

                repairForm.Controls.AddRange(new Control[] {
                    progressBar, statusLabel, logTextBox, repairButton, closeButton
                });

                repairForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في أداة الإصلاح: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
