using System;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    public class LoginForm : Form
    {
        private TextBox txtUsername;
        private TextBox txtPassword;
        private Button btnLogin;

        public LoginForm()
        {
            this.Text = "تسجيل الدخول";
            this.Width = 300;
            this.Height = 220;
            this.StartPosition = FormStartPosition.CenterScreen;

            Label lblUsername = new Label() { Text = "اسم المستخدم", Top = 20, Left = 10 };
            txtUsername = new TextBox() { Top = 40, Left = 10, Width = 260 };

            Label lblPassword = new Label() { Text = "كلمة المرور", Top = 80, Left = 10 };
            txtPassword = new TextBox() { Top = 100, Left = 10, Width = 260, UseSystemPasswordChar = true };

            btnLogin = new Button() { Text = "تسجيل الدخول", Top = 140, Left = 10, Width = 260 };
            btnLogin.Click += BtnLogin_Click;

            this.Controls.Add(lblUsername);
            this.Controls.Add(txtUsername);
            this.Controls.Add(lblPassword);
            this.Controls.Add(txtPassword);
            this.Controls.Add(btnLogin);
        }

        private void BtnLogin_Click(object? sender, EventArgs e)
        {
            string username = txtUsername.Text.Trim();
            string password = txtPassword.Text;

            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                MessageBox.Show("من فضلك أدخل اسم المستخدم وكلمة المرور", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // افترضنا ان VerifyUser يرجع null لو فشل، أو يرجع "Admin" أو "User"
            string? role = DatabaseHelper.VerifyUser(username, password);

            if (role == null)
            {
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة، أو الحساب معطل", "فشل تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // نجاح تسجيل الدخول
            MessageBox.Show($"تم تسجيل الدخول بنجاح كـ {role}");

            this.Hide();

            if (role == "Admin")
            {
                var adminForm = new AdminDashboard(username);
                adminForm.FormClosed += (s, args) => this.Show();
                adminForm.Show();
            }
            else
            {
                var userForm = new UserDashboard(username);
                userForm.FormClosed += (s, args) => this.Show();
                userForm.Show();
            }
        }
    }
}
