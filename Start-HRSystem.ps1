Write-Host "HR Management System v5.3.0" -ForegroundColor Green
Write-Host "Starting application..." -ForegroundColor Yellow

Set-Location $PSScriptRoot

try {
    Write-Host "Building project..." -ForegroundColor Cyan
    dotnet build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build successful! Starting application..." -ForegroundColor Green
        Write-Host ""
        Write-Host "Login credentials:" -ForegroundColor Yellow
        Write-Host "  Username: admin" -ForegroundColor White
        Write-Host "  Password: 123456" -ForegroundColor White
        Write-Host ""
        
        dotnet run
    } else {
        Write-Host "Build failed. Please check the errors above." -ForegroundColor Red
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
