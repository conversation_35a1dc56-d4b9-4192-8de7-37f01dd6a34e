using System;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    public class AdminDashboard : Form
    {
        private readonly string _username;
        private readonly DataGridView dgvUsers;
        private readonly Button btnToggleActive;
        private readonly Button btnChangePassword;

        public AdminDashboard(string username)
        {
            _username = username;

            this.Text = $"لوحة تحكم الأدمن - {_username}";
            this.Width = 700;
            this.Height = 500;
            this.StartPosition = FormStartPosition.CenterScreen;

            dgvUsers = new DataGridView()
            {
                Top = 10,
                Left = 10,
                Width = 660,
                Height = 350,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            btnToggleActive = new Button()
            {
                Text = "تفعيل / تعطيل المستخدم",
                Top = 370,
                Left = 10,
                Width = 200
            };

            btnChangePassword = new Button()
            {
                Text = "تغيير كلمة المرور",
                Top = 370,
                Left = 220,
                Width = 200
            };

            btnToggleActive.Click += BtnToggleActive_Click;
            btnChangePassword.Click += BtnChangePassword_Click;

            this.Controls.Add(dgvUsers);
            this.Controls.Add(btnToggleActive);
            this.Controls.Add(btnChangePassword);

            LoadUsers();
        }

        private void LoadUsers()
        {
            // هنا تحط الكود الخاص بتحميل بيانات المستخدمين من قاعدة البيانات
            // لتجنب تعقيد الأمور حاليا نضع مثالاً وهمياً
            dgvUsers.DataSource = DatabaseHelper.GetAllUsers();
        }

        private void BtnToggleActive_Click(object? sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("من فضلك اختر مستخدماً من القائمة");
                return;
            }

            var username = dgvUsers.SelectedRows[0].Cells["Username"].Value?.ToString();
            if (string.IsNullOrEmpty(username))
            {
                MessageBox.Show("تعذر الحصول على اسم المستخدم المحدد");
                return;
            }

            bool currentStatus = (bool)dgvUsers.SelectedRows[0].Cells["IsActive"].Value!;
            bool newStatus = !currentStatus;

            DatabaseHelper.SetUserActiveStatus(username, newStatus);
            LoadUsers();
        }

        private void BtnChangePassword_Click(object? sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("من فضلك اختر مستخدماً من القائمة");
                return;
            }

            var username = dgvUsers.SelectedRows[0].Cells["Username"].Value?.ToString();
            if (string.IsNullOrEmpty(username))
            {
                MessageBox.Show("تعذر الحصول على اسم المستخدم المحدد");
                return;
            }

            string newPassword = Prompt.ShowDialog("أدخل كلمة المرور الجديدة:", "تغيير كلمة المرور");
            if (string.IsNullOrEmpty(newPassword))
            {
                MessageBox.Show("كلمة المرور الجديدة لا يمكن أن تكون فارغة");
                return;
            }

            bool success = DatabaseHelper.ChangePassword(username, newPassword);
            if (success)
            {
                MessageBox.Show("تم تغيير كلمة المرور بنجاح");
            }
            else
            {
                MessageBox.Show("حدث خطأ أثناء تغيير كلمة المرور");
            }
        }
    }

    // صنف مساعدة لعمل نافذة حوارية بسيطة لإدخال نص (يمكنك تحسينه لاحقاً)
    public static class Prompt
    {
        public static string? ShowDialog(string text, string caption)
        {
            Form prompt = new Form()
            {
                Width = 350,
                Height = 150,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                Text = caption,
                StartPosition = FormStartPosition.CenterScreen,
                MinimizeBox = false,
                MaximizeBox = false
            };
            Label textLabel = new Label() { Left = 10, Top = 10, Text = text, AutoSize = true };
            TextBox inputBox = new TextBox() { Left = 10, Top = 35, Width = 300 };
            Button confirmation = new Button() { Text = "موافق", Left = 220, Width = 90, Top = 65, DialogResult = DialogResult.OK };

            confirmation.Click += (sender, e) => { prompt.Close(); };
            prompt.Controls.Add(textLabel);
            prompt.Controls.Add(inputBox);
            prompt.Controls.Add(confirmation);
            prompt.AcceptButton = confirmation;

            return prompt.ShowDialog() == DialogResult.OK ? inputBox.Text : null;
        }
    }
}
