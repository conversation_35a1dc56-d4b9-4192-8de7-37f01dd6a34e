using System;
using System.Drawing;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    public class AdminDashboard : Form
    {
        private readonly string _username;
        private readonly DataGridView dgvUsers;
        private readonly Button btnToggleActive;
        private readonly Button btnChangePassword;
        private readonly Button btnAddUser;
        private readonly Button btnRefresh;
        private readonly Button btnOpenHRSystem;
        private readonly Label lblWelcome;
        private readonly Label lblUsersCount;

        public AdminDashboard(string username)
        {
            _username = username;
            InitializeComponents();
            LoadUsers();
        }

        private void InitializeComponents()
        {
            // إعدادات النافذة
            this.Text = $"لوحة تحكم المدير - {_username}";
            this.Size = new Size(900, 650);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            // عنوان الترحيب
            lblWelcome = new Label
            {
                Text = $"🎯 مرحباً بك في لوحة تحكم المدير - {_username}",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Size = new Size(850, 40),
                Location = new Point(25, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // عداد المستخدمين
            lblUsersCount = new Label
            {
                Text = "عدد المستخدمين: 0",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Size = new Size(200, 25),
                Location = new Point(680, 70),
                TextAlign = ContentAlignment.MiddleRight
            };

            // جدول المستخدمين
            dgvUsers = new DataGridView
            {
                Location = new Point(25, 100),
                Size = new Size(850, 350),
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                MultiSelect = false,
                Font = new Font("Tahoma", 10),
                RowHeadersVisible = false
            };

            // تنسيق رؤوس الأعمدة
            dgvUsers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            dgvUsers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvUsers.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 11, FontStyle.Bold);
            dgvUsers.ColumnHeadersHeight = 35;

            // تنسيق الصفوف
            dgvUsers.DefaultCellStyle.SelectionBackColor = Color.FromArgb(46, 204, 113);
            dgvUsers.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvUsers.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);

            // أزرار التحكم
            btnRefresh = new Button
            {
                Text = "🔄 تحديث القائمة",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(150, 40),
                Location = new Point(25, 470),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            btnAddUser = new Button
            {
                Text = "➕ إضافة مستخدم",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(150, 40),
                Location = new Point(190, 470),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            btnToggleActive = new Button
            {
                Text = "🔄 تفعيل/تعطيل",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(150, 40),
                Location = new Point(355, 470),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            btnChangePassword = new Button
            {
                Text = "🔑 تغيير كلمة المرور",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(150, 40),
                Location = new Point(520, 470),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            btnOpenHRSystem = new Button
            {
                Text = "🏢 فتح نظام الموارد البشرية",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(200, 40),
                Location = new Point(25, 530),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            // ربط الأحداث
            btnRefresh.Click += BtnRefresh_Click;
            btnAddUser.Click += BtnAddUser_Click;
            btnToggleActive.Click += BtnToggleActive_Click;
            btnChangePassword.Click += BtnChangePassword_Click;
            btnOpenHRSystem.Click += BtnOpenHRSystem_Click;

            // إضافة العناصر للنافذة
            this.Controls.AddRange(new Control[] {
                lblWelcome, lblUsersCount, dgvUsers,
                btnRefresh, btnAddUser, btnToggleActive, btnChangePassword, btnOpenHRSystem
            });
        }

        private void LoadUsers()
        {
            try
            {
                var dataTable = DatabaseHelper.GetAllUsers();
                dgvUsers.DataSource = dataTable;

                // تخصيص أسماء الأعمدة
                if (dgvUsers.Columns.Count > 0)
                {
                    dgvUsers.Columns["Username"].HeaderText = "اسم المستخدم";
                    dgvUsers.Columns["Role"].HeaderText = "الصلاحية";
                    dgvUsers.Columns["IsActive"].HeaderText = "نشط";
                    dgvUsers.Columns["Status"].HeaderText = "الحالة";

                    // إخفاء عمود IsActive وإظهار Status بدلاً منه
                    dgvUsers.Columns["IsActive"].Visible = false;
                }

                // تحديث عداد المستخدمين
                lblUsersCount.Text = $"عدد المستخدمين: {dataTable.Rows.Count}";

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {dataTable.Rows.Count} مستخدم");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المستخدمين:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل المستخدمين: {ex.Message}");
            }
        }

        private void BtnRefresh_Click(object? sender, EventArgs e)
        {
            LoadUsers();
            MessageBox.Show("✅ تم تحديث قائمة المستخدمين بنجاح!",
                "تحديث", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnAddUser_Click(object? sender, EventArgs e)
        {
            try
            {
                var addUserForm = new AddUserForm();
                if (addUserForm.ShowDialog() == DialogResult.OK)
                {
                    LoadUsers();
                    MessageBox.Show("✅ تم إضافة المستخدم بنجاح!",
                        "إضافة مستخدم", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إضافة المستخدم:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnToggleActive_Click(object? sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("⚠️ من فضلك اختر مستخدماً من القائمة",
                    "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var username = dgvUsers.SelectedRows[0].Cells["Username"].Value?.ToString();
            if (string.IsNullOrEmpty(username))
            {
                MessageBox.Show("❌ تعذر الحصول على اسم المستخدم المحدد",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // منع المدير من تعطيل نفسه
            if (username.Equals(_username, StringComparison.OrdinalIgnoreCase))
            {
                MessageBox.Show("⚠️ لا يمكنك تعطيل حسابك الخاص!",
                    "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            bool currentStatus = (bool)dgvUsers.SelectedRows[0].Cells["IsActive"].Value!;
            bool newStatus = !currentStatus;
            string action = newStatus ? "تفعيل" : "تعطيل";

            var result = MessageBox.Show($"هل تريد {action} المستخدم '{username}'؟",
                "تأكيد العملية", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    DatabaseHelper.SetUserActiveStatus(username, newStatus);
                    LoadUsers();
                    MessageBox.Show($"✅ تم {action} المستخدم '{username}' بنجاح!",
                        "نجح التحديث", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"❌ خطأ في {action} المستخدم:\n{ex.Message}",
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnChangePassword_Click(object? sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("⚠️ من فضلك اختر مستخدماً من القائمة",
                    "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var username = dgvUsers.SelectedRows[0].Cells["Username"].Value?.ToString();
            if (string.IsNullOrEmpty(username))
            {
                MessageBox.Show("❌ تعذر الحصول على اسم المستخدم المحدد",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            string newPassword = Prompt.ShowDialog($"أدخل كلمة المرور الجديدة للمستخدم '{username}':", "تغيير كلمة المرور");
            if (string.IsNullOrEmpty(newPassword))
            {
                MessageBox.Show("⚠️ كلمة المرور الجديدة لا يمكن أن تكون فارغة",
                    "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (newPassword.Length < 3)
            {
                MessageBox.Show("⚠️ كلمة المرور يجب أن تكون 3 أحرف على الأقل",
                    "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                bool success = DatabaseHelper.ChangePassword(username, newPassword);
                if (success)
                {
                    MessageBox.Show($"✅ تم تغيير كلمة المرور للمستخدم '{username}' بنجاح!\n\nكلمة المرور الجديدة: {newPassword}",
                        "نجح التحديث", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("❌ حدث خطأ أثناء تغيير كلمة المرور",
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تغيير كلمة المرور:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnOpenHRSystem_Click(object? sender, EventArgs e)
        {
            try
            {
                var hrForm = new Form1();
                hrForm.Show();
                MessageBox.Show("✅ تم فتح نظام إدارة الموارد البشرية!",
                    "فتح النظام", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في فتح نظام الموارد البشرية:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    // صنف مساعدة لعمل نافذة حوارية بسيطة لإدخال نص (يمكنك تحسينه لاحقاً)
    public static class Prompt
    {
        public static string? ShowDialog(string text, string caption)
        {
            Form prompt = new Form()
            {
                Width = 350,
                Height = 150,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                Text = caption,
                StartPosition = FormStartPosition.CenterScreen,
                MinimizeBox = false,
                MaximizeBox = false
            };
            Label textLabel = new Label() { Left = 10, Top = 10, Text = text, AutoSize = true };
            TextBox inputBox = new TextBox() { Left = 10, Top = 35, Width = 300 };
            Button confirmation = new Button() { Text = "موافق", Left = 220, Width = 90, Top = 65, DialogResult = DialogResult.OK };

            confirmation.Click += (sender, e) => { prompt.Close(); };
            prompt.Controls.Add(textLabel);
            prompt.Controls.Add(inputBox);
            prompt.Controls.Add(confirmation);
            prompt.AcceptButton = confirmation;

            return prompt.ShowDialog() == DialogResult.OK ? inputBox.Text : null;
        }
    }
}
