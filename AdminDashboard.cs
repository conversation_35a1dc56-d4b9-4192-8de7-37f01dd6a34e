using System;
using System.Drawing;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    public class AdminDashboard : Form
    {
        private readonly string _username;
        private DataGridView dgvUsers;
        private Button btnToggleActive;
        private Button btnChangePassword;
        private Button btnAddUser;
        private Button btnRefresh;
        private Button btnOpenHRSystem;
        private Button btnDiagnoseAccess;
        private Button btnBackupManager;
        private Button btnEmailSettings;
        private Button btnPortableInfo;
        private Button btnGenerateReport;
        private Label lblWelcome;
        private Label lblUsersCount;

        public AdminDashboard(string username)
        {
            _username = username;

            try
            {
                System.Diagnostics.Debug.WriteLine($"🔧 بدء إنشاء لوحة تحكم المدير للمستخدم: {username}");

                InitializeComponents();
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء المكونات بنجاح");

                LoadUsers();
                System.Diagnostics.Debug.WriteLine("✅ تم تحميل المستخدمين بنجاح");

                // التأكد من ظهور النافذة
                this.Show();
                this.BringToFront();
                this.Focus();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء لوحة تحكم المدير بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء لوحة تحكم المدير: {ex.Message}");
                MessageBox.Show($"❌ خطأ في إنشاء لوحة تحكم المدير:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void InitializeComponents()
        {
            // إعدادات النافذة
            this.Text = $"لوحة تحكم المدير - {_username}";
            this.Size = new Size(900, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            // عنوان الترحيب
            lblWelcome = new Label
            {
                Text = $"🎯 مرحباً بك في لوحة تحكم المدير - {_username}",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(44, 62, 80),
                Size = new Size(850, 40),
                Location = new Point(25, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // عداد المستخدمين
            lblUsersCount = new Label
            {
                Text = "عدد المستخدمين: 0",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Size = new Size(200, 25),
                Location = new Point(680, 70),
                TextAlign = ContentAlignment.MiddleRight
            };

            // جدول المستخدمين
            dgvUsers = new DataGridView
            {
                Location = new Point(25, 100),
                Size = new Size(850, 350),
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                MultiSelect = false,
                Font = new Font("Tahoma", 10),
                RowHeadersVisible = false
            };

            // تنسيق رؤوس الأعمدة
            dgvUsers.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 152, 219);
            dgvUsers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvUsers.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 11, FontStyle.Bold);
            dgvUsers.ColumnHeadersHeight = 35;

            // تنسيق الصفوف
            dgvUsers.DefaultCellStyle.SelectionBackColor = Color.FromArgb(46, 204, 113);
            dgvUsers.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvUsers.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);

            // أزرار التحكم
            btnRefresh = new Button
            {
                Text = "🔄 تحديث القائمة",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(150, 40),
                Location = new Point(25, 470),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            btnAddUser = new Button
            {
                Text = "➕ إضافة مستخدم",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(150, 40),
                Location = new Point(190, 470),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            btnToggleActive = new Button
            {
                Text = "🔄 تفعيل/تعطيل",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(150, 40),
                Location = new Point(355, 470),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            btnChangePassword = new Button
            {
                Text = "🔑 تغيير كلمة المرور",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(150, 40),
                Location = new Point(520, 470),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            btnOpenHRSystem = new Button
            {
                Text = "🏢 فتح نظام الموارد البشرية",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(200, 40),
                Location = new Point(25, 530),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            btnDiagnoseAccess = new Button
            {
                Text = "🔍 تشخيص قاعدة بيانات Access",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(180, 35),
                Location = new Point(250, 530),
                BackColor = Color.FromArgb(142, 68, 173),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            btnBackupManager = new Button
            {
                Text = "💾 إدارة النسخ الاحتياطي",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(180, 35),
                Location = new Point(450, 530),
                BackColor = Color.FromArgb(39, 174, 96),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            btnEmailSettings = new Button
            {
                Text = "📧 إعدادات البريد الإلكتروني",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(180, 35),
                Location = new Point(650, 530),
                BackColor = Color.FromArgb(243, 156, 18),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            btnPortableInfo = new Button
            {
                Text = "💿 معلومات النسخة المحمولة",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(180, 35),
                Location = new Point(25, 580),
                BackColor = Color.FromArgb(52, 73, 94),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            btnGenerateReport = new Button
            {
                Text = "📄 تقرير شامل PDF",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(180, 35),
                Location = new Point(250, 580),
                BackColor = Color.FromArgb(211, 84, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            // ربط الأحداث
            btnRefresh.Click += BtnRefresh_Click;
            btnAddUser.Click += BtnAddUser_Click;
            btnToggleActive.Click += BtnToggleActive_Click;
            btnChangePassword.Click += BtnChangePassword_Click;
            btnOpenHRSystem.Click += BtnOpenHRSystem_Click;
            btnDiagnoseAccess.Click += BtnDiagnoseAccess_Click;
            btnBackupManager.Click += BtnBackupManager_Click;
            btnEmailSettings.Click += BtnEmailSettings_Click;
            btnPortableInfo.Click += BtnPortableInfo_Click;
            btnGenerateReport.Click += BtnGenerateReport_Click;

            // إضافة العناصر للنافذة
            this.Controls.AddRange(new Control[] {
                lblWelcome, lblUsersCount, dgvUsers,
                btnRefresh, btnAddUser, btnToggleActive, btnChangePassword, btnOpenHRSystem,
                btnDiagnoseAccess, btnBackupManager, btnEmailSettings, btnPortableInfo, btnGenerateReport
            });
        }

        private void LoadUsers()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 بدء تحميل المستخدمين...");

                // التأكد من إنشاء قاعدة البيانات والمستخدمين الافتراضيين
                DatabaseHelper.CreateDatabaseAndTable();
                System.Diagnostics.Debug.WriteLine("✅ تم التأكد من قاعدة البيانات");

                var dataTable = DatabaseHelper.GetAllUsers();
                System.Diagnostics.Debug.WriteLine($"✅ تم جلب {dataTable.Rows.Count} مستخدم من قاعدة البيانات");

                if (dataTable.Rows.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد مستخدمين في قاعدة البيانات، سيتم إنشاء بيانات تجريبية");

                    // إضافة بيانات تجريبية
                    dataTable.Rows.Add("admin", "Admin", true, "نشط");
                    dataTable.Rows.Add("مدير", "Admin", true, "نشط");
                    dataTable.Rows.Add("hr", "User", true, "نشط");
                    dataTable.Rows.Add("موارد", "User", true, "نشط");
                    dataTable.Rows.Add("user", "User", true, "نشط");

                    System.Diagnostics.Debug.WriteLine("✅ تم إضافة 5 مستخدمين تجريبيين");
                }

                dgvUsers.DataSource = dataTable;
                System.Diagnostics.Debug.WriteLine("✅ تم ربط البيانات بالجدول");

                // تخصيص أسماء الأعمدة
                if (dgvUsers.Columns.Count > 0)
                {
                    dgvUsers.Columns["Username"].HeaderText = "اسم المستخدم";
                    dgvUsers.Columns["Role"].HeaderText = "الصلاحية";
                    dgvUsers.Columns["IsActive"].HeaderText = "نشط";
                    dgvUsers.Columns["Status"].HeaderText = "الحالة";

                    // إخفاء عمود IsActive وإظهار Status بدلاً منه
                    dgvUsers.Columns["IsActive"].Visible = false;

                    System.Diagnostics.Debug.WriteLine("✅ تم تخصيص أسماء الأعمدة");
                }

                // تحديث عداد المستخدمين
                lblUsersCount.Text = $"عدد المستخدمين: {dataTable.Rows.Count}";
                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث العداد: {dataTable.Rows.Count} مستخدم");

                // إجبار تحديث الواجهة
                dgvUsers.Refresh();
                this.Refresh();

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {dataTable.Rows.Count} مستخدم بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل المستخدمين: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ تفاصيل الخطأ: {ex.StackTrace}");

                MessageBox.Show($"❌ خطأ في تحميل المستخدمين:\n{ex.Message}\n\nسيتم عرض بيانات تجريبية.",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);

                // إنشاء بيانات تجريبية في حالة الخطأ
                try
                {
                    var fallbackTable = new System.Data.DataTable();
                    fallbackTable.Columns.Add("Username", typeof(string));
                    fallbackTable.Columns.Add("Role", typeof(string));
                    fallbackTable.Columns.Add("IsActive", typeof(bool));
                    fallbackTable.Columns.Add("Status", typeof(string));

                    fallbackTable.Rows.Add("admin", "Admin", true, "نشط");
                    fallbackTable.Rows.Add("مدير", "Admin", true, "نشط");
                    fallbackTable.Rows.Add("hr", "User", true, "نشط");
                    fallbackTable.Rows.Add("موارد", "User", true, "نشط");
                    fallbackTable.Rows.Add("user", "User", true, "نشط");

                    dgvUsers.DataSource = fallbackTable;
                    lblUsersCount.Text = $"عدد المستخدمين: {fallbackTable.Rows.Count} (بيانات تجريبية)";

                    System.Diagnostics.Debug.WriteLine("✅ تم عرض البيانات التجريبية");
                }
                catch (Exception fallbackEx)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ فشل في عرض البيانات التجريبية: {fallbackEx.Message}");
                }
            }
        }

        private void BtnRefresh_Click(object? sender, EventArgs e)
        {
            LoadUsers();
            MessageBox.Show("✅ تم تحديث قائمة المستخدمين بنجاح!",
                "تحديث", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BtnAddUser_Click(object? sender, EventArgs e)
        {
            try
            {
                var addUserForm = new AddUserForm();
                if (addUserForm.ShowDialog() == DialogResult.OK)
                {
                    LoadUsers();
                    MessageBox.Show("✅ تم إضافة المستخدم بنجاح!",
                        "إضافة مستخدم", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إضافة المستخدم:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnToggleActive_Click(object? sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("⚠️ من فضلك اختر مستخدماً من القائمة",
                    "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var username = dgvUsers.SelectedRows[0].Cells["Username"].Value?.ToString();
            if (string.IsNullOrEmpty(username))
            {
                MessageBox.Show("❌ تعذر الحصول على اسم المستخدم المحدد",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // منع المدير من تعطيل نفسه
            if (username.Equals(_username, StringComparison.OrdinalIgnoreCase))
            {
                MessageBox.Show("⚠️ لا يمكنك تعطيل حسابك الخاص!",
                    "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            bool currentStatus = (bool)dgvUsers.SelectedRows[0].Cells["IsActive"].Value!;
            bool newStatus = !currentStatus;
            string action = newStatus ? "تفعيل" : "تعطيل";

            var result = MessageBox.Show($"هل تريد {action} المستخدم '{username}'؟",
                "تأكيد العملية", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    DatabaseHelper.SetUserActiveStatus(username, newStatus);
                    LoadUsers();
                    MessageBox.Show($"✅ تم {action} المستخدم '{username}' بنجاح!",
                        "نجح التحديث", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"❌ خطأ في {action} المستخدم:\n{ex.Message}",
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnChangePassword_Click(object? sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count == 0)
            {
                MessageBox.Show("⚠️ من فضلك اختر مستخدماً من القائمة",
                    "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var username = dgvUsers.SelectedRows[0].Cells["Username"].Value?.ToString();
            if (string.IsNullOrEmpty(username))
            {
                MessageBox.Show("❌ تعذر الحصول على اسم المستخدم المحدد",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            string newPassword = Prompt.ShowDialog($"أدخل كلمة المرور الجديدة للمستخدم '{username}':", "تغيير كلمة المرور");
            if (string.IsNullOrEmpty(newPassword))
            {
                MessageBox.Show("⚠️ كلمة المرور الجديدة لا يمكن أن تكون فارغة",
                    "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (newPassword.Length < 3)
            {
                MessageBox.Show("⚠️ كلمة المرور يجب أن تكون 3 أحرف على الأقل",
                    "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                bool success = DatabaseHelper.ChangePassword(username, newPassword);
                if (success)
                {
                    MessageBox.Show($"✅ تم تغيير كلمة المرور للمستخدم '{username}' بنجاح!\n\nكلمة المرور الجديدة: {newPassword}",
                        "نجح التحديث", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("❌ حدث خطأ أثناء تغيير كلمة المرور",
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تغيير كلمة المرور:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnOpenHRSystem_Click(object? sender, EventArgs e)
        {
            try
            {
                var hrForm = new Form1();
                hrForm.Show();
                MessageBox.Show("✅ تم فتح نظام إدارة الموارد البشرية!",
                    "فتح النظام", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في فتح نظام الموارد البشرية:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnDiagnoseAccess_Click(object? sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 بدء تشخيص قاعدة بيانات Access...");

                // تشخيص قاعدة بيانات Access
                string diagnosis = DatabaseHelper.DiagnoseAccessDatabase();

                // عرض النتائج في نافذة منفصلة
                var diagnosisForm = new Form
                {
                    Text = "تشخيص قاعدة بيانات Access",
                    Size = new Size(700, 500),
                    StartPosition = FormStartPosition.CenterParent,
                    FormBorderStyle = FormBorderStyle.Sizable
                };

                var textBox = new TextBox
                {
                    Multiline = true,
                    ScrollBars = ScrollBars.Both,
                    Dock = DockStyle.Fill,
                    Font = new Font("Consolas", 10),
                    Text = diagnosis,
                    ReadOnly = true
                };

                var closeButton = new Button
                {
                    Text = "إغلاق",
                    Size = new Size(100, 30),
                    Anchor = AnchorStyles.Bottom | AnchorStyles.Right,
                    Location = new Point(580, 430)
                };

                closeButton.Click += (s, args) => diagnosisForm.Close();

                diagnosisForm.Controls.Add(textBox);
                diagnosisForm.Controls.Add(closeButton);

                diagnosisForm.ShowDialog();

                System.Diagnostics.Debug.WriteLine("✅ تم عرض تشخيص قاعدة بيانات Access");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تشخيص قاعدة بيانات Access: {ex.Message}");
                MessageBox.Show($"❌ خطأ في تشخيص قاعدة بيانات Access:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnBackupManager_Click(object? sender, EventArgs e)
        {
            try
            {
                var backups = BackupManager.GetAvailableBackups();

                var backupForm = new Form
                {
                    Text = "إدارة النسخ الاحتياطي",
                    Size = new Size(800, 600),
                    StartPosition = FormStartPosition.CenterParent
                };

                var dgvBackups = new DataGridView
                {
                    Dock = DockStyle.Fill,
                    DataSource = backups,
                    ReadOnly = true,
                    SelectionMode = DataGridViewSelectionMode.FullRowSelect
                };

                var panel = new Panel { Dock = DockStyle.Bottom, Height = 50 };

                var btnCreateBackup = new Button
                {
                    Text = "إنشاء نسخة احتياطية",
                    Size = new Size(150, 30),
                    Location = new Point(10, 10),
                    BackColor = Color.Green,
                    ForeColor = Color.White
                };

                var btnClose = new Button
                {
                    Text = "إغلاق",
                    Size = new Size(100, 30),
                    Location = new Point(680, 10),
                    BackColor = Color.Gray,
                    ForeColor = Color.White
                };

                btnCreateBackup.Click += async (s, args) =>
                {
                    try
                    {
                        bool success = await BackupManager.CreateManualBackup();
                        if (success)
                        {
                            MessageBox.Show("✅ تم إنشاء النسخة الاحتياطية بنجاح!", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            dgvBackups.DataSource = BackupManager.GetAvailableBackups();
                        }
                        else
                        {
                            MessageBox.Show("❌ فشل في إنشاء النسخة الاحتياطية", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"❌ خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };

                btnClose.Click += (s, args) => backupForm.Close();

                panel.Controls.AddRange(new Control[] { btnCreateBackup, btnClose });
                backupForm.Controls.AddRange(new Control[] { dgvBackups, panel });
                backupForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إدارة النسخ الاحتياطي:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEmailSettings_Click(object? sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("📧 إعدادات البريد الإلكتروني\n\nيمكنك تكوين إعدادات البريد الإلكتروني من خلال ملف email_settings.json",
                    "إعدادات البريد", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إعدادات البريد الإلكتروني:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnPortableInfo_Click(object? sender, EventArgs e)
        {
            try
            {
                var portableInfo = PortableManager.GetPortableInfo();

                string info = "معلومات النسخة المحمولة\n";
                info += "========================\n\n";
                info += $"الوضع: {(portableInfo.IsPortableMode ? "نسخة محمولة 💿" : "نسخة عادية 💻")}\n";
                info += $"مسار التطبيق: {portableInfo.ApplicationPath}\n";
                info += $"مسار البيانات: {portableInfo.DataPath}\n";
                info += $"نوع القرص: {portableInfo.DriveType}\n";
                info += $"المساحة الإجمالية: {portableInfo.TotalSpaceFormatted}\n";
                info += $"المساحة المتاحة: {portableInfo.FreeSpaceFormatted}\n";
                info += $"ملف التكوين موجود: {(portableInfo.ConfigExists ? "نعم ✅" : "لا ❌")}\n";
                info += $"صحة التثبيت: {(portableInfo.IsValid ? "صحيح ✅" : "يحتاج مراجعة ⚠️")}\n\n";

                if (portableInfo.IsPortableMode)
                {
                    info += "مزايا النسخة المحمولة:\n";
                    info += "• يمكن تشغيلها من أي جهاز\n";
                    info += "• جميع البيانات محفوظة في مجلد واحد\n";
                    info += "• سهولة النقل والنسخ الاحتياطي\n";
                    info += "• لا تحتاج تثبيت على النظام\n";
                }

                MessageBox.Show(info, "معلومات النسخة المحمولة", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في عرض معلومات النسخة المحمولة:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnGenerateReport_Click(object? sender, EventArgs e)
        {
            try
            {
                Task.Run(async () =>
                {
                    try
                    {
                        string reportPath = await PDFReportGenerator.GenerateAllEmployeesReport();

                        this.Invoke((MethodInvoker)delegate
                        {
                            MessageBox.Show($"✅ تم إنشاء التقرير الشامل بنجاح!\n\nمسار الملف:\n{reportPath}",
                                "تقرير PDF", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        });
                    }
                    catch (Exception ex)
                    {
                        this.Invoke((MethodInvoker)delegate
                        {
                            MessageBox.Show($"❌ خطأ في إنشاء التقرير:\n{ex.Message}",
                                "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        });
                    }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إنشاء التقرير:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    // صنف مساعدة لعمل نافذة حوارية بسيطة لإدخال نص (يمكنك تحسينه لاحقاً)
    public static class Prompt
    {
        public static string? ShowDialog(string text, string caption)
        {
            Form prompt = new Form()
            {
                Width = 350,
                Height = 150,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                Text = caption,
                StartPosition = FormStartPosition.CenterScreen,
                MinimizeBox = false,
                MaximizeBox = false
            };
            Label textLabel = new Label() { Left = 10, Top = 10, Text = text, AutoSize = true };
            TextBox inputBox = new TextBox() { Left = 10, Top = 35, Width = 300 };
            Button confirmation = new Button() { Text = "موافق", Left = 220, Width = 90, Top = 65, DialogResult = DialogResult.OK };

            confirmation.Click += (sender, e) => { prompt.Close(); };
            prompt.Controls.Add(textLabel);
            prompt.Controls.Add(inputBox);
            prompt.Controls.Add(confirmation);
            prompt.AcceptButton = confirmation;

            return prompt.ShowDialog() == DialogResult.OK ? inputBox.Text : null;
        }
    }
}
