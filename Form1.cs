using System;
using System.Windows.Forms;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Data.SqlClient;
using System.Data;
using System.Collections.Generic;
using System.Globalization;

namespace Ahmedapp_for_work
{
    public partial class Form1 : Form
    {
        private const string DEVELOPER_NAME = "أحمد ابراهيم";
        private const string DEVELOPER_EMAIL = "<EMAIL>";
        private const string APPLICATION_ID = "9a4390b8-85a4-48a6-8703-e818595b780b";
        private readonly Guid AppInstanceId = Guid.Parse("9a4390b8-85a4-48a6-8703-e818595b780b");

        // Main panels
        private Panel sidePanel = null!;
        private Panel mainPanel = null!;
        private Panel headerPanel = null!;
        private Panel contentPanel = null!;

        // Data managers
        private NotificationManager notificationManager = null!;
        private NoteManager noteManager = null!;
        private TipManager tipManager = null!;
        private EmployeeManager employeeManager = null!;

        // Navigation buttons - تم تنظيف القائمة وإزالة الخانات غير المطلوبة
        private Button btnEmployees = null!;
        private Button btnForms = null!;
        private Button btnReports = null!;
        private Button btnAdvancedSearch = null!;

        public Form1()
        {
            InitializeComponent();
            InitializeDataManagers();
            SetupMainInterface();
        }

        private void InitializeDataManagers()
        {
            try
            {
                notificationManager = new NotificationManager();
                noteManager = new NoteManager();
                tipManager = new TipManager();
                employeeManager = new EmployeeManager();

                // محاولة إنشاء قاعدة البيانات إذا لم تكن موجودة
                try
                {
                    employeeManager.InitializeDatabase();
                }
                catch (Exception dbEx)
                {
                    System.Diagnostics.Debug.WriteLine($"تحذير: فشل في إنشاء قاعدة البيانات: {dbEx.Message}");
                }
            }
            catch (Exception ex)
            {
                // إذا فشل تحميل المدراء، استخدم وضع البيانات التجريبية
                System.Diagnostics.Debug.WriteLine($"تحذير: فشل تحميل مدراء البيانات: {ex.Message}");
                MessageBox.Show($"تحذير: لم يتم الاتصال بقاعدة البيانات. سيتم استخدام البيانات التجريبية.\n\nالخطأ: {ex.Message}",
                              "تحذير قاعدة البيانات", MessageBoxButtons.OK, MessageBoxIcon.Warning);

                // إنشاء مدراء فارغين للعمل بدون قاعدة بيانات
                try
                {
                    notificationManager = new NotificationManager();
                    noteManager = new NoteManager();
                    tipManager = new TipManager();
                    employeeManager = new EmployeeManager();
                }
                catch
                {
                    // في حالة فشل كامل، استخدم قيم افتراضية
                    notificationManager = null!;
                    noteManager = null!;
                    tipManager = null!;
                    employeeManager = null!;
                }
            }
        }

        private void SetupMainInterface()
        {
            this.Text = "نظام إدارة الموارد البشرية - HR Management System";
            this.Size = new Size(1600, 900);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Normal;
            this.MinimumSize = new Size(1200, 700);
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.AllowDrop = true; // تفعيل إدراج الملفات

            // إضافة أحداث إدراج الملفات
            this.DragEnter += Form1_DragEnter;
            this.DragDrop += Form1_DragDrop;

            // Create panels in correct order
            CreateHeaderPanel();
            CreateMainPanel();
            CreateSidePanel();

            // Load default view
            LoadWelcomeView();
        }

        private void CreateHeaderPanel()
        {
            headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 70,
                BackColor = Color.FromArgb(41, 128, 185)
            };

            Label titleLabel = new Label
            {
                Text = "قسم المعاشات",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(30, 15),
                Size = new Size(200, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            Label subtitleLabel = new Label
            {
                Text = $"Pensions Department - الإصدار 3.1.0 | ID: {APPLICATION_ID.Substring(0, 8)}",
                Font = new Font("Arial", 10),
                ForeColor = Color.LightBlue,
                Location = new Point(30, 40),
                Size = new Size(500, 20),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // Developer info panel - positioned on the right
            Panel developerPanel = new Panel
            {
                Size = new Size(350, 60),
                Location = new Point(900, 5),
                BackColor = Color.FromArgb(52, 152, 219),
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };

            Label developerTitle = new Label
            {
                Text = "معلومات المطور",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(5, 5),
                Size = new Size(340, 18),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label developerName = new Label
            {
                Text = $"المطور: {DEVELOPER_NAME}",
                Font = new Font("Tahoma", 8),
                ForeColor = Color.White,
                Location = new Point(5, 25),
                Size = new Size(340, 15),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label developerEmail = new Label
            {
                Text = $"البريد: {DEVELOPER_EMAIL}",
                Font = new Font("Arial", 8),
                ForeColor = Color.White,
                Location = new Point(5, 40),
                Size = new Size(340, 15),
                TextAlign = ContentAlignment.MiddleCenter
            };

            developerPanel.Controls.AddRange(new Control[] { developerTitle, developerName, developerEmail });
            headerPanel.Controls.AddRange(new Control[] { titleLabel, subtitleLabel, developerPanel });
            this.Controls.Add(headerPanel);
        }

        private void CreateSidePanel()
        {
            sidePanel = new Panel
            {
                Dock = DockStyle.Right,
                Width = 280,
                BackColor = Color.FromArgb(52, 73, 94),
                AutoScroll = true
            };

            // Add title for sidebar
            Label sidebarTitle = new Label
            {
                Text = "القوائم الرئيسية",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 10),
                Size = new Size(260, 25),
                TextAlign = ContentAlignment.MiddleCenter
            };
            sidePanel.Controls.Add(sidebarTitle);

            // Navigation buttons - قائمة منظفة ومحسنة للأداء
            btnEmployees = CreateNavButton("👥 الموظفين", 0);
            btnForms = CreateNavButton("📋 النماذج", 1);
            btnReports = CreateNavButton("📊 التقارير", 2);
            btnAdvancedSearch = CreateNavButton("🔍 البحث المتقدم", 3);

            // ربط الأحداث للأزرار المحدثة
            btnEmployees.Click += (s, e) => LoadEmployeesView();
            btnForms.Click += (s, e) => LoadFormsView();
            btnReports.Click += (s, e) => LoadReportsView();
            btnAdvancedSearch.Click += (s, e) => LoadAdvancedSearchView();

            // إضافة الأزرار المحدثة للشريط الجانبي
            sidePanel.Controls.AddRange(new Control[] {
                btnEmployees, btnForms, btnReports, btnAdvancedSearch
            });

            this.Controls.Add(sidePanel);
        }

        private Button CreateNavButton(string text, int index)
        {
            Button button = new Button
            {
                Size = new Size(260, 45),
                Location = new Point(10, 45 + (index * 55)),
                BackColor = Color.FromArgb(44, 62, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                Cursor = Cursors.Hand,
                UseVisualStyleBackColor = false,
                Text = text
            };

            // Add hover effects
            button.MouseEnter += (s, e) => {
                button.BackColor = Color.FromArgb(52, 152, 219);
            };

            button.MouseLeave += (s, e) => {
                button.BackColor = Color.FromArgb(44, 62, 80);
            };

            return button;
        }

        private void CreateMainPanel()
        {
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(20),
                AutoScroll = true
            };

            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                AutoScroll = true,
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(10)
            };

            mainPanel.Controls.Add(contentPanel);
            this.Controls.Add(mainPanel);
        }

        private void LoadWelcomeView()
        {
            ClearContentPanel();

            Label welcomeLabel = new Label
            {
                Text = "مرحباً بك في نظام إدارة الموارد البشرية",
                Font = new Font("Tahoma", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(50, 50),
                Size = new Size(800, 40),
                TextAlign = ContentAlignment.MiddleLeft
            };

            Label welcomeSubLabel = new Label
            {
                Text = "Welcome to HR Management System",
                Font = new Font("Arial", 18),
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(50, 100),
                Size = new Size(600, 30),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // Developer info in main area
            Panel developerInfoPanel = new Panel
            {
                Size = new Size(500, 200),
                Location = new Point(50, 150),
                BackColor = Color.FromArgb(236, 240, 241),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label devInfoTitle = new Label
            {
                Text = "معلومات المطور - Developer Information",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(20, 20),
                Size = new Size(460, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label devName = new Label
            {
                Text = $"👨‍💻 اسم المطور: {DEVELOPER_NAME}",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 60),
                Size = new Size(460, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            Label devEmail = new Label
            {
                Text = $"📧 البريد الإلكتروني: {DEVELOPER_EMAIL}",
                Font = new Font("Arial", 12),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 90),
                Size = new Size(460, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            Label devVersion = new Label
            {
                Text = "🚀 الإصدار: 3.1.0 - Version: 3.1.0",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 120),
                Size = new Size(460, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            Label devDate = new Label
            {
                Text = $"📅 تاريخ التطوير: {DateTime.Now.Year}",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 150),
                Size = new Size(460, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            developerInfoPanel.Controls.AddRange(new Control[] {
                devInfoTitle, devName, devEmail, devVersion, devDate
            });

            Label instructionLabel = new Label
            {
                Text = "استخدم الأزرار في الشريط الجانبي للتنقل بين أقسام النظام المختلفة",
                Font = new Font("Tahoma", 14),
                ForeColor = Color.FromArgb(155, 89, 182),
                Location = new Point(50, 400),
                Size = new Size(800, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label newFeaturesLabel = new Label
            {
                Text = "🆕 الميزات الجديدة في الإصدار 3.1.0: التنبيهات 🔔 | الملاحظات 📝 | التلميحات 💡",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(50, 440),
                Size = new Size(800, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label dragDropLabel = new Label
            {
                Text = "📁 ميزة جديدة: اسحب وأفلت الملفات في أي مكان في النافذة لرفعها تلقائياً!",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(230, 126, 34),
                Location = new Point(50, 480),
                Size = new Size(800, 35),
                TextAlign = ContentAlignment.MiddleCenter
            };

            contentPanel.Controls.AddRange(new Control[] {
                welcomeLabel, welcomeSubLabel, developerInfoPanel, instructionLabel, newFeaturesLabel, dragDropLabel
            });
        }

        private void LoadSimpleView(string arabicTitle, string englishTitle)
        {
            ClearContentPanel();

            Label titleLabel = new Label
            {
                Text = $"{arabicTitle} - {englishTitle}",
                Font = new Font("Tahoma", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(30, 20),
                Size = new Size(800, 35),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // إضافة محتوى تفاعلي لكل قسم
            Panel contentArea = new Panel
            {
                Location = new Point(30, 70),
                Size = new Size(contentPanel.Width - 80, 500),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true
            };

            CreateSectionContent(contentArea, englishTitle);

            Label devLabel = new Label
            {
                Text = $"تم تطوير هذا النظام بواسطة: {DEVELOPER_NAME}",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(30, 590),
                Size = new Size(600, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            contentPanel.Controls.AddRange(new Control[] { titleLabel, contentArea, devLabel });
        }

        private void CreateSectionContent(Panel container, string sectionType)
        {
            switch (sectionType)
            {
                case "Employee Management":
                    CreateEmployeeManagementContent(container);
                    break;
                case "Forms Management":
                    CreateFormsManagementContent(container);
                    break;
                case "Work Injuries":
                    CreateWorkInjuriesContent(container);
                    break;
                case "Unpaid Leave":
                    CreateUnpaidLeaveContent(container);
                    break;
                case "City Council Delegates":
                    CreateDelegatesContent(container);
                    break;
                case "Monthly Achievements":
                    CreateAchievementsContent(container);
                    break;
                case "Reports":
                    CreateReportsContent(container);
                    break;
                default:
                    CreateDefaultContent(container, sectionType);
                    break;
            }
        }

        private void CreateEmployeeManagementContent(Panel container)
        {
            Label titleLabel = new Label
            {
                Text = "إدارة الموظفين - Employee Management",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(20, 20),
                Size = new Size(400, 30)
            };

            Button addEmployeeBtn = new Button
            {
                Text = "إضافة موظف جديد\nAdd New Employee",
                Size = new Size(150, 60),
                Location = new Point(20, 60),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            addEmployeeBtn.Click += (s, e) => ShowAddEmployeeDialog();

            Button viewEmployeesBtn = new Button
            {
                Text = "عرض الموظفين\nView Employees",
                Size = new Size(150, 60),
                Location = new Point(180, 60),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            viewEmployeesBtn.Click += (s, e) => LoadEmployeesView();

            Button searchEmployeeBtn = new Button
            {
                Text = "البحث عن موظف\nSearch Employee",
                Size = new Size(150, 60),
                Location = new Point(340, 60),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            searchEmployeeBtn.Click += (s, e) => ShowEmployeeSearchDialog();

            Button diagnosticBtn = new Button
            {
                Text = "تشخيص قاعدة البيانات\nDatabase Diagnostic",
                Size = new Size(150, 60),
                Location = new Point(500, 60),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            diagnosticBtn.Click += (s, e) => ShowDatabaseDiagnosisDialog();

            // Sample employee data
            ListView employeeList = new ListView
            {
                Location = new Point(20, 140),
                Size = new Size(container.Width - 40, 300),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true
            };

            employeeList.Columns.Add("الرقم", 60);
            employeeList.Columns.Add("الاسم", 150);
            employeeList.Columns.Add("المنصب", 120);
            employeeList.Columns.Add("القسم", 100);
            employeeList.Columns.Add("تاريخ التوظيف", 120);

            // Add sample data
            employeeList.Items.Add(new ListViewItem(new[] { "001", "أحمد محمد", "مطور برمجيات", "تقنية المعلومات", "2023-01-15" }));
            employeeList.Items.Add(new ListViewItem(new[] { "002", "فاطمة علي", "محاسبة", "المالية", "2023-02-20" }));
            employeeList.Items.Add(new ListViewItem(new[] { "003", "محمد سالم", "مدير مشروع", "إدارة المشاريع", "2023-03-10" }));

            container.Controls.AddRange(new Control[] { titleLabel, addEmployeeBtn, viewEmployeesBtn, searchEmployeeBtn, diagnosticBtn, employeeList });
        }

        private void CreateFormsManagementContent(Panel container)
        {
            Label titleLabel = new Label
            {
                Text = "إدارة النماذج - Forms Management",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(20, 20),
                Size = new Size(400, 30)
            };

            Button createFormBtn = new Button
            {
                Text = "إنشاء نموذج جديد\nCreate New Form",
                Size = new Size(150, 60),
                Location = new Point(20, 60),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            createFormBtn.Click += (s, e) => ShowCreateFormDialog();

            Button viewFormsBtn = new Button
            {
                Text = "عرض النماذج\nView Forms",
                Size = new Size(150, 60),
                Location = new Point(180, 60),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            viewFormsBtn.Click += (s, e) => MessageBox.Show("عرض جميع النماذج\nإجمالي النماذج: 3\nمعلق: 1 | مقبول: 1 | قيد المراجعة: 1", "عرض النماذج", MessageBoxButtons.OK, MessageBoxIcon.Information);

            Button approveFormsBtn = new Button
            {
                Text = "الموافقة على النماذج\nApprove Forms",
                Size = new Size(150, 60),
                Location = new Point(340, 60),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            approveFormsBtn.Click += (s, e) => ShowApproveFormsDialog();

            // Forms list
            ListView formsList = new ListView
            {
                Location = new Point(20, 140),
                Size = new Size(container.Width - 40, 300),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true
            };

            formsList.Columns.Add("رقم النموذج", 80);
            formsList.Columns.Add("نوع النموذج", 150);
            formsList.Columns.Add("مقدم الطلب", 120);
            formsList.Columns.Add("التاريخ", 100);
            formsList.Columns.Add("الحالة", 100);

            // Add sample data
            formsList.Items.Add(new ListViewItem(new[] { "F001", "طلب إجازة", "أحمد محمد", "2024-01-15", "معلق" }));
            formsList.Items.Add(new ListViewItem(new[] { "F002", "طلب ترقية", "فاطمة علي", "2024-01-20", "مقبول" }));
            formsList.Items.Add(new ListViewItem(new[] { "F003", "طلب تدريب", "محمد سالم", "2024-01-25", "قيد المراجعة" }));

            container.Controls.AddRange(new Control[] { titleLabel, createFormBtn, viewFormsBtn, approveFormsBtn, formsList });
        }

        private void CreateWorkInjuriesContent(Panel container)
        {
            Label titleLabel = new Label
            {
                Text = "إصابات العمل - Work Injuries",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(231, 76, 60),
                Location = new Point(20, 20),
                Size = new Size(400, 30)
            };

            Button reportInjuryBtn = new Button
            {
                Text = "الإبلاغ عن إصابة\nReport Injury",
                Size = new Size(150, 60),
                Location = new Point(20, 60),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            reportInjuryBtn.Click += (s, e) => ShowReportInjuryDialog();

            Button viewInjuriesBtn = new Button
            {
                Text = "عرض الإصابات\nView Injuries",
                Size = new Size(150, 60),
                Location = new Point(180, 60),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            viewInjuriesBtn.Click += (s, e) => MessageBox.Show("عرض جميع الإصابات\nإجمالي الإصابات المسجلة: 0\nحالات نشطة: 0\nحالات مغلقة: 0", "عرض الإصابات", MessageBoxButtons.OK, MessageBoxIcon.Information);

            Button medicalReportsBtn = new Button
            {
                Text = "التقارير الطبية\nMedical Reports",
                Size = new Size(150, 60),
                Location = new Point(340, 60),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            medicalReportsBtn.Click += (s, e) => MessageBox.Show("التقارير الطبية\nتقارير متاحة: 0\nمتابعات طبية: 0\nحالات شفاء: 0", "التقارير الطبية", MessageBoxButtons.OK, MessageBoxIcon.Information);

            Button addInjuryEmployeeBtn = new Button
            {
                Text = "إضافة موظف إصابة\nAdd Injury Employee",
                Size = new Size(150, 60),
                Location = new Point(20, 140),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            addInjuryEmployeeBtn.Click += (s, e) => ShowAddEmployeeDialog("injury");

            container.Controls.AddRange(new Control[] { titleLabel, reportInjuryBtn, viewInjuriesBtn, medicalReportsBtn, addInjuryEmployeeBtn });
        }

        private void CreateDefaultContent(Panel container, string sectionType)
        {
            Label statusLabel = new Label
            {
                Text = $"قسم {sectionType} جاهز للاستخدام\nSection {sectionType} is ready to use",
                Font = new Font("Tahoma", 14),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(20, 20),
                Size = new Size(400, 60),
                TextAlign = ContentAlignment.MiddleCenter
            };

            Label instructionLabel = new Label
            {
                Text = "يمكنك البدء في استخدام هذا القسم الآن\nYou can start using this section now",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 100),
                Size = new Size(400, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            container.Controls.AddRange(new Control[] { statusLabel, instructionLabel });
        }

        private void CreateUnpaidLeaveContent(Panel container)
        {
            Label titleLabel = new Label
            {
                Text = "الإجازات بدون مرتب - Unpaid Leave",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(20, 20),
                Size = new Size(400, 30)
            };

            Button domesticLeaveBtn = new Button
            {
                Text = "إجازة داخل البلاد\nDomestic Leave",
                Size = new Size(150, 60),
                Location = new Point(20, 60),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            domesticLeaveBtn.Click += (s, e) => ShowDomesticLeaveDialog();

            Button internationalLeaveBtn = new Button
            {
                Text = "إجازة خارج البلاد\nInternational Leave",
                Size = new Size(150, 60),
                Location = new Point(180, 60),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            internationalLeaveBtn.Click += (s, e) => ShowInternationalLeaveDialog();

            Button childCareLeaveBtn = new Button
            {
                Text = "إجازة رعاية الطفل\nChild Care Leave",
                Size = new Size(150, 60),
                Location = new Point(340, 60),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            childCareLeaveBtn.Click += (s, e) => ShowChildCareLeaveDialog();

            Button viewLeavesBtn = new Button
            {
                Text = "عرض الإجازات\nView Leaves",
                Size = new Size(150, 60),
                Location = new Point(500, 60),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            viewLeavesBtn.Click += (s, e) => MessageBox.Show("عرض جميع الإجازات\nإجمالي الطلبات: 0\nمعتمدة: 0\nمعلقة: 0\nمرفوضة: 0", "عرض الإجازات", MessageBoxButtons.OK, MessageBoxIcon.Information);

            Button addEmployeeLeaveBtn = new Button
            {
                Text = "إضافة موظف إجازة\nAdd Leave Employee",
                Size = new Size(150, 60),
                Location = new Point(20, 140),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            addEmployeeLeaveBtn.Click += (s, e) => ShowAddEmployeeDialog("unpaid_leave");

            container.Controls.AddRange(new Control[] { titleLabel, domesticLeaveBtn, internationalLeaveBtn, childCareLeaveBtn, viewLeavesBtn, addEmployeeLeaveBtn });
        }

        private void CreateDelegatesContent(Panel container)
        {
            Label titleLabel = new Label
            {
                Text = "المنتدبين - City Council Delegates",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(20, 20),
                Size = new Size(400, 30)
            };

            Button toCouncilBtn = new Button
            {
                Text = "منتدب إلى المجلس\nDelegate to Council",
                Size = new Size(150, 60),
                Location = new Point(20, 60),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            toCouncilBtn.Click += (s, e) => ShowDelegateToCouncilDialog();

            Button fromCouncilBtn = new Button
            {
                Text = "منتدب من المجلس\nDelegate from Council",
                Size = new Size(150, 60),
                Location = new Point(180, 60),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            fromCouncilBtn.Click += (s, e) => ShowDelegateFromCouncilDialog();

            Button viewDelegatesBtn = new Button
            {
                Text = "عرض المنتدبين\nView Delegates",
                Size = new Size(150, 60),
                Location = new Point(340, 60),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            viewDelegatesBtn.Click += (s, e) => MessageBox.Show("عرض جميع المنتدبين\nإلى المجلس: 0\nمن المجلس: 0\nانتدابات نشطة: 0", "عرض المنتدبين", MessageBoxButtons.OK, MessageBoxIcon.Information);

            Button delegatesReportBtn = new Button
            {
                Text = "تقرير المنتدبين\nDelegates Report",
                Size = new Size(150, 60),
                Location = new Point(500, 60),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            delegatesReportBtn.Click += (s, e) => ShowDelegatesReportDialog();

            Button addDelegateEmployeeBtn = new Button
            {
                Text = "إضافة موظف منتدب\nAdd Delegate Employee",
                Size = new Size(150, 60),
                Location = new Point(20, 140),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            addDelegateEmployeeBtn.Click += (s, e) => ShowAddEmployeeDialog("delegate");

            container.Controls.AddRange(new Control[] { titleLabel, toCouncilBtn, fromCouncilBtn, viewDelegatesBtn, delegatesReportBtn, addDelegateEmployeeBtn });
        }

        private void CreateAchievementsContent(Panel container)
        {
            Label titleLabel = new Label
            {
                Text = "الإنجازات الشهرية - Monthly Achievements",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(20, 20),
                Size = new Size(400, 30)
            };

            Button newAchievementBtn = new Button
            {
                Text = "إنجاز جديد\nNew Achievement",
                Size = new Size(150, 60),
                Location = new Point(20, 60),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            newAchievementBtn.Click += (s, e) => ShowNewAchievementDialog();

            Button completedBtn = new Button
            {
                Text = "الإنجازات المنتهية\nCompleted Achievements",
                Size = new Size(150, 60),
                Location = new Point(180, 60),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            completedBtn.Click += (s, e) => MessageBox.Show("الإنجازات المنتهية\nإنجازات مكتملة: 0\nمهام منجزة: 0\nأهداف محققة: 0", "الإنجازات المنتهية", MessageBoxButtons.OK, MessageBoxIcon.Information);

            Button inProgressBtn = new Button
            {
                Text = "قيد التنفيذ\nIn Progress",
                Size = new Size(150, 60),
                Location = new Point(340, 60),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            inProgressBtn.Click += (s, e) => MessageBox.Show("الإنجازات قيد التنفيذ\nمهام جارية: 0\nمشاريع نشطة: 0\nأهداف قيد التحقيق: 0", "قيد التنفيذ", MessageBoxButtons.OK, MessageBoxIcon.Information);

            Button monthlyReportBtn = new Button
            {
                Text = "التقرير الشهري\nMonthly Report",
                Size = new Size(150, 60),
                Location = new Point(500, 60),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };
            monthlyReportBtn.Click += (s, e) => ShowMonthlyReportDialog();

            container.Controls.AddRange(new Control[] { titleLabel, newAchievementBtn, completedBtn, inProgressBtn, monthlyReportBtn });
        }

        private void CreateReportsContent(Panel container)
        {
            CreateDefaultContent(container, "Reports");
        }

        private void LoadDeductionsView()
        {
            ClearContentPanel();

            Label titleLabel = new Label
            {
                Text = "حاسبة الاستقطاعات - Deductions Calculator",
                Font = new Font("Tahoma", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(30, 20),
                Size = new Size(800, 35),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // Main calculation panel
            Panel calculationPanel = new Panel
            {
                Location = new Point(30, 70),
                Size = new Size(800, 600),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true
            };

            // Input amount
            Label amountLabel = new Label
            {
                Text = "المبلغ الأساسي - Base Amount:",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(250, 30),
                TextAlign = ContentAlignment.MiddleLeft
            };

            TextBox amountTextBox = new TextBox
            {
                Font = new Font("Arial", 14),
                Location = new Point(280, 20),
                Size = new Size(200, 30),
                Text = "",
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };

            Label placeholderLabel = new Label
            {
                Text = "مثال: 10000",
                Font = new Font("Arial", 10),
                ForeColor = Color.Gray,
                Location = new Point(280, 55),
                Size = new Size(100, 20)
            };



            Button calculateBtn = new Button
            {
                Text = "احسب الاستقطاعات\nCalculate",
                Size = new Size(140, 50),
                Location = new Point(500, 15),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold)
            };

            // Deductions breakdown
            Label breakdownLabel = new Label
            {
                Text = "تفصيل الاستقطاعات - Deductions Breakdown:",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(20, 80),
                Size = new Size(400, 30)
            };

            // Create deduction fields
            int yPos = 120;
            string[] deductionLabels = { "12%", "9%", "3%", "1%", "1%", "1%", "1%", "0.25%" };
            string[] deductionNames = { "التأمين الصحي", "التأمين الاجتماعي", "ضريبة الدخل", "تأمين البطالة", "تأمين الحوادث", "التدريب المهني", "رسوم إدارية", "رسوم خدمات" };
            TextBox[] deductionTextBoxes = new TextBox[8];

            for (int i = 0; i < deductionLabels.Length; i++)
            {
                Label label = new Label
                {
                    Text = $"{deductionLabels[i]} - {deductionNames[i]}:",
                    Font = new Font("Tahoma", 12),
                    ForeColor = Color.FromArgb(52, 73, 94),
                    Location = new Point(20, yPos),
                    Size = new Size(250, 25)
                };

                deductionTextBoxes[i] = new TextBox
                {
                    Font = new Font("Arial", 12),
                    Location = new Point(280, yPos),
                    Size = new Size(150, 25),
                    ReadOnly = true,
                    BackColor = Color.FromArgb(236, 240, 241)
                };

                calculationPanel.Controls.AddRange(new Control[] { label, deductionTextBoxes[i] });
                yPos += 35;
            }

            // Special calculations
            Label specialLabel = new Label
            {
                Text = "حسابات خاصة - Special Calculations:",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(155, 89, 182),
                Location = new Point(20, yPos + 10),
                Size = new Size(300, 30)
            };

            yPos += 50;

            Label sumLabel = new Label
            {
                Text = "مجموع (1% + 0.25%):",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, yPos),
                Size = new Size(200, 25)
            };

            TextBox sumTextBox = new TextBox
            {
                Font = new Font("Arial", 12),
                Location = new Point(280, yPos),
                Size = new Size(150, 25),
                ReadOnly = true,
                BackColor = Color.FromArgb(255, 235, 59)
            };

            yPos += 35;

            Label totalLabel = new Label
            {
                Text = "إجمالي أجور الاشتراكات:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(231, 76, 60),
                Location = new Point(20, yPos),
                Size = new Size(250, 25)
            };

            TextBox totalTextBox = new TextBox
            {
                Font = new Font("Arial", 12, FontStyle.Bold),
                Location = new Point(280, yPos),
                Size = new Size(150, 25),
                ReadOnly = true,
                BackColor = Color.FromArgb(255, 193, 7)
            };

            yPos += 35;

            Label netLabel = new Label
            {
                Text = "إجمالي 28.25%:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(20, yPos),
                Size = new Size(150, 25)
            };

            TextBox netTextBox = new TextBox
            {
                Font = new Font("Arial", 12, FontStyle.Bold),
                Location = new Point(280, yPos),
                Size = new Size(150, 25),
                ReadOnly = true,
                BackColor = Color.FromArgb(76, 175, 80)
            };

            // Calculate button event
            calculateBtn.Click += (s, e) =>
            {
                try
                {
                    if (decimal.TryParse(amountTextBox.Text, out decimal baseAmount))
                    {
                        decimal[] percentages = { 0.12m, 0.09m, 0.03m, 0.01m, 0.01m, 0.01m, 0.01m, 0.0025m };

                        for (int i = 0; i < percentages.Length; i++)
                        {
                            decimal deduction = baseAmount * percentages[i];
                            deductionTextBoxes[i].Text = deduction.ToString("F2");
                        }

                        // Special sum (1% + 0.25%)
                        decimal specialSum = baseAmount * 0.01m + baseAmount * 0.0025m;
                        sumTextBox.Text = specialSum.ToString("F2");

                        // إجمالي أجور الاشتراكات (المبلغ ÷ 28.25%)
                        decimal totalDeductions = baseAmount / 0.2825m;
                        totalTextBox.Text = totalDeductions.ToString("F2");

                        // إجمالي 28.25% (مجموع جميع الاستقطاعات الفردية)
                        decimal netAmount = 0;
                        for (int i = 0; i < percentages.Length; i++)
                        {
                            netAmount += baseAmount * percentages[i];
                        }
                        netTextBox.Text = netAmount.ToString("F2");
                    }
                    else
                    {
                        MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في الحساب: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            calculationPanel.Controls.AddRange(new Control[] {
                amountLabel, amountTextBox, placeholderLabel, calculateBtn, breakdownLabel,
                specialLabel, sumLabel, sumTextBox, totalLabel, totalTextBox, netLabel, netTextBox
            });

            Label devLabel = new Label
            {
                Text = $"تم تطوير هذا النظام بواسطة: {DEVELOPER_NAME}",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(30, 690),
                Size = new Size(600, 25),
                TextAlign = ContentAlignment.MiddleLeft
            };

            contentPanel.Controls.AddRange(new Control[] { titleLabel, calculationPanel, devLabel });
        }

        private void LoadFileManagerView()
        {
            ClearContentPanel();

            Label titleLabel = new Label
            {
                Text = "📁 إدارة الملفات - File Manager",
                Font = new Font("Arial", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(30, 20),
                AutoSize = true
            };

            // Upload section
            Panel uploadPanel = new Panel
            {
                Size = new Size(contentPanel.Width - 80, 120),
                Location = new Point(30, 60),
                BackColor = Color.FromArgb(236, 240, 241),
                BorderStyle = BorderStyle.FixedSingle,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right
            };

            Label uploadTitle = new Label
            {
                Text = "📤 رفع الملفات - Upload Files",
                Font = new Font("Arial", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(20, 15),
                AutoSize = true
            };

            Button btnSelectFiles = new Button
            {
                Text = "اختيار الملفات - Select Files",
                Size = new Size(150, 35),
                Location = new Point(20, 50),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Arial", 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnSelectFiles.Click += BtnSelectFiles_Click;

            Button btnUploadFiles = new Button
            {
                Text = "رفع الملفات - Upload Files",
                Size = new Size(150, 35),
                Location = new Point(180, 50),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Arial", 10, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnUploadFiles.Click += BtnUploadFiles_Click;

            Label uploadInfo = new Label
            {
                Text = "الملفات المدعومة: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, TXT",
                Font = new Font("Arial", 9),
                ForeColor = Color.FromArgb(108, 117, 125),
                Location = new Point(20, 90),
                AutoSize = true
            };

            uploadPanel.Controls.AddRange(new Control[] { uploadTitle, btnSelectFiles, btnUploadFiles, uploadInfo });

            // Files list section
            Panel filesPanel = new Panel
            {
                Size = new Size(contentPanel.Width - 80, 350),
                Location = new Point(30, 200),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };

            Label filesTitle = new Label
            {
                Text = "📋 الملفات المرفوعة - Uploaded Files",
                Font = new Font("Arial", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(20, 15),
                AutoSize = true
            };

            ListBox filesList = new ListBox
            {
                Size = new Size(filesPanel.Width - 40, 250),
                Location = new Point(20, 50),
                Font = new Font("Arial", 10),
                BackColor = Color.FromArgb(248, 249, 250),
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };
            filesList.Name = "filesList";
            LoadUploadedFiles(filesList);

            Button btnOpenFile = new Button
            {
                Text = "فتح الملف\nOpen File",
                Size = new Size(100, 40),
                Location = new Point(20, 310),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Arial", 8, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left
            };
            btnOpenFile.Click += (s, e) => OpenSelectedFile(filesList);

            Button btnDeleteFile = new Button
            {
                Text = "حذف الملف\nDelete File",
                Size = new Size(100, 40),
                Location = new Point(130, 310),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Arial", 8, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left
            };
            btnDeleteFile.Click += (s, e) => DeleteSelectedFile(filesList);

            Button btnRefreshFiles = new Button
            {
                Text = "تحديث\nRefresh",
                Size = new Size(100, 40),
                Location = new Point(240, 310),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Arial", 8, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left
            };
            btnRefreshFiles.Click += (s, e) => LoadUploadedFiles(filesList);

            Button btnSearchFiles = new Button
            {
                Text = "🔍 بحث\nSearch",
                Size = new Size(100, 40),
                Location = new Point(350, 310),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Arial", 8, FontStyle.Bold),
                Cursor = Cursors.Hand,
                Anchor = AnchorStyles.Bottom | AnchorStyles.Left
            };
            btnSearchFiles.Click += (s, e) => ShowFileSearchDialog(filesList);

            filesPanel.Controls.AddRange(new Control[] {
                filesTitle, filesList, btnOpenFile, btnDeleteFile, btnRefreshFiles, btnSearchFiles
            });

            contentPanel.Controls.AddRange(new Control[] { titleLabel, uploadPanel, filesPanel });
        }

        private void BtnSelectFiles_Click(object? sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Title = "اختيار الملفات للرفع - Select Files to Upload",
                Filter = "جميع الملفات المدعومة|*.pdf;*.doc;*.docx;*.xls;*.xlsx;*.jpg;*.jpeg;*.png;*.txt|" +
                        "ملفات PDF|*.pdf|" +
                        "ملفات Word|*.doc;*.docx|" +
                        "ملفات Excel|*.xls;*.xlsx|" +
                        "ملفات الصور|*.jpg;*.jpeg;*.png|" +
                        "ملفات نصية|*.txt",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                string fileNames = string.Join(", ", openFileDialog.FileNames.Select(Path.GetFileName));
                MessageBox.Show($"تم اختيار الملفات:\n{fileNames}", "ملفات محددة",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Store selected files for upload
                selectedFiles = openFileDialog.FileNames;
            }
        }

        private string[] selectedFiles = new string[0];

        private void BtnUploadFiles_Click(object? sender, EventArgs e)
        {
            if (selectedFiles.Length == 0)
            {
                MessageBox.Show("يرجى اختيار الملفات أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                string uploadFolder = Path.Combine(Application.StartupPath, "UploadedFiles");
                if (!Directory.Exists(uploadFolder))
                {
                    Directory.CreateDirectory(uploadFolder);
                }

                int successCount = 0;
                foreach (string sourceFile in selectedFiles)
                {
                    string fileName = Path.GetFileName(sourceFile);
                    string destinationFile = Path.Combine(uploadFolder, fileName);

                    // Add timestamp if file exists
                    if (File.Exists(destinationFile))
                    {
                        string nameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
                        string extension = Path.GetExtension(fileName);
                        fileName = $"{nameWithoutExt}_{DateTime.Now:yyyyMMdd_HHmmss}{extension}";
                        destinationFile = Path.Combine(uploadFolder, fileName);
                    }

                    File.Copy(sourceFile, destinationFile);
                    successCount++;
                }

                MessageBox.Show($"تم رفع {successCount} ملف بنجاح!", "نجح الرفع",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                selectedFiles = new string[0]; // Clear selection

                // Refresh files list if it exists
                var filesList = contentPanel.Controls.OfType<Panel>()
                    .SelectMany(p => p.Controls.OfType<ListBox>())
                    .FirstOrDefault(lb => lb.Name == "filesList");
                if (filesList != null)
                {
                    LoadUploadedFiles(filesList);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في رفع الملفات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadUploadedFiles(ListBox filesList)
        {
            try
            {
                filesList.Items.Clear();
                string uploadFolder = Path.Combine(Application.StartupPath, "UploadedFiles");

                if (Directory.Exists(uploadFolder))
                {
                    var files = Directory.GetFiles(uploadFolder);
                    foreach (string file in files)
                    {
                        var fileInfo = new FileInfo(file);
                        string displayText = $"{Path.GetFileName(file)} ({FormatFileSize(fileInfo.Length)}) - {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm}";
                        filesList.Items.Add(displayText);
                    }

                    if (files.Length == 0)
                    {
                        filesList.Items.Add("لا توجد ملفات مرفوعة - No uploaded files");
                    }
                }
                else
                {
                    filesList.Items.Add("مجلد الملفات غير موجود - Upload folder not found");
                }
            }
            catch (Exception ex)
            {
                filesList.Items.Clear();
                filesList.Items.Add($"خطأ في تحميل الملفات: {ex.Message}");
            }
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        private void OpenSelectedFile(ListBox filesList)
        {
            if (filesList.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار ملف للفتح", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                string selectedText = filesList.SelectedItem.ToString() ?? "";
                string fileName = selectedText.Split('(')[0].Trim();
                string uploadFolder = Path.Combine(Application.StartupPath, "UploadedFiles");
                string filePath = Path.Combine(uploadFolder, fileName);

                if (File.Exists(filePath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = filePath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("الملف غير موجود", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DeleteSelectedFile(ListBox filesList)
        {
            if (filesList.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار ملف للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف هذا الملف؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    string selectedText = filesList.SelectedItem.ToString() ?? "";
                    string fileName = selectedText.Split('(')[0].Trim();
                    string uploadFolder = Path.Combine(Application.StartupPath, "UploadedFiles");
                    string filePath = Path.Combine(uploadFolder, fileName);

                    if (File.Exists(filePath))
                    {
                        File.Delete(filePath);
                        MessageBox.Show("تم حذف الملف بنجاح", "نجح الحذف",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        LoadUploadedFiles(filesList);
                    }
                    else
                    {
                        MessageBox.Show("الملف غير موجود", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الملف: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void ShowFileSearchDialog(ListBox filesList)
        {
            Form searchForm = new Form
            {
                Text = "البحث في الملفات - File Search",
                Size = new Size(800, 600),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            Label titleLabel = new Label
            {
                Text = "البحث المتقدم في الملفات والمحتوى",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(20, 20),
                Size = new Size(750, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // File name search
            Label fileNameLabel = new Label
            {
                Text = "البحث في اسم الملف:",
                Font = new Font("Tahoma", 12),
                Location = new Point(20, 70),
                Size = new Size(180, 25)
            };

            TextBox fileNameTextBox = new TextBox
            {
                Font = new Font("Arial", 12),
                Location = new Point(210, 70),
                Size = new Size(500, 25),
                PlaceholderText = "أدخل جزء من اسم الملف..."
            };

            // Content search
            Label contentLabel = new Label
            {
                Text = "البحث في محتوى الملف:",
                Font = new Font("Tahoma", 12),
                Location = new Point(20, 110),
                Size = new Size(180, 25)
            };

            TextBox contentTextBox = new TextBox
            {
                Font = new Font("Arial", 12),
                Location = new Point(210, 110),
                Size = new Size(500, 25),
                PlaceholderText = "كلمات للبحث داخل الملفات..."
            };

            // File type filter
            Label typeLabel = new Label
            {
                Text = "نوع الملف:",
                Font = new Font("Tahoma", 12),
                Location = new Point(20, 150),
                Size = new Size(100, 25)
            };

            ComboBox typeCombo = new ComboBox
            {
                Font = new Font("Arial", 12),
                Location = new Point(180, 150),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            typeCombo.Items.AddRange(new string[] { "جميع الأنواع", "PDF", "Word (.doc/.docx)", "Excel (.xls/.xlsx)", "صور (.jpg/.png)", "نصوص (.txt)" });
            typeCombo.SelectedIndex = 0;

            // Date range
            Label dateLabel = new Label
            {
                Text = "تاريخ الرفع:",
                Font = new Font("Tahoma", 12),
                Location = new Point(20, 190),
                Size = new Size(100, 25)
            };

            DateTimePicker fromDate = new DateTimePicker
            {
                Font = new Font("Arial", 10),
                Location = new Point(180, 190),
                Size = new Size(120, 25),
                Value = DateTime.Now.AddMonths(-1)
            };

            Label toLabel = new Label
            {
                Text = "إلى:",
                Font = new Font("Tahoma", 12),
                Location = new Point(310, 190),
                Size = new Size(30, 25)
            };

            DateTimePicker toDate = new DateTimePicker
            {
                Font = new Font("Arial", 10),
                Location = new Point(350, 190),
                Size = new Size(120, 25),
                Value = DateTime.Now
            };

            // Size filter
            Label sizeLabel = new Label
            {
                Text = "حجم الملف:",
                Font = new Font("Tahoma", 12),
                Location = new Point(20, 230),
                Size = new Size(100, 25)
            };

            ComboBox sizeCombo = new ComboBox
            {
                Font = new Font("Arial", 12),
                Location = new Point(180, 230),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            sizeCombo.Items.AddRange(new string[] { "أي حجم", "أقل من 1 MB", "1-10 MB", "10-100 MB", "أكثر من 100 MB" });
            sizeCombo.SelectedIndex = 0;

            // Results area
            Label resultsLabel = new Label
            {
                Text = "نتائج البحث:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(20, 270),
                Size = new Size(100, 25)
            };

            ListBox resultsListBox = new ListBox
            {
                Font = new Font("Arial", 10),
                Location = new Point(20, 300),
                Size = new Size(550, 100),
                BackColor = Color.FromArgb(248, 249, 250)
            };

            // Buttons
            Button searchButton = new Button
            {
                Text = "🔍 بحث",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(100, 40),
                Location = new Point(200, 420),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button clearButton = new Button
            {
                Text = "مسح",
                Font = new Font("Tahoma", 12),
                Size = new Size(100, 40),
                Location = new Point(320, 420),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button closeButton = new Button
            {
                Text = "إغلاق",
                Font = new Font("Tahoma", 12),
                Size = new Size(100, 40),
                Location = new Point(440, 420),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // Search functionality
            searchButton.Click += (s, e) =>
            {
                try
                {
                    resultsListBox.Items.Clear();
                    string uploadFolder = Path.Combine(Application.StartupPath, "UploadedFiles");

                    if (!Directory.Exists(uploadFolder))
                    {
                        resultsListBox.Items.Add("مجلد الملفات غير موجود");
                        return;
                    }

                    var files = Directory.GetFiles(uploadFolder);
                    var matchedFiles = new List<string>();

                    foreach (string file in files)
                    {
                        var fileInfo = new FileInfo(file);
                        bool matches = true;

                        // Check file name
                        if (!string.IsNullOrEmpty(fileNameTextBox.Text))
                        {
                            if (!fileInfo.Name.ToLower().Contains(fileNameTextBox.Text.ToLower()))
                                matches = false;
                        }

                        // Check file type
                        if (typeCombo.SelectedIndex > 0)
                        {
                            string selectedType = typeCombo.Text;
                            string extension = fileInfo.Extension.ToLower();

                            bool typeMatches = selectedType switch
                            {
                                "PDF" => extension == ".pdf",
                                "Word (.doc/.docx)" => extension == ".doc" || extension == ".docx",
                                "Excel (.xls/.xlsx)" => extension == ".xls" || extension == ".xlsx",
                                "صور (.jpg/.png)" => extension == ".jpg" || extension == ".jpeg" || extension == ".png",
                                "نصوص (.txt)" => extension == ".txt",
                                _ => true
                            };

                            if (!typeMatches) matches = false;
                        }

                        // Check date range
                        if (fileInfo.LastWriteTime < fromDate.Value.Date || fileInfo.LastWriteTime > toDate.Value.Date.AddDays(1))
                            matches = false;

                        // Check size
                        if (sizeCombo.SelectedIndex > 0)
                        {
                            long fileSizeBytes = fileInfo.Length;
                            double fileSizeMB = fileSizeBytes / (1024.0 * 1024.0);

                            bool sizeMatches = sizeCombo.Text switch
                            {
                                "أقل من 1 MB" => fileSizeMB < 1,
                                "1-10 MB" => fileSizeMB >= 1 && fileSizeMB <= 10,
                                "10-100 MB" => fileSizeMB > 10 && fileSizeMB <= 100,
                                "أكثر من 100 MB" => fileSizeMB > 100,
                                _ => true
                            };

                            if (!sizeMatches) matches = false;
                        }

                        // Check content (basic text search for supported files)
                        if (!string.IsNullOrEmpty(contentTextBox.Text) && matches)
                        {
                            try
                            {
                                if (fileInfo.Extension.ToLower() == ".txt")
                                {
                                    string content = File.ReadAllText(file);
                                    if (!content.ToLower().Contains(contentTextBox.Text.ToLower()))
                                        matches = false;
                                }
                                // For other file types, we'll just search in filename for now
                            }
                            catch
                            {
                                // If we can't read the file, skip content search
                            }
                        }

                        if (matches)
                        {
                            string displayText = $"{fileInfo.Name} ({FormatFileSize(fileInfo.Length)}) - {fileInfo.LastWriteTime:yyyy-MM-dd}";
                            matchedFiles.Add(displayText);
                        }
                    }

                    if (matchedFiles.Count > 0)
                    {
                        foreach (string file in matchedFiles)
                        {
                            resultsListBox.Items.Add(file);
                        }
                        resultsListBox.Items.Add($"--- تم العثور على {matchedFiles.Count} ملف ---");
                    }
                    else
                    {
                        resultsListBox.Items.Add("لم يتم العثور على ملفات تطابق معايير البحث");
                    }
                }
                catch (Exception ex)
                {
                    resultsListBox.Items.Clear();
                    resultsListBox.Items.Add($"خطأ في البحث: {ex.Message}");
                }
            };

            clearButton.Click += (s, e) =>
            {
                fileNameTextBox.Clear();
                contentTextBox.Clear();
                typeCombo.SelectedIndex = 0;
                sizeCombo.SelectedIndex = 0;
                resultsListBox.Items.Clear();
                fromDate.Value = DateTime.Now.AddMonths(-1);
                toDate.Value = DateTime.Now;
            };

            closeButton.Click += (s, e) => searchForm.Close();

            searchForm.Controls.AddRange(new Control[] {
                titleLabel, fileNameLabel, fileNameTextBox, contentLabel, contentTextBox,
                typeLabel, typeCombo, dateLabel, fromDate, toLabel, toDate,
                sizeLabel, sizeCombo, resultsLabel, resultsListBox,
                searchButton, clearButton, closeButton
            });

            searchForm.ShowDialog();
        }

        private void LoadNotificationsView()
        {
            ClearContentPanel();

            Label titleLabel = new Label
            {
                Text = "🔔 التنبيهات - Notifications",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(30, 20),
                Size = new Size(400, 30),
                RightToLeft = RightToLeft.Yes
            };

            // Add notification button for authorized users
            Button btnAddNotification = new Button
            {
                Text = "إضافة تنبيه جديد\nAdd New Notification",
                Size = new Size(150, 50),
                Location = new Point(30, 60),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Cursor = Cursors.Hand,
                RightToLeft = RightToLeft.Yes
            };
            btnAddNotification.Click += BtnAddNotification_Click;

            // Notifications panel
            Panel notificationsPanel = new Panel
            {
                Size = new Size(contentPanel.Width - 80, 400),
                Location = new Point(30, 120),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };

            LoadNotificationsList(notificationsPanel);

            contentPanel.Controls.AddRange(new Control[] { titleLabel, btnAddNotification, notificationsPanel });
        }

        private void LoadNotificationsList(Panel container)
        {
            container.Controls.Clear();
            var notifications = notificationManager.GetActiveNotifications();

            int yPosition = 10;
            foreach (var notification in notifications)
            {
                Panel notificationCard = CreateNotificationCard(notification, yPosition);
                container.Controls.Add(notificationCard);
                yPosition += notificationCard.Height + 10;
            }

            if (notifications.Count == 0)
            {
                Label noDataLabel = new Label
                {
                    Text = "لا توجد تنبيهات حالياً - No notifications available",
                    Font = new Font("Arial", 12),
                    ForeColor = Color.Gray,
                    Location = new Point(20, 20),
                    AutoSize = true
                };
                container.Controls.Add(noDataLabel);
            }
        }

        private Panel CreateNotificationCard(Notification notification, int yPosition)
        {
            Panel card = new Panel
            {
                Size = new Size(700, 80),
                Location = new Point(10, yPosition),
                BackColor = GetNotificationColor(notification.NotificationType),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label titleLabel = new Label
            {
                Text = notification.Title,
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 10),
                Size = new Size(600, 25),
                RightToLeft = RightToLeft.Yes,
                TextAlign = ContentAlignment.MiddleRight
            };

            Label messageLabel = new Label
            {
                Text = notification.Message,
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 40),
                Size = new Size(600, 25),
                RightToLeft = RightToLeft.Yes,
                TextAlign = ContentAlignment.MiddleRight
            };

            Label infoLabel = new Label
            {
                Text = $"الأولوية: {notification.Priority} | بواسطة: {notification.CreatedBy} | {FormatDateToArabic(notification.CreatedDate)}",
                Font = new Font("Tahoma", 12),
                ForeColor = Color.LightGray,
                Location = new Point(10, 70),
                Size = new Size(600, 15),
                RightToLeft = RightToLeft.Yes,
                TextAlign = ContentAlignment.MiddleRight
            };

            card.Controls.AddRange(new Control[] { titleLabel, messageLabel, infoLabel });
            return card;
        }

        private Color GetNotificationColor(string type)
        {
            return type switch
            {
                "Success" => Color.FromArgb(46, 204, 113),
                "Warning" => Color.FromArgb(230, 126, 34),
                "Error" => Color.FromArgb(231, 76, 60),
                _ => Color.FromArgb(52, 152, 219)
            };
        }

        private void BtnAddNotification_Click(object? sender, EventArgs e)
        {
            // Simple form for adding notifications
            Form addForm = new Form
            {
                Text = "إضافة تنبيه جديد",
                Size = new Size(500, 400),
                StartPosition = FormStartPosition.CenterParent
            };

            Label titleLabel = new Label { Text = "العنوان:", Location = new Point(20, 20), AutoSize = true };
            TextBox titleTextBox = new TextBox { Location = new Point(20, 45), Size = new Size(400, 25) };

            Label messageLabel = new Label { Text = "الرسالة:", Location = new Point(20, 80), AutoSize = true };
            TextBox messageTextBox = new TextBox { Location = new Point(20, 105), Size = new Size(400, 80), Multiline = true };

            Label typeLabel = new Label { Text = "النوع:", Location = new Point(20, 200), AutoSize = true };
            ComboBox typeComboBox = new ComboBox
            {
                Location = new Point(20, 225),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            typeComboBox.Items.AddRange(new[] { "Info", "Success", "Warning", "Error" });
            typeComboBox.SelectedIndex = 0;

            Label priorityLabel = new Label { Text = "الأولوية:", Location = new Point(200, 200), AutoSize = true };
            ComboBox priorityComboBox = new ComboBox
            {
                Location = new Point(200, 225),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            priorityComboBox.Items.AddRange(new[] { "Low", "Medium", "High", "Critical" });
            priorityComboBox.SelectedIndex = 1;

            Button saveButton = new Button
            {
                Text = "حفظ",
                Location = new Point(20, 270),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(130, 270),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            saveButton.Click += (s, e) =>
            {
                if (string.IsNullOrWhiteSpace(titleTextBox.Text) || string.IsNullOrWhiteSpace(messageTextBox.Text))
                {
                    MessageBox.Show("يرجى ملء جميع الحقول المطلوبة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var notification = new Notification
                {
                    Title = titleTextBox.Text,
                    Message = messageTextBox.Text,
                    NotificationType = typeComboBox.SelectedItem?.ToString() ?? "Info",
                    Priority = priorityComboBox.SelectedItem?.ToString() ?? "Medium",
                    CreatedBy = DEVELOPER_NAME
                };

                if (notificationManager.AddNotification(notification))
                {
                    MessageBox.Show("تم إضافة التنبيه بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    addForm.Close();
                    LoadNotificationsView(); // Refresh the view
                }
                else
                {
                    MessageBox.Show("فشل في إضافة التنبيه", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            cancelButton.Click += (s, e) => addForm.Close();

            addForm.Controls.AddRange(new Control[] {
                titleLabel, titleTextBox, messageLabel, messageTextBox,
                typeLabel, typeComboBox, priorityLabel, priorityComboBox,
                saveButton, cancelButton
            });

            addForm.ShowDialog();
        }

        private void LoadNotesView()
        {
            ClearContentPanel();

            Label titleLabel = new Label
            {
                Text = "📝 الملاحظات - Notes",
                Font = new Font("Arial", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(30, 20),
                AutoSize = true
            };

            // Control buttons
            Button btnAddNote = new Button
            {
                Text = "إضافة ملاحظة\nAdd Note",
                Size = new Size(120, 50),
                Location = new Point(30, 60),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Arial", 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnAddNote.Click += BtnAddNote_Click;

            ComboBox categoryFilter = new ComboBox
            {
                Location = new Point(160, 70),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            categoryFilter.Items.AddRange(new[] { "الكل", "General", "Employee", "Process", "Reminder" });
            categoryFilter.SelectedIndex = 0;
            categoryFilter.SelectedIndexChanged += (s, e) => LoadNotesListByCategory(categoryFilter.SelectedItem?.ToString() ?? "الكل");

            Label filterLabel = new Label
            {
                Text = "تصفية حسب الفئة:",
                Location = new Point(160, 50),
                AutoSize = true,
                Font = new Font("Arial", 9)
            };

            // Notes panel
            Panel notesPanel = new Panel
            {
                Size = new Size(contentPanel.Width - 80, 400),
                Location = new Point(30, 120),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };
            notesPanel.Name = "notesPanel";

            LoadNotesListByCategory("الكل");

            contentPanel.Controls.AddRange(new Control[] { titleLabel, btnAddNote, filterLabel, categoryFilter, notesPanel });
        }

        private void LoadNotesListByCategory(string category)
        {
            var notesPanel = contentPanel.Controls.OfType<Panel>().FirstOrDefault(p => p.Name == "notesPanel");
            if (notesPanel == null) return;

            notesPanel.Controls.Clear();
            var notes = category == "الكل" ? noteManager.GetAllNotes() : noteManager.GetNotesByCategory(category);

            int yPosition = 10;
            foreach (var note in notes)
            {
                Panel noteCard = CreateNoteCard(note, yPosition);
                notesPanel.Controls.Add(noteCard);
                yPosition += noteCard.Height + 10;
            }

            if (notes.Count == 0)
            {
                Label noDataLabel = new Label
                {
                    Text = "لا توجد ملاحظات في هذه الفئة - No notes in this category",
                    Font = new Font("Arial", 12),
                    ForeColor = Color.Gray,
                    Location = new Point(20, 20),
                    AutoSize = true
                };
                notesPanel.Controls.Add(noDataLabel);
            }
        }

        private Panel CreateNoteCard(Note note, int yPosition)
        {
            Panel card = new Panel
            {
                Size = new Size(700, 100),
                Location = new Point(10, yPosition),
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label titleLabel = new Label
            {
                Text = note.Title,
                Font = new Font("Arial", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(10, 10),
                Size = new Size(600, 20)
            };

            Label contentLabel = new Label
            {
                Text = note.Content.Length > 100 ? note.Content.Substring(0, 100) + "..." : note.Content,
                Font = new Font("Arial", 10),
                ForeColor = Color.FromArgb(108, 117, 125),
                Location = new Point(10, 35),
                Size = new Size(600, 30)
            };

            Label infoLabel = new Label
            {
                Text = $"الفئة: {note.Category} | بواسطة: {note.CreatedBy} | {note.CreatedDate:yyyy-MM-dd}",
                Font = new Font("Arial", 8),
                ForeColor = Color.FromArgb(155, 155, 155),
                Location = new Point(10, 70),
                Size = new Size(500, 15)
            };

            if (!string.IsNullOrEmpty(note.Tags))
            {
                Label tagsLabel = new Label
                {
                    Text = $"العلامات: {note.Tags}",
                    Font = new Font("Arial", 8),
                    ForeColor = Color.FromArgb(52, 152, 219),
                    Location = new Point(10, 85),
                    Size = new Size(600, 15)
                };
                card.Controls.Add(tagsLabel);
            }

            card.Controls.AddRange(new Control[] { titleLabel, contentLabel, infoLabel });
            return card;
        }

        private void BtnAddNote_Click(object? sender, EventArgs e)
        {
            Form addForm = new Form
            {
                Text = "إضافة ملاحظة جديدة",
                Size = new Size(800, 600),
                StartPosition = FormStartPosition.CenterParent
            };

            Label titleLabel = new Label { Text = "العنوان:", Location = new Point(20, 20), AutoSize = true };
            TextBox titleTextBox = new TextBox { Location = new Point(20, 45), Size = new Size(700, 25) };

            Label contentLabel = new Label { Text = "المحتوى:", Location = new Point(20, 80), AutoSize = true };
            TextBox contentTextBox = new TextBox { Location = new Point(20, 105), Size = new Size(700, 150), Multiline = true };

            Label categoryLabel = new Label { Text = "الفئة:", Location = new Point(20, 240), AutoSize = true };
            ComboBox categoryComboBox = new ComboBox
            {
                Location = new Point(20, 265),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            categoryComboBox.Items.AddRange(new[] { "General", "Employee", "Process", "Reminder" });
            categoryComboBox.SelectedIndex = 0;

            Label tagsLabel = new Label { Text = "العلامات (مفصولة بفواصل):", Location = new Point(200, 240), AutoSize = true };
            TextBox tagsTextBox = new TextBox { Location = new Point(200, 265), Size = new Size(300, 25) };

            CheckBox isPrivateCheckBox = new CheckBox
            {
                Text = "ملاحظة خاصة",
                Location = new Point(20, 300),
                AutoSize = true
            };

            Button saveButton = new Button
            {
                Text = "حفظ",
                Location = new Point(20, 340),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(130, 340),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            saveButton.Click += (s, e) =>
            {
                if (string.IsNullOrWhiteSpace(titleTextBox.Text) || string.IsNullOrWhiteSpace(contentTextBox.Text))
                {
                    MessageBox.Show("يرجى ملء العنوان والمحتوى", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var note = new Note
                {
                    Title = titleTextBox.Text,
                    Content = contentTextBox.Text,
                    Category = categoryComboBox.SelectedItem?.ToString() ?? "General",
                    Tags = tagsTextBox.Text,
                    IsPrivate = isPrivateCheckBox.Checked,
                    CreatedBy = DEVELOPER_NAME
                };

                if (noteManager.AddNote(note))
                {
                    MessageBox.Show("تم إضافة الملاحظة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    addForm.Close();
                    LoadNotesView();
                }
                else
                {
                    MessageBox.Show("فشل في إضافة الملاحظة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            cancelButton.Click += (s, e) => addForm.Close();

            addForm.Controls.AddRange(new Control[] {
                titleLabel, titleTextBox, contentLabel, contentTextBox,
                categoryLabel, categoryComboBox, tagsLabel, tagsTextBox,
                isPrivateCheckBox, saveButton, cancelButton
            });

            addForm.ShowDialog();
        }

        private void LoadTipsView()
        {
            ClearContentPanel();

            Label titleLabel = new Label
            {
                Text = "💡 التلميحات والإرشادات - Tips & Guidelines",
                Font = new Font("Arial", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Location = new Point(30, 20),
                AutoSize = true
            };

            // Control buttons
            Button btnAddTip = new Button
            {
                Text = "إضافة تلميح\nAdd Tip",
                Size = new Size(120, 50),
                Location = new Point(30, 60),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Arial", 9, FontStyle.Bold),
                Cursor = Cursors.Hand
            };
            btnAddTip.Click += BtnAddTip_Click;

            ComboBox categoryFilter = new ComboBox
            {
                Location = new Point(160, 70),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            categoryFilter.Items.AddRange(new[] { "الكل", "Employee Management", "Forms", "Leave", "Injury", "Delegates", "General" });
            categoryFilter.SelectedIndex = 0;
            categoryFilter.SelectedIndexChanged += (s, e) => LoadTipsListByCategory(categoryFilter.SelectedItem?.ToString() ?? "الكل");

            Label filterLabel = new Label
            {
                Text = "تصفية حسب الفئة:",
                Location = new Point(160, 50),
                AutoSize = true,
                Font = new Font("Arial", 9)
            };

            // Tips panel
            Panel tipsPanel = new Panel
            {
                Size = new Size(contentPanel.Width - 80, 400),
                Location = new Point(30, 120),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true,
                Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom
            };
            tipsPanel.Name = "tipsPanel";

            LoadTipsListByCategory("الكل");

            contentPanel.Controls.AddRange(new Control[] { titleLabel, btnAddTip, filterLabel, categoryFilter, tipsPanel });
        }

        private void LoadTipsListByCategory(string category)
        {
            var tipsPanel = contentPanel.Controls.OfType<Panel>().FirstOrDefault(p => p.Name == "tipsPanel");
            if (tipsPanel == null) return;

            tipsPanel.Controls.Clear();
            var tips = category == "الكل" ? tipManager.GetActiveTips() : tipManager.GetTipsByCategory(category);

            int yPosition = 10;
            foreach (var tip in tips)
            {
                Panel tipCard = CreateTipCard(tip, yPosition);
                tipsPanel.Controls.Add(tipCard);
                yPosition += tipCard.Height + 10;
            }

            if (tips.Count == 0)
            {
                Label noDataLabel = new Label
                {
                    Text = "لا توجد تلميحات في هذه الفئة - No tips in this category",
                    Font = new Font("Arial", 12),
                    ForeColor = Color.Gray,
                    Location = new Point(20, 20),
                    AutoSize = true
                };
                tipsPanel.Controls.Add(noDataLabel);
            }
        }

        private Panel CreateTipCard(Tip tip, int yPosition)
        {
            Panel card = new Panel
            {
                Size = new Size(700, 120),
                Location = new Point(10, yPosition),
                BackColor = GetTipColor(tip.TipType),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label typeLabel = new Label
            {
                Text = GetTipTypeIcon(tip.TipType) + " " + tip.TipType,
                Font = new Font("Arial", 10, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 5),
                Size = new Size(150, 20)
            };

            Label titleLabel = new Label
            {
                Text = tip.Title,
                Font = new Font("Arial", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 25),
                Size = new Size(600, 20)
            };

            Label contentLabel = new Label
            {
                Text = tip.Content.Length > 120 ? tip.Content.Substring(0, 120) + "..." : tip.Content,
                Font = new Font("Arial", 10),
                ForeColor = Color.White,
                Location = new Point(10, 50),
                Size = new Size(600, 40)
            };

            Label infoLabel = new Label
            {
                Text = $"الفئة: {tip.Category} | بواسطة: {tip.CreatedBy} | {tip.CreatedDate:yyyy-MM-dd}",
                Font = new Font("Arial", 8),
                ForeColor = Color.LightGray,
                Location = new Point(10, 95),
                Size = new Size(600, 15)
            };

            card.Controls.AddRange(new Control[] { typeLabel, titleLabel, contentLabel, infoLabel });
            return card;
        }

        private Color GetTipColor(string tipType)
        {
            return tipType switch
            {
                "Best Practice" => Color.FromArgb(46, 204, 113),
                "Guideline" => Color.FromArgb(52, 152, 219),
                "Warning" => Color.FromArgb(230, 126, 34),
                _ => Color.FromArgb(155, 89, 182)
            };
        }

        private string GetTipTypeIcon(string tipType)
        {
            return tipType switch
            {
                "Best Practice" => "⭐",
                "Guideline" => "📋",
                "Warning" => "⚠️",
                _ => "💡"
            };
        }

        private void BtnAddTip_Click(object? sender, EventArgs e)
        {
            Form addForm = new Form
            {
                Text = "إضافة تلميح جديد",
                Size = new Size(800, 600),
                StartPosition = FormStartPosition.CenterParent
            };

            Label titleLabel = new Label { Text = "العنوان:", Location = new Point(20, 20), AutoSize = true };
            TextBox titleTextBox = new TextBox { Location = new Point(20, 45), Size = new Size(700, 25) };

            Label contentLabel = new Label { Text = "المحتوى:", Location = new Point(20, 80), AutoSize = true };
            TextBox contentTextBox = new TextBox { Location = new Point(20, 105), Size = new Size(700, 150), Multiline = true };

            Label categoryLabel = new Label { Text = "الفئة:", Location = new Point(20, 240), AutoSize = true };
            ComboBox categoryComboBox = new ComboBox
            {
                Location = new Point(20, 265),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            categoryComboBox.Items.AddRange(new[] { "Employee Management", "Forms", "Leave", "Injury", "Delegates", "General" });
            categoryComboBox.SelectedIndex = 5;

            Label typeLabel = new Label { Text = "النوع:", Location = new Point(250, 240), AutoSize = true };
            ComboBox typeComboBox = new ComboBox
            {
                Location = new Point(250, 265),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            typeComboBox.Items.AddRange(new[] { "Tip", "Guideline", "Best Practice", "Warning" });
            typeComboBox.SelectedIndex = 0;

            Label orderLabel = new Label { Text = "ترتيب العرض:", Location = new Point(20, 300), AutoSize = true };
            NumericUpDown orderNumeric = new NumericUpDown
            {
                Location = new Point(20, 325),
                Size = new Size(100, 25),
                Minimum = 0,
                Maximum = 100,
                Value = 0
            };

            Button saveButton = new Button
            {
                Text = "حفظ",
                Location = new Point(20, 370),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(130, 370),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            saveButton.Click += (s, e) =>
            {
                if (string.IsNullOrWhiteSpace(titleTextBox.Text) || string.IsNullOrWhiteSpace(contentTextBox.Text))
                {
                    MessageBox.Show("يرجى ملء العنوان والمحتوى", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var tip = new Tip
                {
                    Title = titleTextBox.Text,
                    Content = contentTextBox.Text,
                    Category = categoryComboBox.SelectedItem?.ToString() ?? "General",
                    TipType = typeComboBox.SelectedItem?.ToString() ?? "Tip",
                    DisplayOrder = (int)orderNumeric.Value,
                    CreatedBy = DEVELOPER_NAME
                };

                if (tipManager.AddTip(tip))
                {
                    MessageBox.Show("تم إضافة التلميح بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    addForm.Close();
                    LoadTipsView();
                }
                else
                {
                    MessageBox.Show("فشل في إضافة التلميح", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            cancelButton.Click += (s, e) => addForm.Close();

            addForm.Controls.AddRange(new Control[] {
                titleLabel, titleTextBox, contentLabel, contentTextBox,
                categoryLabel, categoryComboBox, typeLabel, typeComboBox,
                orderLabel, orderNumeric, saveButton, cancelButton
            });

            addForm.ShowDialog();
        }

        // دوال إدراج الملفات - Drag and Drop Functions
        private void Form1_DragEnter(object? sender, DragEventArgs e)
        {
            if (e.Data != null && e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                e.Effect = DragDropEffects.Copy;
            }
            else
            {
                e.Effect = DragDropEffects.None;
            }
        }

        private void Form1_DragDrop(object? sender, DragEventArgs e)
        {
            if (e.Data != null && e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
                ProcessDroppedFiles(files);
            }
        }

        private void ProcessDroppedFiles(string[] files)
        {
            try
            {
                string uploadFolder = Path.Combine(Application.StartupPath, "UploadedFiles");
                if (!Directory.Exists(uploadFolder))
                {
                    Directory.CreateDirectory(uploadFolder);
                }

                int successCount = 0;
                List<string> processedFiles = new List<string>();

                foreach (string sourceFile in files)
                {
                    if (File.Exists(sourceFile))
                    {
                        string fileName = Path.GetFileName(sourceFile);
                        string destinationFile = Path.Combine(uploadFolder, fileName);

                        // Add timestamp if file exists
                        if (File.Exists(destinationFile))
                        {
                            string nameWithoutExt = Path.GetFileNameWithoutExtension(fileName);
                            string extension = Path.GetExtension(fileName);
                            fileName = $"{nameWithoutExt}_{DateTime.Now:yyyyMMdd_HHmmss}{extension}";
                            destinationFile = Path.Combine(uploadFolder, fileName);
                        }

                        File.Copy(sourceFile, destinationFile);
                        processedFiles.Add(fileName);
                        successCount++;
                    }
                }

                if (successCount > 0)
                {
                    string fileList = string.Join("\n", processedFiles);
                    MessageBox.Show($"تم رفع {successCount} ملف بنجاح!\n\nالملفات المرفوعة:\n{fileList}",
                        "نجح رفع الملفات", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Switch to file manager view
                    LoadFileManagerView();
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على ملفات صالحة للرفع", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في رفع الملفات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Dialog Functions
        private void ShowAddEmployeeDialog(string employeeType = "")
        {
            Form employeeForm = new Form
            {
                Text = "إضافة موظف جديد بالتفصيل - Add New Employee",
                Size = new Size(1300, 750),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            Label titleLabel = new Label
            {
                Text = "نموذج إضافة موظف جديد",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(20, 20),
                Size = new Size(1250, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Employee Type Selection
            Label typeLabel = new Label
            {
                Text = "حالة الموظف:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 70),
                Size = new Size(100, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            RadioButton delegatedEmployee = new RadioButton
            {
                Text = "منتدب",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(130, 70),
                Size = new Size(100, 25),
                Checked = string.IsNullOrEmpty(employeeType) || employeeType == "delegate",
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            RadioButton injuryEmployee = new RadioButton
            {
                Text = "إصابة عمل",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(250, 70),
                Size = new Size(120, 25),
                Checked = employeeType == "injury",
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            RadioButton unpaidLeaveEmployee = new RadioButton
            {
                Text = "إجازة بدون مرتب",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(390, 70),
                Size = new Size(150, 25),
                Checked = employeeType == "unpaid_leave",
                ForeColor = Color.FromArgb(52, 73, 94)
            };



            // Basic Information
            Label basicInfoLabel = new Label
            {
                Text = "البيانات الأساسية:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 120),
                Size = new Size(150, 25)
            };

            // Name
            Label nameLabel = new Label
            {
                Text = "الاسم الكامل بالتفصيل:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(1050, 160),
                Size = new Size(150, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            TextBox nameTextBox = new TextBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(820, 160),
                Size = new Size(220, 25),
                RightToLeft = RightToLeft.Yes,
                BorderStyle = BorderStyle.FixedSingle,
                PlaceholderText = "الاسم الرباعي كاملاً"
            };

            // National ID
            Label nationalIdLabel = new Label
            {
                Text = "الرقم القومي الكامل:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(650, 160),
                Size = new Size(150, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            TextBox nationalIdTextBox = new TextBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(430, 160),
                Size = new Size(210, 25),
                RightToLeft = RightToLeft.Yes,
                BorderStyle = BorderStyle.FixedSingle,
                MaxLength = 14,
                PlaceholderText = "أدخل الرقم القومي 14 رقم"
            };

            // Insurance Number
            Label insuranceLabel = new Label
            {
                Text = "الرقم التأميني بالكامل:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(280, 160),
                Size = new Size(140, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            TextBox insuranceTextBox = new TextBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(20, 160),
                Size = new Size(250, 25),
                RightToLeft = RightToLeft.Yes,
                BorderStyle = BorderStyle.FixedSingle,
                PlaceholderText = "رقم التأمينات الاجتماعية الكامل"
            };

            // Dynamic panels for different employee types
            Panel delegationPanel = new Panel
            {
                Location = new Point(20, 200),
                Size = new Size(1250, 350),
                BackColor = Color.FromArgb(236, 240, 241),
                BorderStyle = BorderStyle.FixedSingle,
                Visible = false
            };

            Panel injuryPanel = new Panel
            {
                Location = new Point(20, 200),
                Size = new Size(1250, 200),
                BackColor = Color.FromArgb(255, 235, 235),
                BorderStyle = BorderStyle.FixedSingle,
                Visible = false
            };

            Panel unpaidLeavePanel = new Panel
            {
                Location = new Point(20, 200),
                Size = new Size(1250, 200),
                BackColor = Color.FromArgb(235, 255, 235),
                BorderStyle = BorderStyle.FixedSingle,
                Visible = false
            };

            // Delegation Panel Content
            Label delegationLabel = new Label
            {
                Text = "معلومات الانتداب:",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(10, 10),
                Size = new Size(300, 30),
                TextAlign = ContentAlignment.MiddleRight
            };

            Label decisionNumLabel = new Label
            {
                Text = "رقم القرار بالتفصيل:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(1050, 50),
                Size = new Size(140, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            TextBox decisionNumTextBox = new TextBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(850, 50),
                Size = new Size(190, 25),
                RightToLeft = RightToLeft.Yes,
                BorderStyle = BorderStyle.FixedSingle,
                PlaceholderText = "رقم قرار الانتداب كاملاً"
            };

            Label decisionDateLabel = new Label
            {
                Text = "تاريخ القرار بالتفصيل:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(680, 50),
                Size = new Size(150, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            DateTimePicker decisionDatePicker = new DateTimePicker
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(480, 50),
                Size = new Size(190, 25),
                Format = DateTimePickerFormat.Short,
                RightToLeft = RightToLeft.Yes
            };

            Label endDateLabel = new Label
            {
                Text = "نهاية الانتداب بالتفصيل:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(280, 50),
                Size = new Size(180, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            ComboBox endDateCombo = new ComboBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(20, 50),
                Size = new Size(250, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            endDateCombo.Items.AddRange(new string[] { "إدراج تاريخ", "حتى تاريخه", "لا يكتب التاريخ" });

            Label delegationFromLabel = new Label
            {
                Text = "جهة الانتداب من:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(920, 90),
                Size = new Size(120, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            TextBox delegationFromTextBox = new TextBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(720, 90),
                Size = new Size(190, 25),
                RightToLeft = RightToLeft.Yes,
                BorderStyle = BorderStyle.FixedSingle,
                PlaceholderText = "اسم الجهة المنتدب منها"
            };

            Label delegationToLabel = new Label
            {
                Text = "جهة الانتداب إلى:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(580, 90),
                Size = new Size(120, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            TextBox delegationToTextBox = new TextBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(380, 90),
                Size = new Size(190, 25),
                RightToLeft = RightToLeft.Yes,
                BorderStyle = BorderStyle.FixedSingle,
                PlaceholderText = "اسم الجهة المنتدب إليها"
            };

            Label appointmentTypeLabel = new Label
            {
                Text = "بند التعيين بالتفصيل:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(240, 90),
                Size = new Size(130, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            ComboBox appointmentTypeCombo = new ComboBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(20, 90),
                Size = new Size(210, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            appointmentTypeCombo.Items.AddRange(new string[] { "دائم", "فصل مستقل 1", "فصل مستقل 2", "فصل مستقل 3" });

            // الاشتراكات التأمينية - الصف الثالث
            Label insuranceContributionsLabel = new Label
            {
                Text = "الاشتراكات التأمينية بالتفصيل:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(1050, 130),
                Size = new Size(200, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            Label totalContributionsLabel = new Label
            {
                Text = "إجمالي الاشتراكات بالكامل:",
                Font = new Font("Tahoma", 11),
                Location = new Point(750, 130),
                Size = new Size(170, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            TextBox totalContributionsTextBox = new TextBox
            {
                Font = new Font("Tahoma", 11),
                Location = new Point(600, 130),
                Size = new Size(140, 25),
                RightToLeft = RightToLeft.Yes,
                PlaceholderText = "المبلغ الإجمالي بالكامل",
                BorderStyle = BorderStyle.FixedSingle
            };

            // النسب المحسوبة تلقائياً - الصف الثالث (تكملة)
            Label rate12Label = new Label
            {
                Text = "12%:",
                Font = new Font("Tahoma", 10),
                Location = new Point(420, 130),
                Size = new Size(35, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            TextBox rate12TextBox = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(460, 130),
                Size = new Size(70, 25),
                RightToLeft = RightToLeft.Yes,
                ReadOnly = true,
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label rate9Label = new Label
            {
                Text = "9%:",
                Font = new Font("Tahoma", 10),
                Location = new Point(540, 130),
                Size = new Size(30, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            TextBox rate9TextBox = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(575, 130),
                Size = new Size(70, 25),
                RightToLeft = RightToLeft.Yes,
                ReadOnly = true,
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label rate3Label = new Label
            {
                Text = "3%:",
                Font = new Font("Tahoma", 10),
                Location = new Point(655, 130),
                Size = new Size(30, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            TextBox rate3TextBox = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(690, 130),
                Size = new Size(70, 25),
                RightToLeft = RightToLeft.Yes,
                ReadOnly = true,
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label rate1_1Label = new Label
            {
                Text = "1%:",
                Font = new Font("Tahoma", 10),
                Location = new Point(770, 130),
                Size = new Size(30, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            TextBox rate1_1TextBox = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(805, 130),
                Size = new Size(70, 25),
                RightToLeft = RightToLeft.Yes,
                ReadOnly = true,
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            // باقي النسب - الصف الرابع
            Label rate1_2Label = new Label
            {
                Text = "1%:",
                Font = new Font("Tahoma", 10),
                Location = new Point(10, 160),
                Size = new Size(30, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            TextBox rate1_2TextBox = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(45, 160),
                Size = new Size(70, 25),
                RightToLeft = RightToLeft.Yes,
                ReadOnly = true,
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label rate1_3Label = new Label
            {
                Text = "1%:",
                Font = new Font("Tahoma", 10),
                Location = new Point(125, 160),
                Size = new Size(30, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            TextBox rate1_3TextBox = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(160, 160),
                Size = new Size(70, 25),
                RightToLeft = RightToLeft.Yes,
                ReadOnly = true,
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label rate1_4Label = new Label
            {
                Text = "1%:",
                Font = new Font("Tahoma", 10),
                Location = new Point(240, 160),
                Size = new Size(30, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            TextBox rate1_4TextBox = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(275, 160),
                Size = new Size(70, 25),
                RightToLeft = RightToLeft.Yes,
                ReadOnly = true,
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            Label rate025Label = new Label
            {
                Text = "0.25%:",
                Font = new Font("Tahoma", 10),
                Location = new Point(355, 160),
                Size = new Size(45, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            TextBox rate025TextBox = new TextBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(405, 160),
                Size = new Size(70, 25),
                RightToLeft = RightToLeft.Yes,
                ReadOnly = true,
                BackColor = Color.FromArgb(248, 249, 250),
                BorderStyle = BorderStyle.FixedSingle
            };

            // الحقول الإضافية - الصف الخامس
            Label cashReplacementLabel = new Label
            {
                Text = "استبدال نقدي بالتفصيل:",
                Font = new Font("Tahoma", 11),
                Location = new Point(1050, 200),
                Size = new Size(150, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            TextBox cashReplacementTextBox = new TextBox
            {
                Font = new Font("Tahoma", 11),
                Location = new Point(850, 200),
                Size = new Size(190, 25),
                RightToLeft = RightToLeft.Yes,
                PlaceholderText = "المبلغ بالتفصيل",
                BorderStyle = BorderStyle.FixedSingle
            };

            Label considerationPeriodLabel = new Label
            {
                Text = "مدة اعتبارية بالتفصيل:",
                Font = new Font("Tahoma", 11),
                Location = new Point(650, 200),
                Size = new Size(150, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            TextBox considerationPeriodTextBox = new TextBox
            {
                Font = new Font("Tahoma", 11),
                Location = new Point(450, 200),
                Size = new Size(190, 25),
                RightToLeft = RightToLeft.Yes,
                PlaceholderText = "المدة بالأشهر بالتفصيل",
                BorderStyle = BorderStyle.FixedSingle
            };

            Label loanInstallmentLabel = new Label
            {
                Text = "قسط إعارة بالتفصيل:",
                Font = new Font("Tahoma", 11),
                Location = new Point(280, 200),
                Size = new Size(130, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            TextBox loanInstallmentTextBox = new TextBox
            {
                Font = new Font("Tahoma", 11),
                Location = new Point(80, 200),
                Size = new Size(190, 25),
                RightToLeft = RightToLeft.Yes,
                PlaceholderText = "المبلغ بالتفصيل",
                BorderStyle = BorderStyle.FixedSingle
            };

            Label otherLabel = new Label
            {
                Text = "بيانات أخرى:",
                Font = new Font("Tahoma", 11),
                Location = new Point(950, 230),
                Size = new Size(100, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            TextBox otherTextBox = new TextBox
            {
                Font = new Font("Tahoma", 11),
                Location = new Point(650, 230),
                Size = new Size(290, 25),
                RightToLeft = RightToLeft.Yes,
                PlaceholderText = "بيانات إضافية أخرى بالتفصيل",
                BorderStyle = BorderStyle.FixedSingle
            };

            // زر رفع الملفات - الصف السابع
            Button uploadFilesBtn = new Button
            {
                Text = "رفع ملفات الانتداب بالتفصيل\nUpload Delegation Files",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(250, 50),
                Location = new Point(950, 270),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                TextAlign = ContentAlignment.MiddleCenter
            };
            uploadFilesBtn.Click += (s, e) => ShowDocumentUploadDialog();

            // إضافة حدث حساب النسب التلقائي
            totalContributionsTextBox.TextChanged += (s, e) =>
            {
                if (decimal.TryParse(totalContributionsTextBox.Text, out decimal total) && total > 0)
                {
                    // حساب النسب
                    rate12TextBox.Text = (total * 0.12m).ToString("F2");
                    rate9TextBox.Text = (total * 0.09m).ToString("F2");
                    rate3TextBox.Text = (total * 0.03m).ToString("F2");
                    rate1_1TextBox.Text = (total * 0.01m).ToString("F2");
                    rate1_2TextBox.Text = (total * 0.01m).ToString("F2");
                    rate1_3TextBox.Text = (total * 0.01m).ToString("F2");
                    rate1_4TextBox.Text = (total * 0.01m).ToString("F2");
                    rate025TextBox.Text = (total * 0.0025m).ToString("F2");
                }
                else
                {
                    // مسح الحقول إذا كان الإدخال غير صحيح
                    rate12TextBox.Clear();
                    rate9TextBox.Clear();
                    rate3TextBox.Clear();
                    rate1_1TextBox.Clear();
                    rate1_2TextBox.Clear();
                    rate1_3TextBox.Clear();
                    rate1_4TextBox.Clear();
                    rate025TextBox.Clear();
                }
            };

            delegationPanel.Controls.AddRange(new Control[] {
                delegationLabel,
                decisionNumLabel, decisionNumTextBox, decisionDateLabel, decisionDatePicker,
                endDateLabel, endDateCombo, delegationFromLabel, delegationFromTextBox,
                delegationToLabel, delegationToTextBox, appointmentTypeLabel, appointmentTypeCombo,
                insuranceContributionsLabel, totalContributionsLabel, totalContributionsTextBox,
                rate12Label, rate12TextBox, rate9Label, rate9TextBox, rate3Label, rate3TextBox, rate1_1Label, rate1_1TextBox,
                rate1_2Label, rate1_2TextBox, rate1_3Label, rate1_3TextBox, rate1_4Label, rate1_4TextBox, rate025Label, rate025TextBox,
                cashReplacementLabel, cashReplacementTextBox, considerationPeriodLabel, considerationPeriodTextBox,
                loanInstallmentLabel, loanInstallmentTextBox, otherLabel, otherTextBox,
                uploadFilesBtn
            });

            // Regular employee information
            Label jobTitleLabel = new Label
            {
                Text = "المسمى الوظيفي بالتفصيل:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(1050, 570),
                Size = new Size(170, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            TextBox jobTitleTextBox = new TextBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(820, 570),
                Size = new Size(220, 25),
                RightToLeft = RightToLeft.Yes,
                BorderStyle = BorderStyle.FixedSingle,
                PlaceholderText = "المسمى الوظيفي الكامل"
            };

            Label departmentLabel = new Label
            {
                Text = "القسم/الإدارة بالتفصيل:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(650, 570),
                Size = new Size(150, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            TextBox departmentTextBox = new TextBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(420, 570),
                Size = new Size(220, 25),
                RightToLeft = RightToLeft.Yes,
                BorderStyle = BorderStyle.FixedSingle,
                PlaceholderText = "اسم القسم والإدارة بالكامل"
            };

            Label hireDateLabel = new Label
            {
                Text = "تاريخ التعيين بالتفصيل:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(250, 570),
                Size = new Size(150, 25),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = Color.FromArgb(52, 73, 94)
            };

            DateTimePicker hireDatePicker = new DateTimePicker
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(20, 570),
                Size = new Size(220, 25),
                Format = DateTimePickerFormat.Short,
                RightToLeft = RightToLeft.Yes
            };

            // Add content to injury panel
            Label injuryLabel = new Label
            {
                Text = "معلومات إصابة العمل:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(231, 76, 60),
                Location = new Point(10, 10),
                Size = new Size(200, 25)
            };

            Label injuryTypeLabel = new Label
            {
                Text = "نوع الإصابة:",
                Font = new Font("Tahoma", 10),
                Location = new Point(10, 50),
                Size = new Size(80, 25)
            };

            ComboBox injuryTypeCombo = new ComboBox
            {
                Font = new Font("Arial", 10),
                Location = new Point(100, 50),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            injuryTypeCombo.Items.AddRange(new string[] { "إصابة داخل العمل", "إصابة خارج العمل", "مرض مهني" });

            Label injuryDateLabel = new Label
            {
                Text = "تاريخ الإصابة:",
                Font = new Font("Tahoma", 10),
                Location = new Point(320, 50),
                Size = new Size(80, 25)
            };

            DateTimePicker injuryDatePicker = new DateTimePicker
            {
                Font = new Font("Arial", 10),
                Location = new Point(410, 50),
                Size = new Size(150, 25),
                Format = DateTimePickerFormat.Short
            };

            Button uploadInjuryFilesBtn = new Button
            {
                Text = "رفع ملفات الإصابة\nUpload Injury Files",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(150, 50),
                Location = new Point(10, 90),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            uploadInjuryFilesBtn.Click += (s, e) => ShowInjuryDocumentsDialog();

            Button injuryDocsHintBtn = new Button
            {
                Text = "المستندات المطلوبة\nRequired Documents",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(150, 50),
                Location = new Point(180, 90),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            injuryDocsHintBtn.Click += (s, e) => ShowInjuryDocumentsHint();

            injuryPanel.Controls.AddRange(new Control[] {
                injuryLabel, injuryTypeLabel, injuryTypeCombo, injuryDateLabel, injuryDatePicker,
                uploadInjuryFilesBtn, injuryDocsHintBtn
            });

            // Add content to unpaid leave panel
            Label unpaidLeaveLabel = new Label
            {
                Text = "معلومات الإجازة بدون مرتب:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(10, 10),
                Size = new Size(250, 25)
            };

            Label unpaidLeaveTypeLabel = new Label
            {
                Text = "نوع الإجازة:",
                Font = new Font("Tahoma", 10),
                Location = new Point(10, 50),
                Size = new Size(80, 25)
            };

            ComboBox unpaidLeaveTypeCombo = new ComboBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(100, 50),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            unpaidLeaveTypeCombo.Items.AddRange(new string[] { "إجازة داخل البلاد", "إجازة خارج البلاد", "إجازة رعاية طفل" });

            Label leaveStartLabel = new Label
            {
                Text = "تاريخ بداية الإجازة:",
                Font = new Font("Tahoma", 10),
                Location = new Point(320, 50),
                Size = new Size(120, 25)
            };

            DateTimePicker leaveStartPicker = new DateTimePicker
            {
                Font = new Font("Arial", 10),
                Location = new Point(450, 50),
                Size = new Size(150, 25),
                Format = DateTimePickerFormat.Short
            };

            Label leaveStatusLabel = new Label
            {
                Text = "حالة الإجازة:",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Location = new Point(10, 90),
                Size = new Size(120, 25)
            };

            ComboBox leaveStatusCombo = new ComboBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(140, 90),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            leaveStatusCombo.Items.AddRange(new string[] { "أول مرة", "تجديد" });

            Label unpaidLeaveEndLabel = new Label
            {
                Text = "تاريخ انتهاء الإجازة:",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Location = new Point(310, 90),
                Size = new Size(150, 25)
            };

            DateTimePicker unpaidLeaveEndPicker = new DateTimePicker
            {
                Font = new Font("Arial", 10),
                Location = new Point(470, 90),
                Size = new Size(150, 25),
                Format = DateTimePickerFormat.Short
            };

            Button renewalDocsBtn = new Button
            {
                Text = "الملفات المطلوبة\nRequired Files",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(180, 50),
                Location = new Point(640, 90),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                TextAlign = ContentAlignment.MiddleCenter
            };
            renewalDocsBtn.Click += (s, e) => ShowUnpaidLeaveDocumentsDialog();

            unpaidLeavePanel.Controls.AddRange(new Control[] {
                unpaidLeaveLabel, unpaidLeaveTypeLabel, unpaidLeaveTypeCombo, leaveStartLabel, leaveStartPicker,
                leaveStatusLabel, leaveStatusCombo, unpaidLeaveEndLabel, unpaidLeaveEndPicker, renewalDocsBtn
            });

            // Set initial panel visibility based on employee type
            delegationPanel.Visible = string.IsNullOrEmpty(employeeType) || employeeType == "delegate";
            injuryPanel.Visible = employeeType == "injury";
            unpaidLeavePanel.Visible = employeeType == "unpaid_leave";

            // Event handlers for radio buttons
            delegatedEmployee.CheckedChanged += (s, e) =>
            {
                if (delegatedEmployee.Checked)
                {
                    delegationPanel.Visible = true;
                    injuryPanel.Visible = false;
                    unpaidLeavePanel.Visible = false;
                }
            };

            injuryEmployee.CheckedChanged += (s, e) =>
            {
                if (injuryEmployee.Checked)
                {
                    injuryPanel.Visible = true;
                    delegationPanel.Visible = false;
                    unpaidLeavePanel.Visible = false;
                }
            };

            unpaidLeaveEmployee.CheckedChanged += (s, e) =>
            {
                if (unpaidLeaveEmployee.Checked)
                {
                    unpaidLeavePanel.Visible = true;
                    delegationPanel.Visible = false;
                    injuryPanel.Visible = false;
                }
            };

            // Buttons
            Button saveBtn = new Button
            {
                Text = "حفظ الموظف بالتفصيل\nSave Employee",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(150, 50),
                Location = new Point(650, 620),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button cancelBtn = new Button
            {
                Text = "إلغاء العملية\nCancel",
                Font = new Font("Tahoma", 12),
                Size = new Size(150, 50),
                Location = new Point(820, 620),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            saveBtn.Click += (s, e) =>
            {
                // التحقق من البيانات الأساسية
                if (string.IsNullOrWhiteSpace(nameTextBox.Text))
                {
                    MessageBox.Show("⚠️ يرجى إدخال اسم الموظف\n\nالاسم مطلوب لحفظ بيانات الموظف",
                                  "بيانات ناقصة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    nameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(nationalIdTextBox.Text))
                {
                    MessageBox.Show("⚠️ يرجى إدخال الرقم القومي\n\nالرقم القومي مطلوب لحفظ بيانات الموظف",
                                  "بيانات ناقصة", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    nationalIdTextBox.Focus();
                    return;
                }

                // التحقق من صحة الرقم القومي
                if (nationalIdTextBox.Text.Length != 14 || !nationalIdTextBox.Text.All(char.IsDigit))
                {
                    MessageBox.Show("⚠️ الرقم القومي يجب أن يكون 14 رقماً\n\nيرجى التأكد من إدخال الرقم القومي بشكل صحيح",
                                  "رقم قومي غير صحيح", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    nationalIdTextBox.Focus();
                    return;
                }

                try
                {
                    // تحديد نوع الموظف
                    string employeeType = "منتدب"; // الافتراضي
                    if (delegatedEmployee.Checked) employeeType = "منتدب";
                    else if (injuryEmployee.Checked) employeeType = "إصابة عمل";
                    else if (unpaidLeaveEmployee.Checked) employeeType = "إجازة بدون مرتب";

                    // إنشاء كائن الموظف
                    Employee employee = new Employee
                    {
                        Name = nameTextBox.Text.Trim(),
                        NationalId = nationalIdTextBox.Text.Trim(),
                        InsuranceNumber = insuranceTextBox.Text.Trim(),
                        JobTitle = jobTitleTextBox.Text.Trim(),
                        Department = departmentTextBox.Text.Trim(),
                        HireDate = hireDatePicker.Value,
                        EmployeeType = employeeType
                    };

                    // إضافة البيانات الخاصة حسب نوع الموظف
                    if (delegatedEmployee.Checked)
                    {
                        employee.DecisionNumber = decisionNumTextBox.Text.Trim();
                        employee.DecisionDate = decisionDatePicker.Value;
                        employee.DelegationType = $"من: {delegationFromTextBox.Text} - إلى: {delegationToTextBox.Text}"; // جهة الانتداب
                        employee.WorkDepartment = endDateCombo.Text; // نهاية الانتداب
                        employee.Management = appointmentTypeCombo.Text; // بند التعيين
                        employee.LeaveEndDate = decisionDatePicker.Value;
                        employee.AppointmentType = appointmentTypeCombo.Text;
                        employee.RequiredDocuments = $"إجمالي الاشتراكات: {totalContributionsTextBox.Text}, استبدال نقدي: {cashReplacementTextBox.Text}, مدة اعتبارية: {considerationPeriodTextBox.Text}, قسط إعارة: {loanInstallmentTextBox.Text}, أخرى: {otherTextBox.Text}";
                    }
                    else if (injuryEmployee.Checked)
                    {
                        employee.InjuryType = injuryTypeCombo.Text;
                        employee.InjuryDate = injuryDatePicker.Value;
                    }
                    else if (unpaidLeaveEmployee.Checked)
                    {
                        employee.UnpaidLeaveType = unpaidLeaveTypeCombo.Text;
                        employee.LeaveStatus = leaveStatusCombo.Text;
                        employee.LeaveStartDate = leaveStartPicker.Value;
                        employee.UnpaidLeaveEndDate = unpaidLeaveEndPicker.Value;
                    }

                    // رسالة النجاح
                    string message = $"✅ تم حفظ بيانات الموظف بنجاح\n\n";
                    message += $"📝 الاسم: {employee.Name}\n";
                    message += $"🆔 الرقم القومي: {employee.NationalId}\n";
                    message += $"🏥 الرقم التأميني: {employee.InsuranceNumber}\n";
                    message += $"👔 النوع: {employee.EmployeeType}\n";

                    if (delegatedEmployee.Checked)
                    {
                        message += $"\n📋 تفاصيل الانتداب:\n";
                        message += $"📄 رقم القرار: {employee.DecisionNumber}\n";
                        message += $"📅 تاريخ القرار: {(employee.DecisionDate.HasValue ? FormatDateToArabic(employee.DecisionDate.Value) : "غير محدد")}\n";
                        message += $"🏢 الجهة: {employee.DelegationType}\n";
                        message += $"🏛️ جهة العمل: {employee.WorkDepartment}\n";
                        message += $"📊 الإدارة: {employee.Management}\n";
                        message += $"⏰ تاريخ انتهاء الإجازة: {(employee.LeaveEndDate.HasValue ? FormatDateToArabic(employee.LeaveEndDate.Value) : "غير محدد")}";
                    }
                    else if (injuryEmployee.Checked)
                    {
                        message += $"\n🚨 تفاصيل الإصابة:\n";
                        message += $"⚕️ نوع الإصابة: {employee.InjuryType}\n";
                        message += $"📅 تاريخ الإصابة: {(employee.InjuryDate.HasValue ? FormatDateToArabic(employee.InjuryDate.Value) : "غير محدد")}";
                    }
                    else if (unpaidLeaveEmployee.Checked)
                    {
                        message += $"\n🏖️ تفاصيل الإجازة:\n";
                        message += $"📋 نوع الإجازة: {employee.UnpaidLeaveType}\n";
                        message += $"🔄 حالة الإجازة: {employee.LeaveStatus}\n";
                        message += $"📅 تاريخ البداية: {(employee.LeaveStartDate.HasValue ? FormatDateToArabic(employee.LeaveStartDate.Value) : "غير محدد")}\n";
                        message += $"⏰ تاريخ الانتهاء: {(employee.UnpaidLeaveEndDate.HasValue ? FormatDateToArabic(employee.UnpaidLeaveEndDate.Value) : "غير محدد")}";
                    }
                    else
                    {
                        message += $"\n💼 تفاصيل العمل:\n";
                        message += $"👔 المنصب: {employee.JobTitle}\n";
                        message += $"🏢 القسم: {employee.Department}\n";
                        message += $"📅 تاريخ التوظيف: {FormatDateToArabic(employee.HireDate)}";
                    }

                    // إضافة معلومات المطور
                    employee.CreatedBy = DEVELOPER_NAME;

                    // Save to database
                    try
                    {
                        int newEmployeeId = employeeManager.AddEmployeeAndGetId(employee);
                        if (newEmployeeId > 0)
                        {
                            // ربط الملفات المؤقتة بالموظف الجديد
                            LinkTemporaryFilesToEmployee(newEmployeeId, employee.Name);

                            MessageBox.Show($"{message}\n\n✅ تم حفظ البيانات في قاعدة البيانات بنجاح\n📁 تم ربط الملفات المرفوعة بالموظف\n🆔 رقم الموظف الجديد: {newEmployeeId}", "تم الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            employeeForm.Close();
                            LoadEmployeesView(); // Refresh the employees view
                        }
                        else
                        {
                            // إضافة تشخيص قاعدة البيانات
                            string diagnosis = employeeManager.DiagnoseDatabaseIssues();
                            MessageBox.Show($"فشل في حفظ الموظف في قاعدة البيانات\n\n📊 تشخيص قاعدة البيانات:\n{diagnosis}\n\nتأكد من:\n- تشغيل SQL Server LocalDB\n- صحة البيانات المدخلة\n- عدم تكرار الرقم الوطني",
                                          "خطأ في الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        // إضافة تشخيص مفصل للخطأ
                        string diagnosis = "";
                        try
                        {
                            diagnosis = employeeManager.DiagnoseDatabaseIssues();
                        }
                        catch
                        {
                            diagnosis = "❌ فشل في تشخيص قاعدة البيانات";
                        }

                        MessageBox.Show($"خطأ في حفظ الموظف:\n\n🔴 الخطأ: {ex.Message}\n\n📊 تشخيص قاعدة البيانات:\n{diagnosis}\n\n🔧 تفاصيل إضافية:\n- تأكد من تشغيل SQL Server LocalDB\n- تحقق من صحة البيانات المدخلة\n- تأكد من عدم تكرار الرقم القومي",
                                      "خطأ في قاعدة البيانات", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ عام في حفظ الموظف:\n\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            cancelBtn.Click += (s, e) => employeeForm.Close();

            employeeForm.Controls.AddRange(new Control[] {
                titleLabel, typeLabel, delegatedEmployee, injuryEmployee, unpaidLeaveEmployee,
                basicInfoLabel, nameLabel, nameTextBox, nationalIdLabel, nationalIdTextBox,
                insuranceLabel, insuranceTextBox, delegationPanel, injuryPanel, unpaidLeavePanel,
                jobTitleLabel, jobTitleTextBox, departmentLabel, departmentTextBox,
                hireDateLabel, hireDatePicker, saveBtn, cancelBtn
            });

            employeeForm.ShowDialog();
        }

        private void ShowEmployeeSearchDialog()
        {
            MessageBox.Show("البحث عن موظف\n\nيمكنك البحث بـ:\n- الاسم\n- رقم الموظف\n- القسم\n- المنصب", "البحث عن موظف", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowCreateFormDialog()
        {
            MessageBox.Show("إنشاء نموذج جديد\n\nأنواع النماذج المتاحة:\n- طلب إجازة\n- طلب ترقية\n- طلب تدريب\n- شكوى\n- طلب نقل", "إنشاء نموذج", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowApproveFormsDialog()
        {
            MessageBox.Show("الموافقة على النماذج\n\nالنماذج المعلقة:\n- F001: طلب إجازة (أحمد محمد)\n- F003: طلب تدريب (محمد سالم)", "الموافقة على النماذج", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowReportInjuryDialog()
        {
            Form injuryForm = new Form
            {
                Text = "الإبلاغ عن إصابة عمل - Work Injury Report",
                Size = new Size(900, 700),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            Label titleLabel = new Label
            {
                Text = "نموذج الإبلاغ عن إصابة العمل",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(231, 76, 60),
                Location = new Point(20, 20),
                Size = new Size(850, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Basic Information
            Label basicInfoLabel = new Label
            {
                Text = "البيانات الأساسية:",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 70),
                Size = new Size(200, 25)
            };

            Label nameLabel = new Label
            {
                Text = "اسم المصاب:",
                Font = new Font("Tahoma", 12),
                Location = new Point(20, 110),
                Size = new Size(100, 25)
            };

            TextBox nameTextBox = new TextBox
            {
                Font = new Font("Arial", 12),
                Location = new Point(130, 110),
                Size = new Size(200, 25)
            };

            Label dateLabel = new Label
            {
                Text = "تاريخ الإصابة:",
                Font = new Font("Tahoma", 12),
                Location = new Point(350, 110),
                Size = new Size(100, 25)
            };

            DateTimePicker injuryDate = new DateTimePicker
            {
                Font = new Font("Arial", 12),
                Location = new Point(460, 110),
                Size = new Size(150, 25),
                Format = DateTimePickerFormat.Custom,
                CustomFormat = "dd MMMM yyyy"
            };

            Label locationLabel = new Label
            {
                Text = "مكان الإصابة:",
                Font = new Font("Tahoma", 12),
                Location = new Point(20, 150),
                Size = new Size(100, 25)
            };

            ComboBox locationCombo = new ComboBox
            {
                Font = new Font("Arial", 12),
                Location = new Point(130, 150),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            locationCombo.Items.AddRange(new string[] { "داخل العمل", "خارج العمل" });

            // Required Documents Section
            Label documentsLabel = new Label
            {
                Text = "المستندات المطلوبة:",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 200),
                Size = new Size(200, 25)
            };

            // Documents list
            CheckedListBox documentsChecklist = new CheckedListBox
            {
                Font = new Font("Tahoma", 10),
                Location = new Point(20, 240),
                Size = new Size(850, 200),
                CheckOnClick = true
            };

            string[] requiredDocuments = {
                "1. محضر الشرطة (إذا كانت الإصابة خارج العمل)",
                "2. محضر إداري (إذا كانت الإصابة في العمل)",
                "3. تقرير أولي من المستشفى فيه ساعة ودخول المستشفى أول مرة",
                "4. خط سير",
                "5. أمر تكليف بعمل من الجهة بمكان وقع الإصابة",
                "6. نموذج (61) قرار وزاري",
                "7. نموذج (103) مكرر",
                "8. نموذج (22)",
                "9. نموذج (24)"
            };

            documentsChecklist.Items.AddRange(requiredDocuments);

            // Upload buttons
            Button uploadDocsBtn = new Button
            {
                Text = "رفع المستندات\nUpload Documents",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(120, 50),
                Location = new Point(20, 460),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button uploadInjuryFilesBtn = new Button
            {
                Text = "رفع ملفات الإصابة\nUpload Injury Files",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(130, 50),
                Location = new Point(150, 460),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button disabilityCommitteeBtn = new Button
            {
                Text = "تحويل للجنة العجز\nDisability Committee",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(120, 50),
                Location = new Point(150, 460),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button saveBtn = new Button
            {
                Text = "حفظ البلاغ\nSave Report",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(120, 50),
                Location = new Point(600, 460),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button cancelBtn = new Button
            {
                Text = "إلغاء\nCancel",
                Font = new Font("Tahoma", 12),
                Size = new Size(120, 50),
                Location = new Point(730, 460),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // Event handlers
            uploadDocsBtn.Click += (s, e) => ShowDocumentUploadDialog();
            uploadInjuryFilesBtn.Click += (s, e) => ShowInjuryDocumentsDialog();
            disabilityCommitteeBtn.Click += (s, e) => ShowDisabilityCommitteeDialog();

            saveBtn.Click += (s, e) =>
            {
                if (string.IsNullOrEmpty(nameTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المصاب", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (locationCombo.SelectedIndex == -1)
                {
                    MessageBox.Show("يرجى تحديد مكان الإصابة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                MessageBox.Show($"تم حفظ بلاغ الإصابة بنجاح\n\nاسم المصاب: {nameTextBox.Text}\nتاريخ الإصابة: {FormatDateToArabic(injuryDate.Value)}\nمكان الإصابة: {locationCombo.Text}\nالمستندات المحددة: {documentsChecklist.CheckedItems.Count} من {documentsChecklist.Items.Count}",
                    "تم الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);

                injuryForm.Close();
            };

            cancelBtn.Click += (s, e) => injuryForm.Close();

            injuryForm.Controls.AddRange(new Control[] {
                titleLabel, basicInfoLabel, nameLabel, nameTextBox, dateLabel, injuryDate,
                locationLabel, locationCombo, documentsLabel, documentsChecklist,
                uploadDocsBtn, uploadInjuryFilesBtn, disabilityCommitteeBtn, saveBtn, cancelBtn
            });

            injuryForm.ShowDialog();
        }

        private void ShowDomesticLeaveDialog()
        {
            MessageBox.Show("طلب إجازة داخل البلاد\n\nالمعلومات المطلوبة:\n- تاريخ بداية الإجازة\n- تاريخ نهاية الإجازة\n- سبب الإجازة\n- عنوان الإقامة\n- رقم الاتصال", "إجازة داخل البلاد", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowInternationalLeaveDialog()
        {
            MessageBox.Show("طلب إجازة خارج البلاد\n\nالمعلومات المطلوبة:\n- تاريخ بداية الإجازة\n- تاريخ نهاية الإجازة\n- البلد المقصود\n- سبب السفر\n- معلومات الاتصال\n- تذكرة الطيران", "إجازة خارج البلاد", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowChildCareLeaveDialog()
        {
            MessageBox.Show("طلب إجازة رعاية الطفل\n\nالمعلومات المطلوبة:\n- اسم الطفل\n- تاريخ الميلاد\n- مدة الإجازة المطلوبة\n- شهادة الميلاد\n- تقرير طبي (إن وجد)", "إجازة رعاية الطفل", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowDelegateToCouncilDialog()
        {
            MessageBox.Show("انتداب موظف إلى مجلس المدينة\n\nالمعلومات المطلوبة:\n- اسم الموظف\n- المنصب الحالي\n- مدة الانتداب\n- المهام المطلوبة\n- تاريخ البداية", "انتداب إلى المجلس", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowDelegateFromCouncilDialog()
        {
            MessageBox.Show("استقبال منتدب من مجلس المدينة\n\nالمعلومات المطلوبة:\n- اسم المنتدب\n- المنصب في المجلس\n- مدة الانتداب\n- القسم المستقبل\n- المهام المحددة", "انتداب من المجلس", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowNewAchievementDialog()
        {
            MessageBox.Show("تسجيل إنجاز جديد\n\nالمعلومات المطلوبة:\n- عنوان الإنجاز\n- وصف مفصل\n- الموظف المسؤول\n- تاريخ الإنجاز\n- التقييم\n- الملاحظات", "إنجاز جديد", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowMonthlyReportDialog()
        {
            MessageBox.Show("التقرير الشهري للإنجازات\n\nيتضمن:\n- إجمالي الإنجازات\n- الموظفين المتميزين\n- المشاريع المكتملة\n- الأهداف المحققة\n- التوصيات", "التقرير الشهري", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowRenewalDocumentsDialog()
        {
            Form renewalForm = new Form
            {
                Text = "مستندات الإجازة بدون مرتب - Unpaid Leave Documents",
                Size = new Size(1000, 700),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            Label titleLabel = new Label
            {
                Text = "مستندات الإجازة بدون مرتب",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(230, 126, 34),
                Location = new Point(20, 20),
                Size = new Size(950, 30),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            // Document type selection
            Label typeLabel = new Label
            {
                Text = "نوع المستندات:",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 70),
                Size = new Size(150, 25),
                RightToLeft = RightToLeft.Yes
            };

            ComboBox typeCombo = new ComboBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(180, 70),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            typeCombo.Items.AddRange(new string[] { "أول مرة", "تجديد", "استلام العمل" });
            typeCombo.SelectedIndex = 0;

            CheckedListBox documentsChecklist = new CheckedListBox
            {
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(20, 110),
                Size = new Size(950, 200),
                CheckOnClick = true,
                RightToLeft = RightToLeft.Yes
            };

            // Update documents based on selection
            typeCombo.SelectedIndexChanged += (s, e) =>
            {
                documentsChecklist.Items.Clear();
                switch (typeCombo.SelectedIndex)
                {
                    case 0: // أول مرة
                        documentsChecklist.Items.AddRange(new string[] {
                            "1. طلب من جهة العمل",
                            "2. إقرار من الرغبة أو عدم الرغبة",
                            "3. استمارة (1) عدد 3",
                            "4. استمارة (2) عدد 3",
                            "5. استمارة (3) عدد 3 عند استلام العمل"
                        });
                        break;
                    case 1: // تجديد
                        documentsChecklist.Items.AddRange(new string[] {
                            "1. طلب من جهة العمل",
                            "2. استمارة (2) عدد 3"
                        });
                        break;
                    case 2: // استلام العمل
                        documentsChecklist.Items.AddRange(new string[] {
                            "1. استمارة (1) عدد 3",
                            "2. استمارة (2) عدد 3"
                        });
                        break;
                }
            };

            // Initialize with first option
            documentsChecklist.Items.AddRange(new string[] {
                "1. طلب من جهة العمل",
                "2. إقرار من الرغبة أو عدم الرغبة",
                "3. استمارة (1) عدد 3",
                "4. استمارة (2) عدد 3",
                "5. استمارة (3) عدد 3 عند استلام العمل"
            });

            Button uploadBtn = new Button
            {
                Text = "رفع الملفات\nUpload Files",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(120, 50),
                Location = new Point(20, 290),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            uploadBtn.Click += (s, e) => ShowDocumentUploadDialog();

            Label hintsLabel = new Label
            {
                Text = "تلميحات مهمة:",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(20, 360),
                Size = new Size(150, 25),
                RightToLeft = RightToLeft.Yes
            };

            Label hint1Label = new Label
            {
                Text = "1- صورة إيصال السداد – صورة من خطاب السداد عن المدة السابقة والأصل بالملفات يسلم ونحتفظ بصورة بملفه- وصورة في طلب التجديد",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(231, 76, 60),
                Location = new Point(20, 400),
                Size = new Size(750, 40),
                RightToLeft = RightToLeft.Yes
            };

            Label hint2Label = new Label
            {
                Text = "2- تجديد إجازة بدون مرتب (استمارة 2 مكتوب فيها المنطقة التأمينية رقمها 92 ورقم المنشأة 354954) و (مفردات مرتب) بعد توقيع المفتش ترسل إلى هيئة التأمينات والمعاشات",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(20, 450),
                Size = new Size(750, 60),
                RightToLeft = RightToLeft.Yes
            };

            Button closeBtn = new Button
            {
                Text = "إغلاق\nClose",
                Font = new Font("Tahoma", 12),
                Size = new Size(120, 50),
                Location = new Point(650, 290),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            closeBtn.Click += (s, e) => renewalForm.Close();

            renewalForm.Controls.AddRange(new Control[] {
                titleLabel, typeLabel, typeCombo, documentsChecklist, uploadBtn, hintsLabel, hint1Label, hint2Label, closeBtn
            });

            renewalForm.ShowDialog();
        }

        private void ShowAddNotificationDialog()
        {
            Form notificationForm = new Form
            {
                Text = "إضافة تنبيه جديد - Add New Notification",
                Size = new Size(800, 600),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            Label titleLabel = new Label
            {
                Text = "إضافة تنبيه جديد",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(750, 30),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            Label notificationTitleLabel = new Label
            {
                Text = "عنوان التنبيه:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(20, 70),
                Size = new Size(150, 25),
                RightToLeft = RightToLeft.Yes
            };

            TextBox notificationTitleTextBox = new TextBox
            {
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                Location = new Point(180, 70),
                Size = new Size(550, 30),
                RightToLeft = RightToLeft.Yes
            };

            Label messageLabel = new Label
            {
                Text = "نص التنبيه:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(20, 120),
                Size = new Size(150, 25),
                RightToLeft = RightToLeft.Yes
            };

            TextBox messageTextBox = new TextBox
            {
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                Location = new Point(180, 120),
                Size = new Size(550, 100),
                Multiline = true,
                RightToLeft = RightToLeft.Yes
            };

            Label typeLabel = new Label
            {
                Text = "نوع التنبيه:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(20, 220),
                Size = new Size(100, 25),
                RightToLeft = RightToLeft.Yes
            };

            ComboBox typeCombo = new ComboBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(130, 220),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            typeCombo.Items.AddRange(new string[] { "معلومات", "تحذير", "خطأ", "نجاح" });
            typeCombo.SelectedIndex = 0;

            Label priorityLabel = new Label
            {
                Text = "الأولوية:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(350, 220),
                Size = new Size(80, 25),
                RightToLeft = RightToLeft.Yes
            };

            ComboBox priorityCombo = new ComboBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(440, 220),
                Size = new Size(100, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            priorityCombo.Items.AddRange(new string[] { "عادي", "متوسط", "عالي", "عاجل" });
            priorityCombo.SelectedIndex = 1;

            Button saveBtn = new Button
            {
                Text = "حفظ التنبيه\nSave Notification",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(120, 50),
                Location = new Point(300, 280),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button cancelBtn = new Button
            {
                Text = "إلغاء\nCancel",
                Font = new Font("Tahoma", 12),
                Size = new Size(120, 50),
                Location = new Point(430, 280),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            saveBtn.Click += (s, e) =>
            {
                if (string.IsNullOrEmpty(notificationTitleTextBox.Text) || string.IsNullOrEmpty(messageTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال عنوان التنبيه والنص", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                MessageBox.Show($"تم حفظ التنبيه بنجاح\n\nالعنوان: {notificationTitleTextBox.Text}\nالنوع: {typeCombo.Text}\nالأولوية: {priorityCombo.Text}",
                    "تم الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);

                notificationForm.Close();
            };

            cancelBtn.Click += (s, e) => notificationForm.Close();

            notificationForm.Controls.AddRange(new Control[] {
                titleLabel, notificationTitleLabel, notificationTitleTextBox, messageLabel, messageTextBox,
                typeLabel, typeCombo, priorityLabel, priorityCombo, saveBtn, cancelBtn
            });

            notificationForm.ShowDialog();
        }

        private void ShowDelegatesReportDialog()
        {
            Form reportForm = new Form
            {
                Text = "بيان بأسماء الحاصلين على إجازات خاصة (بدون مرتب)",
                Size = new Size(1200, 700),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            Label titleLabel = new Label
            {
                Text = "بيان بأسماء الحاصلين على إجازات خاصة (بدون مرتب)",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 20),
                Size = new Size(1150, 30),
                TextAlign = ContentAlignment.MiddleCenter,
                RightToLeft = RightToLeft.Yes
            };

            // Create DataGridView for the report
            DataGridView reportGrid = new DataGridView
            {
                Location = new Point(20, 70),
                Size = new Size(1150, 500),
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                RightToLeft = RightToLeft.Yes,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            // Add columns
            reportGrid.Columns.Add("م", "م");
            reportGrid.Columns.Add("الاسم", "الاســــم");
            reportGrid.Columns.Add("الحالة_الوظيفية", "الحالة الوظيفية");
            reportGrid.Columns.Add("الادارة", "الإدارة التي يعمل بها");
            reportGrid.Columns.Add("بند_التعيين", "بند التعيين");

            // Set column widths
            reportGrid.Columns[0].Width = 50;
            reportGrid.Columns[1].Width = 200;
            reportGrid.Columns[2].Width = 400;
            reportGrid.Columns[3].Width = 200;
            reportGrid.Columns[4].Width = 150;

            // Add sample data
            reportGrid.Rows.Add("1", "أحمد محمد علي", "إجازة بدون مرتب - قرار رقم 123/2025 من 15/01/2025 إلى 15/01/2026", "الإدارة المالية", "دائم");
            reportGrid.Rows.Add("2", "فاطمة أحمد سالم", "رعاية طفل - قرار رقم 124/2025 من 01/02/2025 إلى 01/02/2027", "إدارة الموارد البشرية", "فصل مستقل 1");
            reportGrid.Rows.Add("3", "محمد سعد حسن", "إجازة بدون مرتب - قرار رقم 125/2025 من 10/03/2025 إلى 10/03/2026", "الإدارة الهندسية", "فصل مستقل 2");

            Button printBtn = new Button
            {
                Text = "طباعة التقرير\nPrint Report",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(120, 50),
                Location = new Point(900, 590),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button exportBtn = new Button
            {
                Text = "تصدير Excel\nExport Excel",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(120, 50),
                Location = new Point(770, 590),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button closeBtn = new Button
            {
                Text = "إغلاق\nClose",
                Font = new Font("Tahoma", 12),
                Size = new Size(120, 50),
                Location = new Point(1030, 590),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            printBtn.Click += (s, e) => MessageBox.Show("سيتم طباعة التقرير", "طباعة", MessageBoxButtons.OK, MessageBoxIcon.Information);
            exportBtn.Click += (s, e) => MessageBox.Show("سيتم تصدير التقرير إلى Excel", "تصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
            closeBtn.Click += (s, e) => reportForm.Close();

            reportForm.Controls.AddRange(new Control[] { titleLabel, reportGrid, printBtn, exportBtn, closeBtn });
            reportForm.ShowDialog();
        }

        private void ShowDocumentUploadDialog()
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Multiselect = true;
                openFileDialog.Filter = "جميع الملفات (*.*)|*.*|PDF files (*.pdf)|*.pdf|Word files (*.docx)|*.docx|صور (*.jpg;*.png)|*.jpg;*.png";
                openFileDialog.Title = "اختر المستندات المطلوبة";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string uploadedFiles = string.Join("\n", openFileDialog.FileNames.Select(f => Path.GetFileName(f)));
                    MessageBox.Show($"تم رفع المستندات بنجاح:\n\n{uploadedFiles}", "رفع المستندات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void ShowInjuryFilesUploadDialog()
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Multiselect = true;
                openFileDialog.Filter = "ملفات PDF (*.pdf)|*.pdf|صور (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp|مستندات Word (*.docx;*.doc)|*.docx;*.doc|جميع الملفات (*.*)|*.*";
                openFileDialog.Title = "اختر ملفات الإصابة (تقارير طبية، صور، مستندات)";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string uploadedFiles = string.Join("\n", openFileDialog.FileNames.Select(f => Path.GetFileName(f)));

                    // Create a detailed message with file types
                    var filesByType = openFileDialog.FileNames
                        .GroupBy(f => Path.GetExtension(f).ToLower())
                        .Select(g => $"{g.Key.ToUpper()}: {g.Count()} ملف")
                        .ToList();

                    string fileTypeSummary = string.Join(" | ", filesByType);

                    MessageBox.Show($"تم رفع ملفات الإصابة بنجاح:\n\n{uploadedFiles}\n\n📊 ملخص الملفات:\n{fileTypeSummary}\n\n📁 إجمالي الملفات: {openFileDialog.FileNames.Length}",
                        "رفع ملفات الإصابة", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void ShowInjuryDocumentsDialog()
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Multiselect = true;
                openFileDialog.Filter = "ملفات PDF (*.pdf)|*.pdf|صور (*.jpg;*.jpeg;*.png;*.bmp)|*.jpg;*.jpeg;*.png;*.bmp|مستندات Word (*.docx;*.doc)|*.docx;*.doc|جميع الملفات (*.*)|*.*";
                openFileDialog.Title = "رفع مستندات إصابة العمل";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string uploadedFiles = string.Join("\n", openFileDialog.FileNames.Select(f => Path.GetFileName(f)));
                    MessageBox.Show($"تم رفع مستندات إصابة العمل بنجاح:\n\n{uploadedFiles}\n\n📁 إجمالي الملفات: {openFileDialog.FileNames.Length}",
                        "رفع مستندات الإصابة", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void ShowInjuryDocumentsHint()
        {
            Form hintForm = new Form
            {
                Text = "المستندات المطلوبة في ملف إصابة العمل",
                Size = new Size(900, 700),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            Label titleLabel = new Label
            {
                Text = "المستندات المطلوبة في ملف إصابة العمل",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(231, 76, 60),
                Location = new Point(20, 20),
                Size = new Size(850, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            CheckedListBox documentsChecklist = new CheckedListBox
            {
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(20, 70),
                Size = new Size(850, 400),
                CheckOnClick = true,
                RightToLeft = RightToLeft.Yes
            };

            string[] injuryDocuments = {
                "1. محضر الشرطة (إذا كانت الإصابة خارج العمل) أو محضر إداري (إذا كانت الإصابة في العمل)",
                "2. تقرير أولي من المستشفى فيه ساعة ودخول المستشفى أول مرة",
                "3. خط سير",
                "4. أمر تكليف بعمل من الجهة بمكان وقع الإصابة",
                "5. نموذج (61) قرار وزاري",
                "6. نموذج (103) مكرر",
                "7. نموذج (22)",
                "8. نموذج (24)"
            };

            documentsChecklist.Items.AddRange(injuryDocuments);

            Button closeBtn = new Button
            {
                Text = "إغلاق\nClose",
                Font = new Font("Tahoma", 12),
                Size = new Size(120, 50),
                Location = new Point(400, 500),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            closeBtn.Click += (s, e) => hintForm.Close();

            hintForm.Controls.AddRange(new Control[] {
                titleLabel, documentsChecklist, closeBtn
            });

            hintForm.ShowDialog();
        }

        private void ShowDisabilityCommitteeDialog()
        {
            Form disabilityForm = new Form
            {
                Text = "تحويل للجنة العجز الكلي - Disability Committee Transfer",
                Size = new Size(1000, 800),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            Label titleLabel = new Label
            {
                Text = "الأوراق المطلوبة لتحويل الموظف للجنة الطبية العليا قرار (66) مكرر",
                Font = new Font("Tahoma", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(230, 126, 34),
                Location = new Point(20, 20),
                Size = new Size(950, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            CheckedListBox documentsChecklist = new CheckedListBox
            {
                Font = new Font("Tahoma", 11),
                Location = new Point(20, 70),
                Size = new Size(950, 400),
                CheckOnClick = true
            };

            string[] disabilityDocuments = {
                "1. بيان حالة وظيفية",
                "2. بيان طبيعة عمل",
                "3. إقرار من جهة عمله بأنه لم يعرض على أي لجان طبية من قبل الإجازات",
                "4. إقرار من نفسه بأنه لم يعرض على أي لجان من قبل",
                "5. عدد 2 نموذج تحويل لجنة طبية مختومة بالشعار ومرفق به صورة شخصية مختومة (103) مكرر",
                "6. صورة طبق الأصل من نتيجة الكشف الطبي عند استلام العمل",
                "7. في حالة عدم وجود نتيجة الكشف يرجى إحضار صورة من بطاقة التأمين الصحي مختوم بالشعار",
                "8. بيان بالإجازات المرضية مختومة",
                "9. صورة طبق الأصل من نتائج الإجازات المرضية السابقة",
                "10. صورة بطاقة مختومة بالشعار"
            };

            documentsChecklist.Items.AddRange(disabilityDocuments);

            Label employeeInfoLabel = new Label
            {
                Text = "بيانات الموظف:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                Location = new Point(20, 490),
                Size = new Size(150, 25)
            };

            Label empNameLabel = new Label
            {
                Text = "اسم الموظف:",
                Font = new Font("Tahoma", 10),
                Location = new Point(20, 530),
                Size = new Size(100, 25)
            };

            TextBox empNameTextBox = new TextBox
            {
                Font = new Font("Arial", 10),
                Location = new Point(130, 530),
                Size = new Size(200, 25)
            };

            Label empIdLabel = new Label
            {
                Text = "الرقم القومي:",
                Font = new Font("Tahoma", 10),
                Location = new Point(350, 530),
                Size = new Size(100, 25)
            };

            TextBox empIdTextBox = new TextBox
            {
                Font = new Font("Arial", 10),
                Location = new Point(460, 530),
                Size = new Size(200, 25)
            };

            Label jobTitleLabel = new Label
            {
                Text = "المنصب:",
                Font = new Font("Tahoma", 10),
                Location = new Point(20, 570),
                Size = new Size(100, 25)
            };

            TextBox jobTitleTextBox = new TextBox
            {
                Font = new Font("Arial", 10),
                Location = new Point(130, 570),
                Size = new Size(200, 25)
            };

            Label departmentLabel = new Label
            {
                Text = "القسم:",
                Font = new Font("Tahoma", 10),
                Location = new Point(350, 570),
                Size = new Size(100, 25)
            };

            TextBox departmentTextBox = new TextBox
            {
                Font = new Font("Arial", 10),
                Location = new Point(460, 570),
                Size = new Size(200, 25)
            };

            Button uploadDocsBtn = new Button
            {
                Text = "رفع المستندات\nUpload Documents",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(120, 50),
                Location = new Point(20, 620),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button submitBtn = new Button
            {
                Text = "تقديم الطلب\nSubmit Request",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(120, 50),
                Location = new Point(700, 620),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button cancelBtn = new Button
            {
                Text = "إلغاء\nCancel",
                Font = new Font("Tahoma", 12),
                Size = new Size(120, 50),
                Location = new Point(830, 620),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            uploadDocsBtn.Click += (s, e) => ShowDocumentUploadDialog();

            submitBtn.Click += (s, e) =>
            {
                if (string.IsNullOrEmpty(empNameTextBox.Text) || string.IsNullOrEmpty(empIdTextBox.Text))
                {
                    MessageBox.Show("يرجى إدخال بيانات الموظف كاملة", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                MessageBox.Show($"تم تقديم طلب التحويل للجنة العجز بنجاح\n\nالموظف: {empNameTextBox.Text}\nالرقم القومي: {empIdTextBox.Text}\nالمستندات المرفقة: {documentsChecklist.CheckedItems.Count} من {documentsChecklist.Items.Count}",
                    "تم التقديم", MessageBoxButtons.OK, MessageBoxIcon.Information);

                disabilityForm.Close();
            };

            cancelBtn.Click += (s, e) => disabilityForm.Close();

            disabilityForm.Controls.AddRange(new Control[] {
                titleLabel, documentsChecklist, employeeInfoLabel,
                empNameLabel, empNameTextBox, empIdLabel, empIdTextBox,
                jobTitleLabel, jobTitleTextBox, departmentLabel, departmentTextBox,
                uploadDocsBtn, submitBtn, cancelBtn
            });

            disabilityForm.ShowDialog();
        }

        private string FormatDateToArabic(DateTime date)
        {
            return date.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
        }

        // دالة البحث المتقدم الجديدة
        private void LoadAdvancedSearchView()
        {
            ClearContentPanel();

            Label titleLabel = new Label
            {
                Text = "🔍 البحث المتقدم - Advanced Search",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(20, 20),
                Size = new Size(500, 35)
            };

            // شريط البحث الرئيسي
            GroupBox searchGroup = new GroupBox
            {
                Text = "خيارات البحث",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(20, 70),
                Size = new Size(800, 120),
                ForeColor = Color.FromArgb(44, 62, 80)
            };

            TextBox searchBox = new TextBox
            {
                Font = new Font("Arial", 12),
                Location = new Point(20, 30),
                Size = new Size(300, 30),
                PlaceholderText = "ابحث بالاسم، الرقم القومي، المنصب..."
            };

            ComboBox searchTypeCombo = new ComboBox
            {
                Font = new Font("Tahoma", 11),
                Location = new Point(340, 30),
                Size = new Size(150, 30),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            searchTypeCombo.Items.AddRange(new[] { "جميع البيانات", "الموظفين", "الملفات", "التقارير" });
            searchTypeCombo.SelectedIndex = 0;

            Button searchBtn = new Button
            {
                Text = "🔍 بحث",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(100, 30),
                Location = new Point(510, 30),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button clearBtn = new Button
            {
                Text = "🗑️ مسح",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(80, 30),
                Location = new Point(630, 30),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // أزرار الوصول السريع
            Label quickAccessLabel = new Label
            {
                Text = "الوصول السريع:",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Location = new Point(20, 70),
                Size = new Size(100, 25),
                ForeColor = Color.FromArgb(44, 62, 80)
            };

            Button btnEmployeesQuick = new Button
            {
                Text = "👥 الموظفين",
                Font = new Font("Tahoma", 9, FontStyle.Bold),
                Size = new Size(100, 25),
                Location = new Point(130, 70),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button btnFilesQuick = new Button
            {
                Text = "📁 الملفات",
                Font = new Font("Tahoma", 9, FontStyle.Bold),
                Size = new Size(100, 25),
                Location = new Point(240, 70),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button btnReportsQuick = new Button
            {
                Text = "📊 التقارير",
                Font = new Font("Tahoma", 9, FontStyle.Bold),
                Size = new Size(100, 25),
                Location = new Point(350, 70),
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            searchGroup.Controls.AddRange(new Control[] {
                searchBox, searchTypeCombo, searchBtn, clearBtn,
                quickAccessLabel, btnEmployeesQuick, btnFilesQuick, btnReportsQuick
            });

            // منطقة النتائج
            GroupBox resultsGroup = new GroupBox
            {
                Text = "نتائج البحث",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(20, 210),
                Size = new Size(800, 400),
                ForeColor = Color.FromArgb(44, 62, 80)
            };

            ListView resultsListView = new ListView
            {
                Location = new Point(20, 30),
                Size = new Size(760, 350),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Font = new Font("Tahoma", 10)
            };

            resultsListView.Columns.Add("النوع", 80);
            resultsListView.Columns.Add("العنوان", 200);
            resultsListView.Columns.Add("التفاصيل", 300);
            resultsListView.Columns.Add("التاريخ", 120);

            resultsGroup.Controls.Add(resultsListView);

            // ربط الأحداث
            searchBtn.Click += (s, e) => PerformAdvancedSearch(searchBox.Text, searchTypeCombo.SelectedItem?.ToString() ?? "جميع البيانات", resultsListView);
            clearBtn.Click += (s, e) => {
                searchBox.Clear();
                searchTypeCombo.SelectedIndex = 0;
                resultsListView.Items.Clear();
            };

            btnEmployeesQuick.Click += (s, e) => LoadEmployeesView();
            btnFilesQuick.Click += (s, e) => LoadFileManagerView();
            btnReportsQuick.Click += (s, e) => LoadReportsView();

            contentPanel.Controls.AddRange(new Control[] {
                titleLabel, searchGroup, resultsGroup
            });
        }

        // تنفيذ البحث المتقدم
        private void PerformAdvancedSearch(string searchTerm, string searchType, ListView resultsListView)
        {
            try
            {
                resultsListView.Items.Clear();

                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    MessageBox.Show("يرجى إدخال كلمة البحث", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // بحث في الموظفين
                if (searchType == "جميع البيانات" || searchType == "الموظفين")
                {
                    var employees = SearchEmployees(searchTerm);
                    foreach (var emp in employees)
                    {
                        var item = new ListViewItem(new[] {
                            "👥 موظف",
                            emp.Name ?? "",
                            $"المنصب: {emp.JobTitle ?? ""} - القسم: {emp.Department ?? ""}",
                            emp.HireDate?.ToString("yyyy-MM-dd") ?? ""
                        });
                        item.Tag = emp;
                        resultsListView.Items.Add(item);
                    }
                }

                // بحث في الملفات
                if (searchType == "جميع البيانات" || searchType == "الملفات")
                {
                    // إضافة نتائج البحث في الملفات
                    var fileResults = SearchFiles(searchTerm);
                    foreach (var file in fileResults)
                    {
                        var item = new ListViewItem(new[] {
                            "📁 ملف",
                            file,
                            "ملف موظف",
                            DateTime.Now.ToString("yyyy-MM-dd")
                        });
                        resultsListView.Items.Add(item);
                    }
                }

                // بحث في التقارير
                if (searchType == "جميع البيانات" || searchType == "التقارير")
                {
                    // إضافة نتائج البحث في التقارير
                    var reportResults = SearchReports(searchTerm);
                    foreach (var report in reportResults)
                    {
                        var item = new ListViewItem(new[] {
                            "📊 تقرير",
                            report,
                            "تقرير نظام",
                            DateTime.Now.ToString("yyyy-MM-dd")
                        });
                        resultsListView.Items.Add(item);
                    }
                }

                if (resultsListView.Items.Count == 0)
                {
                    var noResultsItem = new ListViewItem(new[] {
                        "ℹ️",
                        "لا توجد نتائج",
                        "لم يتم العثور على نتائج مطابقة لبحثك",
                        ""
                    });
                    resultsListView.Items.Add(noResultsItem);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // بحث في الموظفين
        private List<Employee> SearchEmployees(string searchTerm)
        {
            var results = new List<Employee>();
            try
            {
                var allEmployees = DatabaseHelper.GetAllEmployees();
                foreach (var emp in allEmployees)
                {
                    if (emp.Name?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true ||
                        emp.NationalId?.Contains(searchTerm) == true ||
                        emp.JobTitle?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true ||
                        emp.Department?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) == true)
                    {
                        results.Add(emp);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث في الموظفين: {ex.Message}");
            }
            return results;
        }

        // بحث في الملفات
        private List<string> SearchFiles(string searchTerm)
        {
            var results = new List<string>();
            try
            {
                string employeeFilesPath = "EmployeeFiles";
                if (Directory.Exists(employeeFilesPath))
                {
                    var files = Directory.GetFiles(employeeFilesPath, "*.*", SearchOption.AllDirectories);
                    foreach (var file in files)
                    {
                        if (Path.GetFileName(file).Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                        {
                            results.Add(Path.GetFileName(file));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث في الملفات: {ex.Message}");
            }
            return results;
        }

        // بحث في التقارير
        private List<string> SearchReports(string searchTerm)
        {
            var results = new List<string>();
            try
            {
                string reportsPath = "Reports";
                if (Directory.Exists(reportsPath))
                {
                    var files = Directory.GetFiles(reportsPath, "*.pdf", SearchOption.AllDirectories);
                    foreach (var file in files)
                    {
                        if (Path.GetFileName(file).Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                        {
                            results.Add(Path.GetFileName(file));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث في التقارير: {ex.Message}");
            }
            return results;
        }

        private void LoadEmployeesView()
        {
            ClearContentPanel();

            Label titleLabel = new Label
            {
                Text = "👥 الموظفين - Employees",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(20, 20),
                Size = new Size(400, 30)
            };

            // Search box
            TextBox searchBox = new TextBox
            {
                Font = new Font("Arial", 12),
                Location = new Point(20, 60),
                Size = new Size(300, 25),
                PlaceholderText = "البحث بالاسم أو الرقم القومي..."
            };

            Button searchBtn = new Button
            {
                Text = "🔍 بحث",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(80, 25),
                Location = new Point(330, 60),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // Employees grid
            DataGridView employeesGrid = new DataGridView
            {
                Location = new Point(20, 100),
                Size = new Size(1200, 400),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.Fixed3D
            };

            // Load employees data
            LoadEmployeesData(employeesGrid);

            // Search functionality
            searchBtn.Click += (s, e) => {
                if (string.IsNullOrWhiteSpace(searchBox.Text))
                {
                    LoadEmployeesData(employeesGrid);
                }
                else
                {
                    SearchEmployeesData(employeesGrid, searchBox.Text);
                }
            };

            searchBox.KeyPress += (s, e) => {
                if (e.KeyChar == (char)Keys.Enter)
                {
                    searchBtn.PerformClick();
                }
            };

            Button refreshBtn = new Button
            {
                Text = "🔄 تحديث",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(100, 40),
                Location = new Point(20, 520),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            refreshBtn.Click += (s, e) => LoadEmployeesData(employeesGrid);

            Button backBtn = new Button
            {
                Text = "🔙 رجوع",
                Font = new Font("Tahoma", 10, FontStyle.Bold),
                Size = new Size(100, 40),
                Location = new Point(130, 520),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            backBtn.Click += (s, e) => CreateEmployeeManagementContent(contentPanel);

            contentPanel.Controls.AddRange(new Control[] {
                titleLabel, searchBox, searchBtn, employeesGrid, refreshBtn, backBtn
            });
        }

        private void LoadEmployeesData(DataGridView grid)
        {
            try
            {
                var employees = employeeManager.GetAllEmployees();

                grid.DataSource = employees.Select(emp => new {
                    الرقم = emp.Id,
                    الاسم = emp.Name,
                    الرقم_القومي = emp.NationalId,
                    الرقم_التأميني = emp.InsuranceNumber,
                    المنصب = emp.JobTitle,
                    القسم = emp.Department,
                    نوع_الموظف = emp.EmployeeType,
                    تاريخ_التوظيف = emp.HireDate.ToString("yyyy-MM-dd"),
                    تاريخ_الإضافة = emp.CreatedDate.ToString("yyyy-MM-dd")
                }).ToList();

                // Update title with count
                var titleLabel = contentPanel.Controls.OfType<Label>().FirstOrDefault();
                if (titleLabel != null)
                {
                    titleLabel.Text = $"📋 قائمة الموظفين ({employees.Count} موظف) - Employees List";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SearchEmployeesData(DataGridView grid, string searchTerm)
        {
            try
            {
                var employees = employeeManager.SearchEmployees(searchTerm);

                grid.DataSource = employees.Select(emp => new {
                    الرقم = emp.Id,
                    الاسم = emp.Name,
                    الرقم_القومي = emp.NationalId,
                    الرقم_التأميني = emp.InsuranceNumber,
                    المنصب = emp.JobTitle,
                    القسم = emp.Department,
                    نوع_الموظف = emp.EmployeeType,
                    تاريخ_التوظيف = emp.HireDate.ToString("yyyy-MM-dd"),
                    تاريخ_الإضافة = emp.CreatedDate.ToString("yyyy-MM-dd")
                }).ToList();

                // Update title with search results count
                var titleLabel = contentPanel.Controls.OfType<Label>().FirstOrDefault();
                if (titleLabel != null)
                {
                    titleLabel.Text = $"📋 نتائج البحث ({employees.Count} موظف) - Search Results";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearContentPanel()
        {
            contentPanel.Controls.Clear();
        }

        // إضافة دالة إدارة ملفات الإجازة بدون مرتب
        private void ShowUnpaidLeaveDocumentsDialog()
        {
            Form documentsForm = new Form
            {
                Text = "الملفات المطلوبة للإجازة بدون مرتب - Required Documents",
                Size = new Size(900, 700),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true
            };

            Label titleLabel = new Label
            {
                Text = "الملفات المطلوبة للإجازة بدون مرتب",
                Font = new Font("Tahoma", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(20, 20),
                Size = new Size(850, 35),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // قسم اختيار نوع المستندات
            Label typeLabel = new Label
            {
                Text = "نوع المستندات:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(20, 80),
                Size = new Size(120, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            ComboBox typeCombo = new ComboBox
            {
                Font = new Font("Tahoma", 12),
                Location = new Point(150, 80),
                Size = new Size(300, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                RightToLeft = RightToLeft.Yes
            };
            typeCombo.Items.AddRange(new string[] {
                "قرار منح الإجازة أول مرة",
                "قرارات التجديد",
                "إيصال السداد أو إخطار أقساط للقيمة الحالية الصادر من التأمينات",
                "قطع الإجازة - صورة من استلام العمل أو قرار القطع"
            });

            // منطقة عرض التفاصيل
            Label detailsLabel = new Label
            {
                Text = "تفاصيل المستندات:",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(20, 120),
                Size = new Size(120, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            TextBox detailsTextBox = new TextBox
            {
                Font = new Font("Tahoma", 11),
                Location = new Point(20, 150),
                Size = new Size(850, 200),
                Multiline = true,
                ReadOnly = true,
                RightToLeft = RightToLeft.Yes,
                BackColor = Color.FromArgb(248, 249, 250),
                ScrollBars = ScrollBars.Vertical
            };

            // تحديث التفاصيل عند تغيير النوع
            typeCombo.SelectedIndexChanged += (s, e) =>
            {
                switch (typeCombo.SelectedIndex)
                {
                    case 0: // قرار منح الإجازة أول مرة
                        detailsTextBox.Text = @"📋 قرار منح الإجازة أول مرة:

✅ المستندات المطلوبة:
• صورة من قرار منح الإجازة بدون مرتب
• صورة من البطاقة الشخصية
• صورة من شهادة الميلاد
• طلب الحصول على الإجازة موقع من الموظف
• موافقة الجهة الإدارية المختصة

📝 ملاحظات:
- يجب أن تكون جميع الأوراق مختومة ومعتمدة
- صالحة لمدة 6 أشهر من تاريخ الإصدار
- يجب تقديم الأصل للمطابقة";
                        break;

                    case 1: // قرارات التجديد
                        detailsTextBox.Text = @"🔄 قرارات التجديد:

✅ المستندات المطلوبة:
• قرار تجديد الإجازة بدون مرتب
• إثبات استمرار الحاجة للإجازة
• تقرير طبي (في حالة الإجازة المرضية)
• إثبات الدراسة (في حالة إجازة الدراسة)
• موافقة الجهة الإدارية على التجديد

📝 ملاحظات:
- يجب تقديم طلب التجديد قبل انتهاء الإجازة بشهر
- الحد الأقصى للتجديد حسب اللوائح المعمول بها
- يجب إرفاق المبررات الداعمة للتجديد";
                        break;

                    case 2: // إيصال السداد
                        detailsTextBox.Text = @"💰 إيصال السداد أو إخطار أقساط للقيمة الحالية:

✅ المستندات المطلوبة:
• إيصال سداد الاشتراكات التأمينية
• إخطار أقساط للقيمة الحالية من التأمينات الاجتماعية
• كشف حساب التأمينات الاجتماعية
• إثبات سداد المستحقات المالية

📝 ملاحظات:
- الحد الأقصى 5 سنوات لكل ملف
- يجب أن تكون الإيصالات أصلية ومعتمدة
- يتم احتساب القيمة حسب آخر راتب
- يجب سداد جميع المستحقات قبل العودة للعمل";
                        break;

                    case 3: // قطع الإجازة
                        detailsTextBox.Text = @"✂️ قطع الإجازة - صورة من استلام العمل أو قرار القطع:

✅ المستندات المطلوبة:
• قرار قطع الإجازة بدون مرتب
• صورة من استلام العمل
• إقرار من الموظف بالعودة للعمل
• تسوية الحالة المالية والتأمينية
• موافقة الجهة الإدارية على القطع

📝 ملاحظات:
- يجب إشعار الإدارة قبل العودة بأسبوعين
- تسوية جميع المستحقات المالية
- استكمال الإجراءات الإدارية للعودة
- تحديث البيانات في النظام";
                        break;
                }
            };

            // أزرار رفع الملفات
            Button uploadBtn = new Button
            {
                Text = "رفع الملفات\nUpload Files",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(150, 60),
                Location = new Point(20, 370),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                TextAlign = ContentAlignment.MiddleCenter
            };

            uploadBtn.Click += (s, e) =>
            {
                if (typeCombo.SelectedIndex == -1)
                {
                    MessageBox.Show("يرجى اختيار نوع المستندات أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                using (OpenFileDialog openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Multiselect = true;
                    openFileDialog.Filter = "جميع الملفات المدعومة|*.pdf;*.doc;*.docx;*.jpg;*.jpeg;*.png;*.zip;*.rar|" +
                                           "ملفات PDF|*.pdf|" +
                                           "ملفات Word|*.doc;*.docx|" +
                                           "الصور|*.jpg;*.jpeg;*.png|" +
                                           "ملفات مضغوطة|*.zip;*.rar";

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        try
                        {
                            string category = typeCombo.Text;
                            string description = $"ملفات {category} - تم الرفع في {DateTime.Now:yyyy-MM-dd HH:mm}";

                            // هنا يمكن إضافة كود حفظ الملفات
                            // يمكن ربطها بـ employeeId إذا كان متوفراً

                            MessageBox.Show($"تم رفع {openFileDialog.FileNames.Length} ملف بنجاح\n\nالفئة: {category}",
                                          "تم الرفع", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            documentsForm.Close();
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في رفع الملفات:\n{ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            };

            Button manageFilesBtn = new Button
            {
                Text = "إدارة الملفات\nManage Files",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(150, 60),
                Location = new Point(190, 370),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                TextAlign = ContentAlignment.MiddleCenter
            };

            manageFilesBtn.Click += (s, e) =>
            {
                EnhancedFileManager.ShowFileManagerDialog(0, "إجازة بدون مرتب");
            };

            Button closeBtn = new Button
            {
                Text = "إغلاق\nClose",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(150, 60),
                Location = new Point(360, 370),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                TextAlign = ContentAlignment.MiddleCenter
            };

            closeBtn.Click += (s, e) => documentsForm.Close();

            documentsForm.Controls.AddRange(new Control[] {
                titleLabel, typeLabel, typeCombo, detailsLabel, detailsTextBox,
                uploadBtn, manageFilesBtn, closeBtn
            });

            // تحديد النوع الأول افتراضياً
            typeCombo.SelectedIndex = 0;

            documentsForm.ShowDialog();
        }



        // متغير لتخزين الملفات المؤقتة
        private static List<TemporaryFileInfo> temporaryFiles = new List<TemporaryFileInfo>();

        // دالة ربط الملفات المؤقتة بالموظف الجديد
        private void LinkTemporaryFilesToEmployee(int employeeId, string employeeName)
        {
            try
            {
                var employeeManager = new EmployeeManager();
                int linkedFiles = 0;

                // ربط الملفات المؤقتة بالموظف
                foreach (var tempFile in temporaryFiles.ToList())
                {
                    try
                    {
                        bool success = employeeManager.SaveEmployeeFile(
                            employeeId,
                            tempFile.FileName,
                            tempFile.FilePath,
                            tempFile.FileType,
                            tempFile.FileSize,
                            tempFile.UploadedBy,
                            tempFile.Category,
                            $"ملف مرتبط بالموظف: {employeeName} - {tempFile.Description}"
                        );

                        if (success)
                        {
                            linkedFiles++;
                            temporaryFiles.Remove(tempFile);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"خطأ في ربط الملف {tempFile.FileName}: {ex.Message}");
                    }
                }

                if (linkedFiles > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"تم ربط {linkedFiles} ملف بالموظف {employeeName} (ID: {employeeId})");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في ربط الملفات المؤقتة: {ex.Message}");
            }
        }

        // دالة إضافة ملف مؤقت
        public static void AddTemporaryFile(string fileName, string filePath, string fileType, long fileSize, string category, string description)
        {
            temporaryFiles.Add(new TemporaryFileInfo
            {
                FileName = fileName,
                FilePath = filePath,
                FileType = fileType,
                FileSize = fileSize,
                UploadedBy = "أحمد ابراهيم",
                Category = category,
                Description = description,
                UploadDate = DateTime.Now
            });
        }

        // فئة معلومات الملف المؤقت
        public class TemporaryFileInfo
        {
            public string FileName { get; set; } = "";
            public string FilePath { get; set; } = "";
            public string FileType { get; set; } = "";
            public long FileSize { get; set; }
            public string UploadedBy { get; set; } = "";
            public string Category { get; set; } = "";
            public string Description { get; set; } = "";
            public DateTime UploadDate { get; set; }
        }

        private void ShowDatabaseDiagnosisDialog()
        {
            try
            {
                string diagnosis = employeeManager.DiagnoseDatabaseIssues();

                Form diagnosisForm = new Form
                {
                    Text = "تشخيص قاعدة البيانات - Database Diagnosis",
                    Size = new Size(800, 600),
                    StartPosition = FormStartPosition.CenterParent,
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    MaximizeBox = false,
                    MinimizeBox = false
                };

                Label titleLabel = new Label
                {
                    Text = "تشخيص حالة قاعدة البيانات",
                    Font = new Font("Tahoma", 16, FontStyle.Bold),
                    ForeColor = Color.FromArgb(52, 152, 219),
                    Location = new Point(20, 20),
                    Size = new Size(750, 30),
                    TextAlign = ContentAlignment.MiddleCenter
                };

                TextBox diagnosisTextBox = new TextBox
                {
                    Text = diagnosis,
                    Font = new Font("Consolas", 12),
                    Location = new Point(20, 70),
                    Size = new Size(740, 400),
                    Multiline = true,
                    ReadOnly = true,
                    ScrollBars = ScrollBars.Vertical,
                    BackColor = Color.FromArgb(248, 249, 250),
                    BorderStyle = BorderStyle.FixedSingle
                };

                Button refreshBtn = new Button
                {
                    Text = "تحديث التشخيص\nRefresh Diagnosis",
                    Font = new Font("Tahoma", 12, FontStyle.Bold),
                    Size = new Size(150, 50),
                    Location = new Point(20, 490),
                    BackColor = Color.FromArgb(46, 204, 113),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat
                };

                refreshBtn.Click += (s, e) =>
                {
                    try
                    {
                        diagnosisTextBox.Text = "جاري التشخيص...";
                        diagnosisTextBox.Refresh();
                        diagnosisTextBox.Text = employeeManager.DiagnoseDatabaseIssues();
                    }
                    catch (Exception ex)
                    {
                        diagnosisTextBox.Text = $"❌ خطأ في التشخيص: {ex.Message}";
                    }
                };

                Button testConnectionBtn = new Button
                {
                    Text = "اختبار الاتصال\nTest Connection",
                    Font = new Font("Tahoma", 12, FontStyle.Bold),
                    Size = new Size(150, 50),
                    Location = new Point(190, 490),
                    BackColor = Color.FromArgb(52, 152, 219),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat
                };

                Button initDatabaseBtn = new Button
                {
                    Text = "إنشاء قاعدة البيانات\nInitialize Database",
                    Font = new Font("Tahoma", 12, FontStyle.Bold),
                    Size = new Size(150, 50),
                    Location = new Point(360, 490),
                    BackColor = Color.FromArgb(230, 126, 34),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat
                };

                testConnectionBtn.Click += (s, e) =>
                {
                    try
                    {
                        var dbManager = new DatabaseManager();
                        bool connected = dbManager.TestConnection();
                        MessageBox.Show(connected ?
                            "✅ الاتصال بقاعدة البيانات ناجح!" :
                            "❌ فشل الاتصال بقاعدة البيانات!",
                            "اختبار الاتصال", MessageBoxButtons.OK,
                            connected ? MessageBoxIcon.Information : MessageBoxIcon.Error);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"❌ خطأ في اختبار الاتصال:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };

                initDatabaseBtn.Click += (s, e) =>
                {
                    try
                    {
                        var result = MessageBox.Show("هل تريد إنشاء قاعدة البيانات والجداول؟\n\nسيتم إنشاء:\n- قاعدة البيانات HRManagementDB\n- جدول الموظفين\n- جدول الملفات\n- جدول الإشعارات\n- جدول الملاحظات\n- جدول التلميحات",
                            "إنشاء قاعدة البيانات", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                        if (result == DialogResult.Yes)
                        {
                            diagnosisTextBox.Text = "جاري إنشاء قاعدة البيانات...";
                            diagnosisTextBox.Refresh();

                            bool success = employeeManager.InitializeDatabase();

                            if (success)
                            {
                                MessageBox.Show("✅ تم إنشاء قاعدة البيانات والجداول بنجاح!",
                                    "نجح الإنشاء", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                diagnosisTextBox.Text = employeeManager.DiagnoseDatabaseIssues();
                            }
                            else
                            {
                                MessageBox.Show("❌ فشل في إنشاء قاعدة البيانات!\n\nتأكد من:\n- تشغيل SQL Server LocalDB\n- صلاحيات الكتابة",
                                    "فشل الإنشاء", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"❌ خطأ في إنشاء قاعدة البيانات:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };

                Button closeBtn = new Button
                {
                    Text = "إغلاق\nClose",
                    Font = new Font("Tahoma", 12),
                    Size = new Size(150, 50),
                    Location = new Point(610, 490),
                    BackColor = Color.FromArgb(149, 165, 166),
                    ForeColor = Color.White,
                    FlatStyle = FlatStyle.Flat
                };
                closeBtn.Click += (s, e) => diagnosisForm.Close();

                diagnosisForm.Controls.AddRange(new Control[] {
                    titleLabel, diagnosisTextBox, refreshBtn, testConnectionBtn, initDatabaseBtn, closeBtn
                });

                diagnosisForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في فتح نافذة التشخيص:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // دالة التقارير المحدثة
        private void LoadReportsView()
        {
            ClearContentPanel();

            Label titleLabel = new Label
            {
                Text = "📊 التقارير - Reports",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(230, 126, 34),
                Location = new Point(20, 20),
                Size = new Size(400, 35)
            };

            // مجموعة تقارير الموظفين
            GroupBox employeeReportsGroup = new GroupBox
            {
                Text = "تقارير الموظفين",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(20, 70),
                Size = new Size(380, 200),
                ForeColor = Color.FromArgb(44, 62, 80)
            };

            Button btnEmployeeReport = new Button
            {
                Text = "📄 تقرير موظف فردي",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(340, 35),
                Location = new Point(20, 30),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button btnAllEmployeesReport = new Button
            {
                Text = "📋 تقرير جميع الموظفين",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(340, 35),
                Location = new Point(20, 75),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button btnDepartmentReport = new Button
            {
                Text = "🏢 تقرير حسب القسم",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(340, 35),
                Location = new Point(20, 120),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            employeeReportsGroup.Controls.AddRange(new Control[] {
                btnEmployeeReport, btnAllEmployeesReport, btnDepartmentReport
            });

            // مجموعة التقارير المالية
            GroupBox financialReportsGroup = new GroupBox
            {
                Text = "التقارير المالية",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(420, 70),
                Size = new Size(380, 200),
                ForeColor = Color.FromArgb(44, 62, 80)
            };

            Button btnSalaryReport = new Button
            {
                Text = "💰 تقرير الرواتب",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(340, 35),
                Location = new Point(20, 30),
                BackColor = Color.FromArgb(39, 174, 96),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button btnInsuranceReport = new Button
            {
                Text = "🛡️ تقرير التأمينات",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(340, 35),
                Location = new Point(20, 75),
                BackColor = Color.FromArgb(243, 156, 18),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button btnFinancialSummary = new Button
            {
                Text = "📊 الملخص المالي",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(340, 35),
                Location = new Point(20, 120),
                BackColor = Color.FromArgb(211, 84, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            financialReportsGroup.Controls.AddRange(new Control[] {
                btnSalaryReport, btnInsuranceReport, btnFinancialSummary
            });

            // مجموعة التقارير الإحصائية
            GroupBox statisticsGroup = new GroupBox
            {
                Text = "التقارير الإحصائية",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Location = new Point(20, 290),
                Size = new Size(780, 150),
                ForeColor = Color.FromArgb(44, 62, 80)
            };

            Button btnMonthlyStats = new Button
            {
                Text = "📈 إحصائيات شهرية",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(180, 35),
                Location = new Point(20, 30),
                BackColor = Color.FromArgb(52, 73, 94),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button btnYearlyStats = new Button
            {
                Text = "📊 إحصائيات سنوية",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(180, 35),
                Location = new Point(220, 30),
                BackColor = Color.FromArgb(142, 68, 173),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button btnCustomReport = new Button
            {
                Text = "🔧 تقرير مخصص",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(180, 35),
                Location = new Point(420, 30),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button btnExportData = new Button
            {
                Text = "📤 تصدير البيانات",
                Font = new Font("Tahoma", 11, FontStyle.Bold),
                Size = new Size(180, 35),
                Location = new Point(620, 30),
                BackColor = Color.FromArgb(26, 188, 156),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            statisticsGroup.Controls.AddRange(new Control[] {
                btnMonthlyStats, btnYearlyStats, btnCustomReport, btnExportData
            });

            // ربط الأحداث
            btnAllEmployeesReport.Click += async (s, e) => {
                try
                {
                    string reportPath = await PDFReportGenerator.GenerateAllEmployeesReport();
                    MessageBox.Show($"تم إنشاء التقرير بنجاح!\n\nمسار الملف:\n{reportPath}",
                        "تقرير PDF", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}",
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            btnEmployeeReport.Click += (s, e) => {
                MessageBox.Show("يرجى اختيار موظف من قائمة الموظفين لإنشاء تقرير فردي",
                    "تقرير موظف", MessageBoxButtons.OK, MessageBoxIcon.Information);
                LoadEmployeesView();
            };

            btnCustomReport.Click += (s, e) => LoadAdvancedSearchView();

            contentPanel.Controls.AddRange(new Control[] {
                titleLabel, employeeReportsGroup, financialReportsGroup, statisticsGroup
            });
        }

        // دالة النماذج المحدثة
        private void LoadFormsView()
        {
            ClearContentPanel();

            Label titleLabel = new Label
            {
                Text = "📋 النماذج - Forms",
                Font = new Font("Tahoma", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 152, 219),
                Location = new Point(20, 20),
                Size = new Size(400, 35)
            };

            // أزرار النماذج الرئيسية
            Button btnAddEmployee = new Button
            {
                Text = "👤 إضافة موظف جديد",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(200, 50),
                Location = new Point(20, 80),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button btnEditEmployee = new Button
            {
                Text = "✏️ تعديل بيانات موظف",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(200, 50),
                Location = new Point(240, 80),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            Button btnEmployeeFiles = new Button
            {
                Text = "📁 ملفات الموظفين",
                Font = new Font("Tahoma", 12, FontStyle.Bold),
                Size = new Size(200, 50),
                Location = new Point(460, 80),
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // ربط الأحداث
            btnAddEmployee.Click += (s, e) => {
                var addEmployeeForm = new AddEmployeeForm();
                addEmployeeForm.ShowDialog();
            };

            btnEditEmployee.Click += (s, e) => LoadEmployeesView();
            btnEmployeeFiles.Click += (s, e) => LoadFileManagerView();

            contentPanel.Controls.AddRange(new Control[] {
                titleLabel, btnAddEmployee, btnEditEmployee, btnEmployeeFiles
            });
        }
    }
}