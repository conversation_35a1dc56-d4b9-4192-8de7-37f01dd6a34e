# دليل استكشاف الأخطاء - Troubleshooting Guide
## نظام إدارة الموارد البشرية v5.3.0

### 📅 تاريخ التحديث: ديسمبر 2024
### 🎯 الهدف: حل جميع مشاكل التشغيل

---

## 🚨 **المشاكل الشائعة وحلولها:**

### **1. ❌ "dotnet is not recognized"**

#### **السبب:**
```
.NET 6.0 SDK غير مثبت أو غير موجود في PATH
```

#### **الحل:**
```
1️⃣ حمل وثبت .NET 6.0 SDK من:
   https://dotnet.microsoft.com/download/dotnet/6.0

2️⃣ أعد تشغيل الكمبيوتر

3️⃣ افتح Command Prompt واكتب:
   dotnet --version

4️⃣ يجب أن يظهر: 6.0.x
```

### **2. ❌ "Project file not found"**

#### **السبب:**
```
أنت لست في المجلد الصحيح أو ملف المشروع مفقود
```

#### **الحل:**
```
1️⃣ تأكد من أنك في المجلد:
   E:\HR_Management_System\

2️⃣ تحقق من وجود الملف:
   Ahmedapp for work.csproj

3️⃣ إذا كان مفقود، تحقق من:
   - هل تم حذف الملف؟
   - هل أنت في المجلد الصحيح؟
```

### **3. ❌ "Build failed"**

#### **السبب:**
```
مشاكل في الكود أو المراجع أو الحزم
```

#### **الحل:**
```
1️⃣ نظف المشروع:
   dotnet clean

2️⃣ استعد الحزم:
   dotnet restore

3️⃣ ابني المشروع:
   dotnet build

4️⃣ إذا فشل، راجع رسائل الخطأ
```

### **4. ❌ "Access denied" أو "Permission denied"**

#### **السبب:**
```
صلاحيات غير كافية أو مكافح الفيروسات يمنع التشغيل
```

#### **الحل:**
```
1️⃣ شغل Command Prompt كمدير:
   - اضغط Win + R
   - اكتب: cmd
   - اضغط Ctrl + Shift + Enter

2️⃣ أضف المجلد لاستثناءات مكافح الفيروسات

3️⃣ تأكد من صلاحيات الكتابة في المجلد
```

---

## 🔧 **ملفات التشغيل المتاحة:**

### **📄 جرب هذه الملفات بالترتيب:**

#### **1. DIAGNOSE_AND_RUN.bat ⭐ الأفضل**
```
✅ يفحص جميع المشاكل
✅ يعطي تشخيص مفصل
✅ يحاول إصلاح المشاكل تلقائياً
✅ يعرض رسائل واضحة
```

#### **2. SIMPLE_RUN.bat**
```
✅ يفحص .NET SDK
✅ يحاول التشغيل المباشر
✅ يبني المشروع إذا فشل التشغيل
```

#### **3. DIRECT_RUN.bat**
```
✅ يستخدم مسار مطلق
✅ يفحص ملف المشروع
✅ يستخدم مسار dotnet الكامل
```

#### **4. VISUAL_STUDIO_RUN.bat**
```
✅ يفتح المشروع في Visual Studio
✅ يدعم VS 2019 و 2022
✅ بديل إذا فشل dotnet run
```

---

## 🎯 **خطوات التشغيل المضمونة:**

### **الطريقة الأولى - التشخيص الشامل:**
```
1️⃣ انقر نقراً مزدوجاً على: DIAGNOSE_AND_RUN.bat
2️⃣ اقرأ رسائل التشخيص
3️⃣ اتبع التعليمات المعروضة
4️⃣ إذا نجح، سيفتح التطبيق
```

### **الطريقة الثانية - Visual Studio:**
```
1️⃣ انقر نقراً مزدوجاً على: VISUAL_STUDIO_RUN.bat
2️⃣ سيفتح المشروع في Visual Studio
3️⃣ اضغط F5 أو Ctrl+F5 في Visual Studio
```

### **الطريقة الثالثة - يدوياً:**
```
1️⃣ افتح Command Prompt كمدير
2️⃣ cd "E:\HR_Management_System"
3️⃣ dotnet --version (للتأكد من .NET)
4️⃣ dotnet restore
5️⃣ dotnet build
6️⃣ dotnet run
```

---

## 🔍 **فحص المتطلبات:**

### **تحقق من هذه النقاط:**

#### **1. .NET SDK:**
```cmd
dotnet --version
```
**المطلوب: 6.0.x أو أحدث**

#### **2. ملفات المشروع:**
```
✅ Ahmedapp for work.csproj
✅ Program.cs
✅ Form1.cs
✅ LoginForm.cs
✅ DatabaseHelper.cs
```

#### **3. المجلد الصحيح:**
```
E:\HR_Management_System\
```

#### **4. الصلاحيات:**
```
✅ صلاحيات قراءة/كتابة في المجلد
✅ تشغيل كمدير (إذا لزم الأمر)
✅ استثناءات مكافح الفيروسات
```

---

## 🆘 **إذا لم يعمل أي شيء:**

### **الحل الأخير:**

#### **1. إعادة تثبيت .NET:**
```
1️⃣ احذف .NET SDK الحالي
2️⃣ حمل أحدث إصدار من:
   https://dotnet.microsoft.com/download/dotnet/6.0
3️⃣ ثبت النسخة الجديدة
4️⃣ أعد تشغيل الكمبيوتر
```

#### **2. استخدام Visual Studio:**
```
1️⃣ ثبت Visual Studio Community 2022 (مجاني)
2️⃣ افتح: Ahmedapp for work.sln
3️⃣ اضغط F5 للتشغيل
```

#### **3. فحص شامل:**
```
1️⃣ شغل: DIAGNOSE_AND_RUN.bat
2️⃣ انسخ رسائل الخطأ
3️⃣ ابحث عن الحل في Google
4️⃣ أو اطلب المساعدة مع رسائل الخطأ
```

---

## 📋 **معلومات للدعم التقني:**

### **إذا طلبت المساعدة، أرسل هذه المعلومات:**

#### **1. معلومات النظام:**
```cmd
systeminfo | findstr /B /C:"OS Name" /C:"OS Version"
dotnet --version
dotnet --list-sdks
```

#### **2. محتويات المجلد:**
```cmd
dir "E:\HR_Management_System" /b
```

#### **3. رسائل الخطأ:**
```
انسخ رسائل الخطأ كاملة من:
DIAGNOSE_AND_RUN.bat
```

#### **4. معلومات المشروع:**
```
📁 المجلد: E:\HR_Management_System\
📄 المشروع: Ahmedapp for work.csproj
🔢 الإصدار: v5.3.0
🖥️ النوع: Windows Forms (.NET 6.0)
```

---

## 🎉 **النتائج المتوقعة:**

### **✅ عند نجاح التشغيل:**
```
1️⃣ ستظهر نافذة تسجيل الدخول
2️⃣ استخدم: admin / 123456
3️⃣ ستظهر القائمة الرئيسية مع 4 خانات:
   - 👥 الموظفين
   - 📋 النماذج  
   - 📊 التقارير
   - 🔍 البحث المتقدم
```

### **🚀 الوظائف المتاحة:**
```
✅ إدارة الموظفين
✅ إضافة وتعديل البيانات
✅ إنشاء التقارير
✅ البحث المتقدم
✅ إدارة الملفات
✅ النسخ الاحتياطية
```

**جرب الملفات الجديدة واتبع التعليمات - النظام سيعمل!** 🚀✨

---

### 📋 **ترتيب المحاولة:**
```
1️⃣ DIAGNOSE_AND_RUN.bat     ⭐ ابدأ بهذا
2️⃣ SIMPLE_RUN.bat           🔧 إذا فشل الأول
3️⃣ VISUAL_STUDIO_RUN.bat    🎯 إذا فشل الثاني
4️⃣ التشغيل اليدوي          💻 الحل الأخير
```
