using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace Ahmedapp_for_work
{
    public class EncryptionManager
    {
        private static readonly string keyFile = "encryption.key";
        private static byte[]? encryptionKey;

        // إعدادات التشفير
        public static class EncryptionSettings
        {
            public static bool EncryptSensitiveData { get; set; } = true;
            public static bool EncryptNationalIds { get; set; } = true;
            public static bool EncryptInsuranceNumbers { get; set; } = true;
            public static bool EncryptPhoneNumbers { get; set; } = false;
            public static bool EncryptSalaries { get; set; } = true;
            public static bool EncryptNotes { get; set; } = false;
        }

        // تهيئة نظام التشفير
        public static void InitializeEncryption()
        {
            try
            {
                if (!EncryptionSettings.EncryptSensitiveData)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ تشفير البيانات الحساسة معطل");
                    return;
                }

                // تحميل أو إنشاء مفتاح التشفير
                LoadOrCreateEncryptionKey();

                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة نظام التشفير بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة نظام التشفير: {ex.Message}");
                NotificationManager.SendErrorNotification("خطأ في نظام التشفير", ex.Message, ex);
            }
        }

        // تحميل أو إنشاء مفتاح التشفير
        private static void LoadOrCreateEncryptionKey()
        {
            try
            {
                if (File.Exists(keyFile))
                {
                    // تحميل المفتاح الموجود
                    string encryptedKey = File.ReadAllText(keyFile);
                    encryptionKey = Convert.FromBase64String(encryptedKey);
                    System.Diagnostics.Debug.WriteLine("✅ تم تحميل مفتاح التشفير الموجود");
                }
                else
                {
                    // إنشاء مفتاح جديد
                    using (var aes = Aes.Create())
                    {
                        aes.GenerateKey();
                        encryptionKey = aes.Key;
                    }

                    // حفظ المفتاح
                    string encodedKey = Convert.ToBase64String(encryptionKey);
                    File.WriteAllText(keyFile, encodedKey);

                    // إخفاء ملف المفتاح
                    File.SetAttributes(keyFile, FileAttributes.Hidden);

                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء وحفظ مفتاح تشفير جديد");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل/إنشاء مفتاح التشفير: {ex.Message}");
                throw;
            }
        }

        // تشفير النص
        public static string EncryptText(string plainText)
        {
            if (string.IsNullOrEmpty(plainText) || !EncryptionSettings.EncryptSensitiveData || encryptionKey == null)
            {
                return plainText;
            }

            try
            {
                using (var aes = Aes.Create())
                {
                    aes.Key = encryptionKey;
                    aes.GenerateIV();

                    using (var encryptor = aes.CreateEncryptor())
                    using (var msEncrypt = new MemoryStream())
                    {
                        // كتابة IV في بداية البيانات المشفرة
                        msEncrypt.Write(aes.IV, 0, aes.IV.Length);

                        using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                        using (var swEncrypt = new StreamWriter(csEncrypt))
                        {
                            swEncrypt.Write(plainText);
                        }

                        return Convert.ToBase64String(msEncrypt.ToArray());
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تشفير النص: {ex.Message}");
                return plainText; // إرجاع النص الأصلي في حالة الخطأ
            }
        }

        // فك تشفير النص
        public static string DecryptText(string encryptedText)
        {
            if (string.IsNullOrEmpty(encryptedText) || !EncryptionSettings.EncryptSensitiveData || encryptionKey == null)
            {
                return encryptedText;
            }

            try
            {
                byte[] encryptedData = Convert.FromBase64String(encryptedText);

                using (var aes = Aes.Create())
                {
                    aes.Key = encryptionKey;

                    // استخراج IV من بداية البيانات
                    byte[] iv = new byte[aes.IV.Length];
                    Array.Copy(encryptedData, 0, iv, 0, iv.Length);
                    aes.IV = iv;

                    using (var decryptor = aes.CreateDecryptor())
                    using (var msDecrypt = new MemoryStream(encryptedData, iv.Length, encryptedData.Length - iv.Length))
                    using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    using (var srDecrypt = new StreamReader(csDecrypt))
                    {
                        return srDecrypt.ReadToEnd();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فك تشفير النص: {ex.Message}");
                return encryptedText; // إرجاع النص المشفر في حالة الخطأ
            }
        }

        // تشفير الرقم القومي
        public static string EncryptNationalId(string nationalId)
        {
            if (!EncryptionSettings.EncryptNationalIds)
            {
                return nationalId;
            }
            return EncryptText(nationalId);
        }

        // فك تشفير الرقم القومي
        public static string DecryptNationalId(string encryptedNationalId)
        {
            if (!EncryptionSettings.EncryptNationalIds)
            {
                return encryptedNationalId;
            }
            return DecryptText(encryptedNationalId);
        }

        // تشفير رقم التأمين
        public static string EncryptInsuranceNumber(string insuranceNumber)
        {
            if (!EncryptionSettings.EncryptInsuranceNumbers)
            {
                return insuranceNumber;
            }
            return EncryptText(insuranceNumber);
        }

        // فك تشفير رقم التأمين
        public static string DecryptInsuranceNumber(string encryptedInsuranceNumber)
        {
            if (!EncryptionSettings.EncryptInsuranceNumbers)
            {
                return encryptedInsuranceNumber;
            }
            return DecryptText(encryptedInsuranceNumber);
        }

        // تشفير رقم الهاتف
        public static string EncryptPhoneNumber(string phoneNumber)
        {
            if (!EncryptionSettings.EncryptPhoneNumbers)
            {
                return phoneNumber;
            }
            return EncryptText(phoneNumber);
        }

        // فك تشفير رقم الهاتف
        public static string DecryptPhoneNumber(string encryptedPhoneNumber)
        {
            if (!EncryptionSettings.EncryptPhoneNumbers)
            {
                return encryptedPhoneNumber;
            }
            return DecryptText(encryptedPhoneNumber);
        }

        // تشفير الراتب
        public static string EncryptSalary(decimal? salary)
        {
            if (!salary.HasValue || !EncryptionSettings.EncryptSalaries)
            {
                return salary?.ToString() ?? "";
            }
            return EncryptText(salary.ToString());
        }

        // فك تشفير الراتب
        public static decimal? DecryptSalary(string encryptedSalary)
        {
            if (string.IsNullOrEmpty(encryptedSalary) || !EncryptionSettings.EncryptSalaries)
            {
                return decimal.TryParse(encryptedSalary, out decimal result) ? result : null;
            }

            string decryptedSalary = DecryptText(encryptedSalary);
            return decimal.TryParse(decryptedSalary, out decimal salary) ? salary : null;
        }

        // تشفير الملاحظات
        public static string EncryptNotes(string notes)
        {
            if (!EncryptionSettings.EncryptNotes)
            {
                return notes;
            }
            return EncryptText(notes);
        }

        // فك تشفير الملاحظات
        public static string DecryptNotes(string encryptedNotes)
        {
            if (!EncryptionSettings.EncryptNotes)
            {
                return encryptedNotes;
            }
            return DecryptText(encryptedNotes);
        }

        // تشفير بيانات الموظف الحساسة
        public static Employee EncryptEmployeeSensitiveData(Employee employee)
        {
            if (!EncryptionSettings.EncryptSensitiveData)
            {
                return employee;
            }

            try
            {
                var encryptedEmployee = new Employee
                {
                    // نسخ البيانات غير الحساسة
                    Name = employee.Name,
                    JobTitle = employee.JobTitle,
                    Department = employee.Department,
                    HireDate = employee.HireDate,
                    EmployeeType = employee.EmployeeType,

                    // تشفير البيانات الحساسة
                    NationalId = EncryptNationalId(employee.NationalId ?? ""),
                    InsuranceNumber = EncryptInsuranceNumber(employee.InsuranceNumber ?? ""),
                    ContactInfo = EncryptPhoneNumber(employee.ContactInfo ?? ""),
                    Notes = EncryptNotes(employee.Notes ?? ""),

                    // نسخ باقي البيانات
                    DecisionNumber = employee.DecisionNumber,
                    DecisionDate = employee.DecisionDate,
                    FromToEntity = employee.FromToEntity,
                    WorkDepartment = employee.WorkDepartment,
                    Management = employee.Management,
                    LeaveEndDate = employee.LeaveEndDate,
                    AppointmentType = employee.AppointmentType,

                    // البيانات المالية (مشفرة إذا كان مطلوباً)
                    TotalInsuranceContributions = employee.TotalInsuranceContributions,
                    Rate12Percent = employee.Rate12Percent,
                    Rate9Percent = employee.Rate9Percent,
                    Rate3Percent = employee.Rate3Percent,
                    Rate1Percent_1 = employee.Rate1Percent_1,
                    Rate1Percent_2 = employee.Rate1Percent_2,
                    Rate1Percent_3 = employee.Rate1Percent_3,
                    Rate1Percent_4 = employee.Rate1Percent_4,
                    Rate025Percent = employee.Rate025Percent,

                    CashReplacement = employee.CashReplacement,
                    ConsiderationPeriod = employee.ConsiderationPeriod,
                    LoanInstallment = employee.LoanInstallment,

                    // بيانات إصابة العمل
                    InjuryType = employee.InjuryType,
                    InjuryDate = employee.InjuryDate,

                    // بيانات الإجازة بدون مرتب
                    UnpaidLeaveType = employee.UnpaidLeaveType,
                    LeaveStatus = employee.LeaveStatus,
                    LeaveStartDate = employee.LeaveStartDate,
                    UnpaidLeaveEndDate = employee.UnpaidLeaveEndDate
                };

                return encryptedEmployee;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تشفير بيانات الموظف: {ex.Message}");
                return employee; // إرجاع البيانات الأصلية في حالة الخطأ
            }
        }

        // فك تشفير بيانات الموظف الحساسة
        public static Employee DecryptEmployeeSensitiveData(Employee encryptedEmployee)
        {
            if (!EncryptionSettings.EncryptSensitiveData)
            {
                return encryptedEmployee;
            }

            try
            {
                var decryptedEmployee = new Employee
                {
                    // نسخ البيانات غير الحساسة
                    Name = encryptedEmployee.Name,
                    JobTitle = encryptedEmployee.JobTitle,
                    Department = encryptedEmployee.Department,
                    HireDate = encryptedEmployee.HireDate,
                    EmployeeType = encryptedEmployee.EmployeeType,

                    // فك تشفير البيانات الحساسة
                    NationalId = DecryptNationalId(encryptedEmployee.NationalId ?? ""),
                    InsuranceNumber = DecryptInsuranceNumber(encryptedEmployee.InsuranceNumber ?? ""),
                    ContactInfo = DecryptPhoneNumber(encryptedEmployee.ContactInfo ?? ""),
                    Notes = DecryptNotes(encryptedEmployee.Notes ?? ""),

                    // نسخ باقي البيانات
                    DecisionNumber = encryptedEmployee.DecisionNumber,
                    DecisionDate = encryptedEmployee.DecisionDate,
                    FromToEntity = encryptedEmployee.FromToEntity,
                    WorkDepartment = encryptedEmployee.WorkDepartment,
                    Management = encryptedEmployee.Management,
                    LeaveEndDate = encryptedEmployee.LeaveEndDate,
                    AppointmentType = encryptedEmployee.AppointmentType,

                    // البيانات المالية
                    TotalInsuranceContributions = encryptedEmployee.TotalInsuranceContributions,
                    Rate12Percent = encryptedEmployee.Rate12Percent,
                    Rate9Percent = encryptedEmployee.Rate9Percent,
                    Rate3Percent = encryptedEmployee.Rate3Percent,
                    Rate1Percent_1 = encryptedEmployee.Rate1Percent_1,
                    Rate1Percent_2 = encryptedEmployee.Rate1Percent_2,
                    Rate1Percent_3 = encryptedEmployee.Rate1Percent_3,
                    Rate1Percent_4 = encryptedEmployee.Rate1Percent_4,
                    Rate025Percent = encryptedEmployee.Rate025Percent,

                    CashReplacement = encryptedEmployee.CashReplacement,
                    ConsiderationPeriod = encryptedEmployee.ConsiderationPeriod,
                    LoanInstallment = encryptedEmployee.LoanInstallment,

                    // بيانات إصابة العمل
                    InjuryType = encryptedEmployee.InjuryType,
                    InjuryDate = encryptedEmployee.InjuryDate,

                    // بيانات الإجازة بدون مرتب
                    UnpaidLeaveType = encryptedEmployee.UnpaidLeaveType,
                    LeaveStatus = encryptedEmployee.LeaveStatus,
                    LeaveStartDate = encryptedEmployee.LeaveStartDate,
                    UnpaidLeaveEndDate = encryptedEmployee.UnpaidLeaveEndDate
                };

                return decryptedEmployee;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فك تشفير بيانات الموظف: {ex.Message}");
                return encryptedEmployee; // إرجاع البيانات المشفرة في حالة الخطأ
            }
        }

        // تشفير ملف
        public static bool EncryptFile(string filePath, string encryptedFilePath)
        {
            try
            {
                if (!EncryptionSettings.EncryptSensitiveData || encryptionKey == null)
                {
                    File.Copy(filePath, encryptedFilePath, true);
                    return true;
                }

                using (var aes = Aes.Create())
                {
                    aes.Key = encryptionKey;
                    aes.GenerateIV();

                    using (var fileStream = new FileStream(encryptedFilePath, FileMode.Create))
                    {
                        // كتابة IV في بداية الملف
                        fileStream.Write(aes.IV, 0, aes.IV.Length);

                        using (var encryptor = aes.CreateEncryptor())
                        using (var cryptoStream = new CryptoStream(fileStream, encryptor, CryptoStreamMode.Write))
                        using (var inputStream = new FileStream(filePath, FileMode.Open))
                        {
                            inputStream.CopyTo(cryptoStream);
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تشفير الملف: {Path.GetFileName(filePath)}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تشفير الملف: {ex.Message}");
                return false;
            }
        }

        // فك تشفير ملف
        public static bool DecryptFile(string encryptedFilePath, string decryptedFilePath)
        {
            try
            {
                if (!EncryptionSettings.EncryptSensitiveData || encryptionKey == null)
                {
                    File.Copy(encryptedFilePath, decryptedFilePath, true);
                    return true;
                }

                using (var aes = Aes.Create())
                {
                    aes.Key = encryptionKey;

                    using (var fileStream = new FileStream(encryptedFilePath, FileMode.Open))
                    {
                        // قراءة IV من بداية الملف
                        byte[] iv = new byte[aes.IV.Length];
                        fileStream.Read(iv, 0, iv.Length);
                        aes.IV = iv;

                        using (var decryptor = aes.CreateDecryptor())
                        using (var cryptoStream = new CryptoStream(fileStream, decryptor, CryptoStreamMode.Read))
                        using (var outputStream = new FileStream(decryptedFilePath, FileMode.Create))
                        {
                            cryptoStream.CopyTo(outputStream);
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم فك تشفير الملف: {Path.GetFileName(encryptedFilePath)}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في فك تشفير الملف: {ex.Message}");
                return false;
            }
        }

        // إنشاء نسخة احتياطية من مفتاح التشفير
        public static bool BackupEncryptionKey(string backupPath)
        {
            try
            {
                if (!File.Exists(keyFile))
                {
                    return false;
                }

                File.Copy(keyFile, Path.Combine(backupPath, "encryption_key_backup.key"), true);
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء نسخة احتياطية من مفتاح التشفير");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ مفتاح التشفير: {ex.Message}");
                return false;
            }
        }

        // استعادة مفتاح التشفير
        public static bool RestoreEncryptionKey(string backupKeyPath)
        {
            try
            {
                if (!File.Exists(backupKeyPath))
                {
                    return false;
                }

                File.Copy(backupKeyPath, keyFile, true);
                File.SetAttributes(keyFile, FileAttributes.Hidden);
                
                // إعادة تحميل المفتاح
                LoadOrCreateEncryptionKey();
                
                System.Diagnostics.Debug.WriteLine("✅ تم استعادة مفتاح التشفير");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في استعادة مفتاح التشفير: {ex.Message}");
                return false;
            }
        }

        // التحقق من سلامة التشفير
        public static bool VerifyEncryption()
        {
            try
            {
                string testText = "اختبار التشفير - Test Encryption";
                string encrypted = EncryptText(testText);
                string decrypted = DecryptText(encrypted);

                bool isValid = testText.Equals(decrypted);
                
                if (isValid)
                {
                    System.Diagnostics.Debug.WriteLine("✅ نظام التشفير يعمل بشكل صحيح");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ خطأ في نظام التشفير");
                }

                return isValid;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من التشفير: {ex.Message}");
                return false;
            }
        }
    }
}
