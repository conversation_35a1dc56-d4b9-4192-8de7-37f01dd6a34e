using System;
using System.Data.OleDb;
using System.IO;
using System.Data;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    public class AccessDatabaseManager
    {
        private readonly string dbFileName = "HRManagement.accdb";
        private readonly string connectionString;

        public AccessDatabaseManager()
        {
            connectionString = GetOptimalConnectionString();
        }

        // الحصول على أفضل connection string حسب الإصدار المتاح
        private string GetOptimalConnectionString()
        {
            // قائمة بـ connection strings مرتبة حسب الأولوية
            var connectionStrings = new[]
            {
                // Office 2016/2019/365 (ACE 16.0)
                $"Provider=Microsoft.ACE.OLEDB.16.0;Data Source={dbFileName};Persist Security Info=False;",

                // Office 2013 (ACE 15.0)
                $"Provider=Microsoft.ACE.OLEDB.15.0;Data Source={dbFileName};Persist Security Info=False;",

                // Office 2010 (ACE 14.0)
                $"Provider=Microsoft.ACE.OLEDB.14.0;Data Source={dbFileName};Persist Security Info=False;",

                // Office 2007 (ACE 12.0) - الإصدار المطلوب
                $"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={dbFileName};Persist Security Info=False;",

                // Jet Engine للملفات القديمة (إذا كان .mdb)
                $"Provider=Microsoft.Jet.OLEDB.4.0;Data Source={dbFileName.Replace(".accdb", ".mdb")};Persist Security Info=False;"
            };

            // اختبار كل connection string
            foreach (var connStr in connectionStrings)
            {
                if (TestConnectionString(connStr))
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم اختيار Connection String: {GetProviderName(connStr)}");
                    return connStr;
                }
            }

            // إذا فشل كل شيء، استخدم الافتراضي
            System.Diagnostics.Debug.WriteLine("⚠️ استخدام Connection String الافتراضي");
            return $"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={dbFileName};Persist Security Info=False;";
        }

        // اختبار connection string
        private bool TestConnectionString(string connStr)
        {
            try
            {
                using (var connection = new OleDbConnection(connStr))
                {
                    connection.Open();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        // استخراج اسم Provider
        private string GetProviderName(string connectionString)
        {
            try
            {
                var parts = connectionString.Split(';');
                foreach (var part in parts)
                {
                    if (part.Trim().StartsWith("Provider=", StringComparison.OrdinalIgnoreCase))
                    {
                        return part.Split('=')[1].Trim();
                    }
                }
                return "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        // إنشاء قاعدة البيانات والجداول
        public bool InitializeAccessDatabase()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 بدء إنشاء قاعدة بيانات Access...");

                // 1. إنشاء قاعدة البيانات إذا لم تكن موجودة
                if (!File.Exists(dbFileName))
                {
                    CreateAccessDatabase(dbFileName);
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قاعدة البيانات Access.");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ قاعدة البيانات Access موجودة بالفعل.");
                }

                // 2. إنشاء الجداول
                CreateTables();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قاعدة بيانات Access بنجاح.");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء قاعدة بيانات Access: {ex.Message}");
                return false;
            }
        }

        // إنشاء قاعدة بيانات .accdb فارغة
        private void CreateAccessDatabase(string fileName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔧 محاولة إنشاء قاعدة البيانات: {fileName}");

                // قائمة بـ connection strings للإنشاء
                var createConnectionStrings = new[]
                {
                    $"Provider=Microsoft.ACE.OLEDB.16.0;Data Source={fileName};",
                    $"Provider=Microsoft.ACE.OLEDB.15.0;Data Source={fileName};",
                    $"Provider=Microsoft.ACE.OLEDB.14.0;Data Source={fileName};",
                    $"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={fileName};Persist Security Info=False;",
                    $"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={fileName};"
                };

                bool created = false;
                Exception lastException = null;

                foreach (var connStr in createConnectionStrings)
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"🔧 محاولة إنشاء بـ: {GetProviderName(connStr)}");

                        var catalog = new ADOX.Catalog();
                        catalog.Create(connStr);

                        System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء قاعدة البيانات بنجاح بـ: {GetProviderName(connStr)}");
                        created = true;
                        break;
                    }
                    catch (Exception ex)
                    {
                        lastException = ex;
                        System.Diagnostics.Debug.WriteLine($"❌ فشل الإنشاء بـ {GetProviderName(connStr)}: {ex.Message}");
                        continue;
                    }
                }

                if (!created)
                {
                    // محاولة أخيرة بطريقة بديلة
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("🔧 محاولة إنشاء قاعدة البيانات بطريقة بديلة...");
                        CreateDatabaseAlternativeMethod(fileName);
                        System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قاعدة البيانات بالطريقة البديلة");
                    }
                    catch (Exception altEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ فشلت الطريقة البديلة: {altEx.Message}");
                        throw new Exception($"فشل في إنشاء قاعدة البيانات بجميع الطرق المتاحة. آخر خطأ: {lastException?.Message}", lastException);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام في إنشاء قاعدة البيانات: {ex.Message}");
                throw;
            }
        }

        // طريقة بديلة لإنشاء قاعدة البيانات
        private void CreateDatabaseAlternativeMethod(string fileName)
        {
            try
            {
                // إنشاء ملف فارغ أولاً
                if (File.Exists(fileName))
                {
                    File.Delete(fileName);
                }

                // استخدام OleDbConnection لإنشاء قاعدة البيانات
                var connectionStrings = new[]
                {
                    $"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={fileName};Persist Security Info=False;",
                    $"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={fileName};"
                };

                foreach (var connStr in connectionStrings)
                {
                    try
                    {
                        // إنشاء ملف قاعدة بيانات فارغ
                        File.WriteAllBytes(fileName, GetEmptyAccessDatabaseBytes());

                        // اختبار الاتصال
                        using (var connection = new OleDbConnection(connStr))
                        {
                            connection.Open();
                            System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قاعدة البيانات بالطريقة البديلة");
                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ فشل في الطريقة البديلة: {ex.Message}");
                        if (File.Exists(fileName))
                        {
                            File.Delete(fileName);
                        }
                        continue;
                    }
                }

                throw new Exception("فشل في إنشاء قاعدة البيانات بالطريقة البديلة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الطريقة البديلة: {ex.Message}");
                throw;
            }
        }

        // الحصول على bytes لملف Access فارغ
        private byte[] GetEmptyAccessDatabaseBytes()
        {
            // هذا header أساسي لملف Access فارغ
            // في التطبيق الحقيقي، يفضل استخدام template جاهز
            return new byte[]
            {
                0x00, 0x01, 0x00, 0x00, 0x53, 0x74, 0x61, 0x6E, 0x64, 0x61, 0x72, 0x64, 0x20, 0x4A, 0x65, 0x74,
                0x20, 0x44, 0x42, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
            };
        }

        // إنشاء الجداول المطلوبة
        private void CreateTables()
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                connection.Open();

                // إنشاء جدول الموظفين المحسن
                string createEmployeesTableSql = @"
                    CREATE TABLE Employees (
                        Id AUTOINCREMENT PRIMARY KEY,
                        FullName TEXT(100) NOT NULL,
                        NationalId TEXT(14),
                        InsuranceNumber TEXT(50),
                        JobTitle TEXT(100),
                        Department TEXT(100),
                        HireDate DATETIME,
                        EmployeeType TEXT(50),
                        Phone TEXT(20),
                        Email TEXT(100),
                        Address TEXT(255),
                        Salary CURRENCY,

                        -- حقول الانتداب
                        DecisionNumber TEXT(100),
                        DecisionDate DATETIME,
                        FromToEntity TEXT(500),
                        WorkDepartment TEXT(100),
                        Management TEXT(100),
                        LeaveEndDate DATETIME,
                        AppointmentType TEXT(100),

                        -- الاشتراكات التأمينية
                        TotalInsuranceContributions CURRENCY,
                        Rate12Percent CURRENCY,
                        Rate9Percent CURRENCY,
                        Rate3Percent CURRENCY,
                        Rate1Percent_1 CURRENCY,
                        Rate1Percent_2 CURRENCY,
                        Rate1Percent_3 CURRENCY,
                        Rate1Percent_4 CURRENCY,
                        Rate025Percent CURRENCY,

                        -- الحقول الإضافية
                        CashReplacement CURRENCY,
                        ConsiderationPeriod INTEGER,
                        LoanInstallment CURRENCY,

                        -- حقول إصابة العمل
                        InjuryType TEXT(100),
                        InjuryDate DATETIME,

                        -- حقول الإجازة بدون مرتب
                        UnpaidLeaveType TEXT(100),
                        LeaveStatus TEXT(50),
                        LeaveStartDate DATETIME,
                        UnpaidLeaveEndDate DATETIME,

                        -- بيانات النظام
                        CreatedBy TEXT(100),
                        CreatedDate DATETIME,
                        Notes MEMO
                    )";

                CreateTableIfNotExists(connection, "Employees", createEmployeesTableSql);

                // إنشاء جدول ملفات الموظفين
                string createEmployeeFilesTableSql = @"
                    CREATE TABLE EmployeeFiles (
                        Id AUTOINCREMENT PRIMARY KEY,
                        EmployeeId INTEGER NOT NULL,
                        FileName TEXT(255) NOT NULL,
                        FilePath TEXT(500) NOT NULL,
                        FileType TEXT(50),
                        FileSize LONG,
                        Category TEXT(100),
                        Description MEMO,
                        UploadedBy TEXT(100),
                        UploadDate DATETIME
                    )";

                CreateTableIfNotExists(connection, "EmployeeFiles", createEmployeeFilesTableSql);

                // إنشاء جدول الإشعارات
                string createNotificationsTableSql = @"
                    CREATE TABLE Notifications (
                        NotificationID AUTOINCREMENT PRIMARY KEY,
                        Title TEXT(255) NOT NULL,
                        Message MEMO NOT NULL,
                        NotificationType TEXT(50),
                        Priority TEXT(20),
                        TargetUsers TEXT(255),
                        IsActive YESNO,
                        ExpiryDate DATETIME,
                        CreatedBy TEXT(100),
                        CreatedDate DATETIME
                    )";

                CreateTableIfNotExists(connection, "Notifications", createNotificationsTableSql);

                // إنشاء جدول الملاحظات
                string createNotesTableSql = @"
                    CREATE TABLE Notes (
                        NoteID AUTOINCREMENT PRIMARY KEY,
                        Title TEXT(255) NOT NULL,
                        Content MEMO NOT NULL,
                        Category TEXT(100),
                        RelatedEntityType TEXT(50),
                        RelatedEntityID INTEGER,
                        IsPrivate YESNO,
                        CreatedBy TEXT(100),
                        CreatedDate DATETIME,
                        Tags TEXT(500)
                    )";

                CreateTableIfNotExists(connection, "Notes", createNotesTableSql);

                // إنشاء جدول التلميحات
                string createTipsTableSql = @"
                    CREATE TABLE Tips (
                        TipID AUTOINCREMENT PRIMARY KEY,
                        Title TEXT(255) NOT NULL,
                        Content MEMO NOT NULL,
                        Category TEXT(100),
                        TipType TEXT(50),
                        IsActive YESNO,
                        DisplayOrder INTEGER,
                        CreatedBy TEXT(100),
                        CreatedDate DATETIME
                    )";

                CreateTableIfNotExists(connection, "Tips", createTipsTableSql);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء جميع الجداول في قاعدة بيانات Access.");
            }
        }

        // دالة مساعدة لإنشاء الجدول إذا لم يكن موجوداً
        private void CreateTableIfNotExists(OleDbConnection connection, string tableName, string createTableSql)
        {
            try
            {
                using (var command = new OleDbCommand(createTableSql, connection))
                {
                    command.ExecuteNonQuery();
                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء جدول {tableName} بنجاح.");
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("already exists") || ex.Message.Contains("موجود"))
                {
                    System.Diagnostics.Debug.WriteLine($"ℹ️ جدول {tableName} موجود بالفعل.");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ أثناء إنشاء جدول {tableName}: {ex.Message}");
                }
            }
        }

        // اختبار الاتصال بقاعدة البيانات
        public bool TestConnection()
        {
            try
            {
                using (var connection = new OleDbConnection(connectionString))
                {
                    connection.Open();
                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل الاتصال بقاعدة بيانات Access: {ex.Message}");
                return false;
            }
        }

        // إضافة موظف جديد
        public int AddEmployee(Employee employee)
        {
            try
            {
                using (var connection = new OleDbConnection(connectionString))
                {
                    connection.Open();

                    string insertSql = @"
                        INSERT INTO Employees (
                            FullName, NationalId, InsuranceNumber, JobTitle, Department, HireDate, EmployeeType,
                            Phone, Email, Address, Salary, DecisionNumber, DecisionDate, FromToEntity,
                            WorkDepartment, Management, LeaveEndDate, AppointmentType,
                            TotalInsuranceContributions, Rate12Percent, Rate9Percent, Rate3Percent,
                            Rate1Percent_1, Rate1Percent_2, Rate1Percent_3, Rate1Percent_4, Rate025Percent,
                            CashReplacement, ConsiderationPeriod, LoanInstallment,
                            InjuryType, InjuryDate, UnpaidLeaveType, LeaveStatus, LeaveStartDate, UnpaidLeaveEndDate,
                            CreatedBy, CreatedDate, Notes
                        ) VALUES (
                            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                        )";

                    using (var command = new OleDbCommand(insertSql, connection))
                    {
                        // إضافة المعاملات
                        command.Parameters.AddWithValue("@FullName", employee.Name ?? "");
                        command.Parameters.AddWithValue("@NationalId", employee.NationalId ?? "");
                        command.Parameters.AddWithValue("@InsuranceNumber", employee.InsuranceNumber ?? "");
                        command.Parameters.AddWithValue("@JobTitle", employee.JobTitle ?? "");
                        command.Parameters.AddWithValue("@Department", employee.Department ?? "");
                        command.Parameters.AddWithValue("@HireDate", employee.HireDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@EmployeeType", employee.EmployeeType ?? "");
                        command.Parameters.AddWithValue("@Phone", employee.ContactInfo ?? "");
                        command.Parameters.AddWithValue("@Email", "");
                        command.Parameters.AddWithValue("@Address", "");
                        command.Parameters.AddWithValue("@Salary", 0);
                        command.Parameters.AddWithValue("@DecisionNumber", employee.DecisionNumber ?? "");
                        command.Parameters.AddWithValue("@DecisionDate", employee.DecisionDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@FromToEntity", employee.FromToEntity ?? "");
                        command.Parameters.AddWithValue("@WorkDepartment", employee.WorkDepartment ?? "");
                        command.Parameters.AddWithValue("@Management", employee.Management ?? "");
                        command.Parameters.AddWithValue("@LeaveEndDate", employee.LeaveEndDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@AppointmentType", employee.AppointmentType ?? "");
                        command.Parameters.AddWithValue("@TotalInsuranceContributions", employee.TotalInsuranceContributions ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate12Percent", employee.Rate12Percent ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate9Percent", employee.Rate9Percent ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate3Percent", employee.Rate3Percent ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate1Percent_1", employee.Rate1Percent_1 ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate1Percent_2", employee.Rate1Percent_2 ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate1Percent_3", employee.Rate1Percent_3 ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate1Percent_4", employee.Rate1Percent_4 ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate025Percent", employee.Rate025Percent ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@CashReplacement", employee.CashReplacement ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@ConsiderationPeriod", employee.ConsiderationPeriod ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@LoanInstallment", employee.LoanInstallment ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@InjuryType", employee.InjuryType ?? "");
                        command.Parameters.AddWithValue("@InjuryDate", employee.InjuryDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@UnpaidLeaveType", employee.UnpaidLeaveType ?? "");
                        command.Parameters.AddWithValue("@LeaveStatus", employee.LeaveStatus ?? "");
                        command.Parameters.AddWithValue("@LeaveStartDate", employee.LeaveStartDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@UnpaidLeaveEndDate", employee.UnpaidLeaveEndDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@CreatedBy", "النظام");
                        command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                        command.Parameters.AddWithValue("@Notes", employee.Notes ?? "");

                        command.ExecuteNonQuery();

                        // الحصول على ID الموظف الجديد
                        using (var idCommand = new OleDbCommand("SELECT @@IDENTITY", connection))
                        {
                            var result = idCommand.ExecuteScalar();
                            return Convert.ToInt32(result);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة الموظف إلى Access: {ex.Message}");
                throw;
            }
        }

        // جلب جميع الموظفين
        public DataTable GetAllEmployees()
        {
            try
            {
                using (var connection = new OleDbConnection(connectionString))
                {
                    connection.Open();

                    string selectSql = "SELECT * FROM Employees ORDER BY FullName";
                    using (var adapter = new OleDbDataAdapter(selectSql, connection))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب الموظفين من Access: {ex.Message}");
                return new DataTable();
            }
        }

        // تشخيص قاعدة البيانات
        public string DiagnoseDatabase()
        {
            var diagnosis = new System.Text.StringBuilder();

            try
            {
                diagnosis.AppendLine("🔍 تشخيص شامل لقاعدة بيانات Access:");
                diagnosis.AppendLine("=" + new string('=', 50));

                // معلومات الملف
                diagnosis.AppendLine($"\n📁 معلومات الملف:");
                diagnosis.AppendLine($"   اسم الملف: {dbFileName}");
                diagnosis.AppendLine($"   المسار الكامل: {Path.GetFullPath(dbFileName)}");

                if (File.Exists(dbFileName))
                {
                    var fileInfo = new FileInfo(dbFileName);
                    diagnosis.AppendLine($"   ✅ الملف موجود");
                    diagnosis.AppendLine($"   📏 الحجم: {fileInfo.Length / 1024:N0} KB ({fileInfo.Length:N0} bytes)");
                    diagnosis.AppendLine($"   📅 تاريخ الإنشاء: {fileInfo.CreationTime:yyyy-MM-dd HH:mm:ss}");
                    diagnosis.AppendLine($"   📅 آخر تعديل: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}");
                    diagnosis.AppendLine($"   🔒 صلاحيات القراءة: {(fileInfo.IsReadOnly ? "للقراءة فقط" : "قراءة وكتابة")}");
                }
                else
                {
                    diagnosis.AppendLine($"   ❌ الملف غير موجود");
                }

                // تشخيص إصدارات Office المتاحة
                diagnosis.AppendLine($"\n🏢 تشخيص إصدارات Microsoft Office:");
                DiagnoseOfficeVersions(diagnosis);

                // تشخيص Connection String
                diagnosis.AppendLine($"\n🔗 تشخيص الاتصال:");
                diagnosis.AppendLine($"   Connection String المستخدم: {GetProviderName(connectionString)}");
                diagnosis.AppendLine($"   التفاصيل الكاملة: {connectionString}");

                // اختبار الاتصال
                if (TestConnection())
                {
                    diagnosis.AppendLine($"   ✅ الاتصال بقاعدة البيانات ناجح");

                    // فحص الجداول
                    using (var connection = new OleDbConnection(connectionString))
                    {
                        connection.Open();

                        // معلومات قاعدة البيانات
                        diagnosis.AppendLine($"\n📊 معلومات قاعدة البيانات:");
                        try
                        {
                            var dbInfo = connection.GetSchema("DataSourceInformation");
                            if (dbInfo.Rows.Count > 0)
                            {
                                var row = dbInfo.Rows[0];
                                diagnosis.AppendLine($"   📋 اسم المنتج: {row["DataSourceProductName"]}");
                                diagnosis.AppendLine($"   🔢 إصدار المنتج: {row["DataSourceProductVersion"]}");
                            }
                        }
                        catch (Exception ex)
                        {
                            diagnosis.AppendLine($"   ⚠️ لا يمكن الحصول على معلومات قاعدة البيانات: {ex.Message}");
                        }

                        // فحص الجداول
                        var tables = connection.GetSchema("Tables");
                        var userTables = new List<DataRow>();

                        foreach (DataRow row in tables.Rows)
                        {
                            string tableName = row["TABLE_NAME"].ToString();
                            string tableType = row["TABLE_TYPE"].ToString();

                            if (!tableName.StartsWith("MSys") && tableType == "TABLE")
                            {
                                userTables.Add(row);
                            }
                        }

                        diagnosis.AppendLine($"\n📋 الجداول ({userTables.Count} جدول):");

                        if (userTables.Count == 0)
                        {
                            diagnosis.AppendLine($"   ⚠️ لا توجد جداول مستخدم في قاعدة البيانات");
                        }
                        else
                        {
                            foreach (var row in userTables)
                            {
                                string tableName = row["TABLE_NAME"].ToString();
                                try
                                {
                                    using (var countCommand = new OleDbCommand($"SELECT COUNT(*) FROM [{tableName}]", connection))
                                    {
                                        int count = Convert.ToInt32(countCommand.ExecuteScalar());
                                        diagnosis.AppendLine($"   ✅ {tableName}: {count:N0} سجل");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    diagnosis.AppendLine($"   ❌ {tableName}: خطأ - {ex.Message}");
                                }
                            }
                        }

                        // إحصائيات إضافية
                        diagnosis.AppendLine($"\n📈 إحصائيات إضافية:");
                        try
                        {
                            int totalRecords = 0;
                            foreach (var row in userTables)
                            {
                                string tableName = row["TABLE_NAME"].ToString();
                                try
                                {
                                    using (var countCommand = new OleDbCommand($"SELECT COUNT(*) FROM [{tableName}]", connection))
                                    {
                                        totalRecords += Convert.ToInt32(countCommand.ExecuteScalar());
                                    }
                                }
                                catch { }
                            }
                            diagnosis.AppendLine($"   📊 إجمالي السجلات: {totalRecords:N0}");
                        }
                        catch (Exception ex)
                        {
                            diagnosis.AppendLine($"   ⚠️ لا يمكن حساب إجمالي السجلات: {ex.Message}");
                        }
                    }
                }
                else
                {
                    diagnosis.AppendLine($"   ❌ فشل الاتصال بقاعدة البيانات");

                    // محاولة تشخيص سبب فشل الاتصال
                    diagnosis.AppendLine($"\n🔧 تشخيص أسباب فشل الاتصال:");
                    DiagnoseConnectionFailure(diagnosis);
                }

                // توصيات
                diagnosis.AppendLine($"\n💡 التوصيات:");
                AddRecommendations(diagnosis);

            }
            catch (Exception ex)
            {
                diagnosis.AppendLine($"\n❌ خطأ عام في التشخيص: {ex.Message}");
                diagnosis.AppendLine($"   تفاصيل الخطأ: {ex.StackTrace}");
            }

            diagnosis.AppendLine($"\n⏰ تم التشخيص في: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            return diagnosis.ToString();
        }

        // تشخيص إصدارات Office المتاحة
        private void DiagnoseOfficeVersions(System.Text.StringBuilder diagnosis)
        {
            var providers = new[]
            {
                ("Microsoft.ACE.OLEDB.16.0", "Office 2016/2019/365"),
                ("Microsoft.ACE.OLEDB.15.0", "Office 2013"),
                ("Microsoft.ACE.OLEDB.14.0", "Office 2010"),
                ("Microsoft.ACE.OLEDB.12.0", "Office 2007"),
                ("Microsoft.Jet.OLEDB.4.0", "Jet Engine (Access 97-2003)")
            };

            foreach (var (provider, version) in providers)
            {
                try
                {
                    using (var connection = new OleDbConnection($"Provider={provider};Data Source=test.accdb;"))
                    {
                        // لا نحتاج فتح الاتصال، فقط إنشاء الكائن
                        diagnosis.AppendLine($"   ✅ {version} ({provider}) - متاح");
                    }
                }
                catch
                {
                    diagnosis.AppendLine($"   ❌ {version} ({provider}) - غير متاح");
                }
            }
        }

        // تشخيص أسباب فشل الاتصال
        private void DiagnoseConnectionFailure(System.Text.StringBuilder diagnosis)
        {
            // فحص وجود الملف
            if (!File.Exists(dbFileName))
            {
                diagnosis.AppendLine($"   🔍 الملف غير موجود - سيتم إنشاؤه تلقائياً");
                return;
            }

            // فحص صلاحيات الملف
            try
            {
                var fileInfo = new FileInfo(dbFileName);
                if (fileInfo.IsReadOnly)
                {
                    diagnosis.AppendLine($"   ⚠️ الملف للقراءة فقط - قد يسبب مشاكل في الكتابة");
                }
            }
            catch (Exception ex)
            {
                diagnosis.AppendLine($"   ❌ لا يمكن فحص صلاحيات الملف: {ex.Message}");
            }

            // اختبار providers مختلفة
            diagnosis.AppendLine($"   🔧 اختبار providers مختلفة:");
            var testProviders = new[]
            {
                "Microsoft.ACE.OLEDB.16.0",
                "Microsoft.ACE.OLEDB.15.0",
                "Microsoft.ACE.OLEDB.14.0",
                "Microsoft.ACE.OLEDB.12.0"
            };

            foreach (var provider in testProviders)
            {
                try
                {
                    using (var connection = new OleDbConnection($"Provider={provider};Data Source={dbFileName};"))
                    {
                        connection.Open();
                        diagnosis.AppendLine($"      ✅ {provider} - يعمل");
                        return; // إذا نجح أي provider، توقف
                    }
                }
                catch (Exception ex)
                {
                    diagnosis.AppendLine($"      ❌ {provider} - فشل: {ex.Message}");
                }
            }
        }

        // إضافة التوصيات
        private void AddRecommendations(System.Text.StringBuilder diagnosis)
        {
            if (!File.Exists(dbFileName))
            {
                diagnosis.AppendLine($"   📝 قم بتشغيل النظام لإنشاء قاعدة البيانات تلقائياً");
            }

            diagnosis.AppendLine($"   📝 تأكد من تثبيت Microsoft Access Database Engine");
            diagnosis.AppendLine($"   📝 يمكن تحميله من موقع Microsoft الرسمي");
            diagnosis.AppendLine($"   📝 استخدم الإصدار المناسب (32-bit أو 64-bit)");
            diagnosis.AppendLine($"   📝 قم بعمل نسخة احتياطية دورية من قاعدة البيانات");
        }
    }
}
