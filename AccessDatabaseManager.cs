using System;
using System.Data.OleDb;
using System.IO;
using System.Data;
using System.Windows.Forms;

namespace Ahmedapp_for_work
{
    public class AccessDatabaseManager
    {
        private readonly string dbFileName = "HRManagement.accdb";
        private readonly string connectionString;

        public AccessDatabaseManager()
        {
            connectionString = $"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={dbFileName};";
        }

        // إنشاء قاعدة البيانات والجداول
        public bool InitializeAccessDatabase()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 بدء إنشاء قاعدة بيانات Access...");

                // 1. إنشاء قاعدة البيانات إذا لم تكن موجودة
                if (!File.Exists(dbFileName))
                {
                    CreateAccessDatabase(dbFileName);
                    System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قاعدة البيانات Access.");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("ℹ️ قاعدة البيانات Access موجودة بالفعل.");
                }

                // 2. إنشاء الجداول
                CreateTables();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء قاعدة بيانات Access بنجاح.");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء قاعدة بيانات Access: {ex.Message}");
                return false;
            }
        }

        // إنشاء قاعدة بيانات .accdb فارغة
        private void CreateAccessDatabase(string fileName)
        {
            try
            {
                // استخدام ADOX لإنشاء قاعدة البيانات
                var catalog = new ADOX.Catalog();
                catalog.Create($"Provider=Microsoft.ACE.OLEDB.12.0;Data Source={fileName};");
                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء ملف قاعدة البيانات: {fileName}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء ملف قاعدة البيانات: {ex.Message}");
                throw;
            }
        }

        // إنشاء الجداول المطلوبة
        private void CreateTables()
        {
            using (var connection = new OleDbConnection(connectionString))
            {
                connection.Open();

                // إنشاء جدول الموظفين المحسن
                string createEmployeesTableSql = @"
                    CREATE TABLE Employees (
                        Id AUTOINCREMENT PRIMARY KEY,
                        FullName TEXT(100) NOT NULL,
                        NationalId TEXT(14),
                        InsuranceNumber TEXT(50),
                        JobTitle TEXT(100),
                        Department TEXT(100),
                        HireDate DATETIME,
                        EmployeeType TEXT(50),
                        Phone TEXT(20),
                        Email TEXT(100),
                        Address TEXT(255),
                        Salary CURRENCY,
                        
                        -- حقول الانتداب
                        DecisionNumber TEXT(100),
                        DecisionDate DATETIME,
                        FromToEntity TEXT(500),
                        WorkDepartment TEXT(100),
                        Management TEXT(100),
                        LeaveEndDate DATETIME,
                        AppointmentType TEXT(100),
                        
                        -- الاشتراكات التأمينية
                        TotalInsuranceContributions CURRENCY,
                        Rate12Percent CURRENCY,
                        Rate9Percent CURRENCY,
                        Rate3Percent CURRENCY,
                        Rate1Percent_1 CURRENCY,
                        Rate1Percent_2 CURRENCY,
                        Rate1Percent_3 CURRENCY,
                        Rate1Percent_4 CURRENCY,
                        Rate025Percent CURRENCY,
                        
                        -- الحقول الإضافية
                        CashReplacement CURRENCY,
                        ConsiderationPeriod INTEGER,
                        LoanInstallment CURRENCY,
                        
                        -- حقول إصابة العمل
                        InjuryType TEXT(100),
                        InjuryDate DATETIME,
                        
                        -- حقول الإجازة بدون مرتب
                        UnpaidLeaveType TEXT(100),
                        LeaveStatus TEXT(50),
                        LeaveStartDate DATETIME,
                        UnpaidLeaveEndDate DATETIME,
                        
                        -- بيانات النظام
                        CreatedBy TEXT(100),
                        CreatedDate DATETIME,
                        Notes MEMO
                    )";

                CreateTableIfNotExists(connection, "Employees", createEmployeesTableSql);

                // إنشاء جدول ملفات الموظفين
                string createEmployeeFilesTableSql = @"
                    CREATE TABLE EmployeeFiles (
                        Id AUTOINCREMENT PRIMARY KEY,
                        EmployeeId INTEGER NOT NULL,
                        FileName TEXT(255) NOT NULL,
                        FilePath TEXT(500) NOT NULL,
                        FileType TEXT(50),
                        FileSize LONG,
                        Category TEXT(100),
                        Description MEMO,
                        UploadedBy TEXT(100),
                        UploadDate DATETIME
                    )";

                CreateTableIfNotExists(connection, "EmployeeFiles", createEmployeeFilesTableSql);

                // إنشاء جدول الإشعارات
                string createNotificationsTableSql = @"
                    CREATE TABLE Notifications (
                        NotificationID AUTOINCREMENT PRIMARY KEY,
                        Title TEXT(255) NOT NULL,
                        Message MEMO NOT NULL,
                        NotificationType TEXT(50),
                        Priority TEXT(20),
                        TargetUsers TEXT(255),
                        IsActive YESNO,
                        ExpiryDate DATETIME,
                        CreatedBy TEXT(100),
                        CreatedDate DATETIME
                    )";

                CreateTableIfNotExists(connection, "Notifications", createNotificationsTableSql);

                // إنشاء جدول الملاحظات
                string createNotesTableSql = @"
                    CREATE TABLE Notes (
                        NoteID AUTOINCREMENT PRIMARY KEY,
                        Title TEXT(255) NOT NULL,
                        Content MEMO NOT NULL,
                        Category TEXT(100),
                        RelatedEntityType TEXT(50),
                        RelatedEntityID INTEGER,
                        IsPrivate YESNO,
                        CreatedBy TEXT(100),
                        CreatedDate DATETIME,
                        Tags TEXT(500)
                    )";

                CreateTableIfNotExists(connection, "Notes", createNotesTableSql);

                // إنشاء جدول التلميحات
                string createTipsTableSql = @"
                    CREATE TABLE Tips (
                        TipID AUTOINCREMENT PRIMARY KEY,
                        Title TEXT(255) NOT NULL,
                        Content MEMO NOT NULL,
                        Category TEXT(100),
                        TipType TEXT(50),
                        IsActive YESNO,
                        DisplayOrder INTEGER,
                        CreatedBy TEXT(100),
                        CreatedDate DATETIME
                    )";

                CreateTableIfNotExists(connection, "Tips", createTipsTableSql);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء جميع الجداول في قاعدة بيانات Access.");
            }
        }

        // دالة مساعدة لإنشاء الجدول إذا لم يكن موجوداً
        private void CreateTableIfNotExists(OleDbConnection connection, string tableName, string createTableSql)
        {
            try
            {
                using (var command = new OleDbCommand(createTableSql, connection))
                {
                    command.ExecuteNonQuery();
                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء جدول {tableName} بنجاح.");
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("already exists") || ex.Message.Contains("موجود"))
                {
                    System.Diagnostics.Debug.WriteLine($"ℹ️ جدول {tableName} موجود بالفعل.");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ أثناء إنشاء جدول {tableName}: {ex.Message}");
                }
            }
        }

        // اختبار الاتصال بقاعدة البيانات
        public bool TestConnection()
        {
            try
            {
                using (var connection = new OleDbConnection(connectionString))
                {
                    connection.Open();
                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل الاتصال بقاعدة بيانات Access: {ex.Message}");
                return false;
            }
        }

        // إضافة موظف جديد
        public int AddEmployee(Employee employee)
        {
            try
            {
                using (var connection = new OleDbConnection(connectionString))
                {
                    connection.Open();

                    string insertSql = @"
                        INSERT INTO Employees (
                            FullName, NationalId, InsuranceNumber, JobTitle, Department, HireDate, EmployeeType,
                            Phone, Email, Address, Salary, DecisionNumber, DecisionDate, FromToEntity,
                            WorkDepartment, Management, LeaveEndDate, AppointmentType,
                            TotalInsuranceContributions, Rate12Percent, Rate9Percent, Rate3Percent,
                            Rate1Percent_1, Rate1Percent_2, Rate1Percent_3, Rate1Percent_4, Rate025Percent,
                            CashReplacement, ConsiderationPeriod, LoanInstallment,
                            InjuryType, InjuryDate, UnpaidLeaveType, LeaveStatus, LeaveStartDate, UnpaidLeaveEndDate,
                            CreatedBy, CreatedDate, Notes
                        ) VALUES (
                            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                        )";

                    using (var command = new OleDbCommand(insertSql, connection))
                    {
                        // إضافة المعاملات
                        command.Parameters.AddWithValue("@FullName", employee.Name ?? "");
                        command.Parameters.AddWithValue("@NationalId", employee.NationalId ?? "");
                        command.Parameters.AddWithValue("@InsuranceNumber", employee.InsuranceNumber ?? "");
                        command.Parameters.AddWithValue("@JobTitle", employee.JobTitle ?? "");
                        command.Parameters.AddWithValue("@Department", employee.Department ?? "");
                        command.Parameters.AddWithValue("@HireDate", employee.HireDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@EmployeeType", employee.EmployeeType ?? "");
                        command.Parameters.AddWithValue("@Phone", employee.ContactInfo ?? "");
                        command.Parameters.AddWithValue("@Email", "");
                        command.Parameters.AddWithValue("@Address", "");
                        command.Parameters.AddWithValue("@Salary", 0);
                        command.Parameters.AddWithValue("@DecisionNumber", employee.DecisionNumber ?? "");
                        command.Parameters.AddWithValue("@DecisionDate", employee.DecisionDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@FromToEntity", employee.FromToEntity ?? "");
                        command.Parameters.AddWithValue("@WorkDepartment", employee.WorkDepartment ?? "");
                        command.Parameters.AddWithValue("@Management", employee.Management ?? "");
                        command.Parameters.AddWithValue("@LeaveEndDate", employee.LeaveEndDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@AppointmentType", employee.AppointmentType ?? "");
                        command.Parameters.AddWithValue("@TotalInsuranceContributions", employee.TotalInsuranceContributions ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate12Percent", employee.Rate12Percent ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate9Percent", employee.Rate9Percent ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate3Percent", employee.Rate3Percent ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate1Percent_1", employee.Rate1Percent_1 ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate1Percent_2", employee.Rate1Percent_2 ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate1Percent_3", employee.Rate1Percent_3 ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate1Percent_4", employee.Rate1Percent_4 ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Rate025Percent", employee.Rate025Percent ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@CashReplacement", employee.CashReplacement ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@ConsiderationPeriod", employee.ConsiderationPeriod ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@LoanInstallment", employee.LoanInstallment ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@InjuryType", employee.InjuryType ?? "");
                        command.Parameters.AddWithValue("@InjuryDate", employee.InjuryDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@UnpaidLeaveType", employee.UnpaidLeaveType ?? "");
                        command.Parameters.AddWithValue("@LeaveStatus", employee.LeaveStatus ?? "");
                        command.Parameters.AddWithValue("@LeaveStartDate", employee.LeaveStartDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@UnpaidLeaveEndDate", employee.UnpaidLeaveEndDate ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@CreatedBy", "النظام");
                        command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                        command.Parameters.AddWithValue("@Notes", employee.Notes ?? "");

                        command.ExecuteNonQuery();

                        // الحصول على ID الموظف الجديد
                        using (var idCommand = new OleDbCommand("SELECT @@IDENTITY", connection))
                        {
                            var result = idCommand.ExecuteScalar();
                            return Convert.ToInt32(result);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة الموظف إلى Access: {ex.Message}");
                throw;
            }
        }

        // جلب جميع الموظفين
        public DataTable GetAllEmployees()
        {
            try
            {
                using (var connection = new OleDbConnection(connectionString))
                {
                    connection.Open();

                    string selectSql = "SELECT * FROM Employees ORDER BY FullName";
                    using (var adapter = new OleDbDataAdapter(selectSql, connection))
                    {
                        var dataTable = new DataTable();
                        adapter.Fill(dataTable);
                        return dataTable;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب الموظفين من Access: {ex.Message}");
                return new DataTable();
            }
        }

        // تشخيص قاعدة البيانات
        public string DiagnoseDatabase()
        {
            var diagnosis = new System.Text.StringBuilder();
            
            try
            {
                diagnosis.AppendLine("🔍 تشخيص قاعدة بيانات Access:");
                diagnosis.AppendLine($"📁 ملف قاعدة البيانات: {dbFileName}");
                diagnosis.AppendLine($"📍 المسار: {Path.GetFullPath(dbFileName)}");
                
                if (File.Exists(dbFileName))
                {
                    var fileInfo = new FileInfo(dbFileName);
                    diagnosis.AppendLine($"✅ الملف موجود - الحجم: {fileInfo.Length / 1024} KB");
                    diagnosis.AppendLine($"📅 تاريخ الإنشاء: {fileInfo.CreationTime}");
                    diagnosis.AppendLine($"📅 آخر تعديل: {fileInfo.LastWriteTime}");
                }
                else
                {
                    diagnosis.AppendLine("❌ ملف قاعدة البيانات غير موجود");
                }

                if (TestConnection())
                {
                    diagnosis.AppendLine("✅ الاتصال بقاعدة البيانات ناجح");
                    
                    // فحص الجداول
                    using (var connection = new OleDbConnection(connectionString))
                    {
                        connection.Open();
                        var tables = connection.GetSchema("Tables");
                        
                        diagnosis.AppendLine($"\n📊 عدد الجداول: {tables.Rows.Count}");
                        
                        foreach (DataRow row in tables.Rows)
                        {
                            string tableName = row["TABLE_NAME"].ToString();
                            if (tableName.StartsWith("MSys")) continue; // تجاهل جداول النظام
                            
                            try
                            {
                                using (var countCommand = new OleDbCommand($"SELECT COUNT(*) FROM [{tableName}]", connection))
                                {
                                    int count = Convert.ToInt32(countCommand.ExecuteScalar());
                                    diagnosis.AppendLine($"✅ جدول {tableName}: {count} سجل");
                                }
                            }
                            catch (Exception ex)
                            {
                                diagnosis.AppendLine($"❌ جدول {tableName}: خطأ - {ex.Message}");
                            }
                        }
                    }
                }
                else
                {
                    diagnosis.AppendLine("❌ فشل الاتصال بقاعدة البيانات");
                }
            }
            catch (Exception ex)
            {
                diagnosis.AppendLine($"❌ خطأ في التشخيص: {ex.Message}");
            }

            return diagnosis.ToString();
        }
    }
}
