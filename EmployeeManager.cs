using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.IO;
using System.Linq;

namespace Ahmedapp_for_work
{
    public class EmployeeManager
    {
        private readonly DatabaseManager dbManager;

        public EmployeeManager()
        {
            dbManager = new DatabaseManager();

            // محاولة إنشاء قاعدة البيانات إذا لم تكن موجودة
            DatabaseDiagnostic.CreateDatabaseIfNotExists();

            InitializeTables();
        }

        private void InitializeTables()
        {
            try
            {
                // التحقق من الاتصال بقاعدة البيانات أولاً
                if (!dbManager.TestConnection())
                {
                    // إذا فشل الاتصال، لا نحاول إنشاء الجداول
                    return;
                }
                // Create Employees table
                string createEmployeesTable = @"
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Employees' AND xtype='U')
                    CREATE TABLE Employees (
                        Id INT IDENTITY(1,1) PRIMARY KEY,
                        Name NVARCHAR(200) NOT NULL,
                        NationalId NVARCHAR(50) UNIQUE NOT NULL,
                        InsuranceNumber NVARCHAR(50),
                        JobTitle NVARCHAR(200),
                        Department NVARCHAR(200),
                        HireDate DATETIME,
                        EmployeeType NVARCHAR(100) DEFAULT 'موظف عادي',
                        IsActive BIT DEFAULT 1,
                        CreatedDate DATETIME DEFAULT GETDATE(),
                        CreatedBy NVARCHAR(100),

                        -- Delegation fields
                        DecisionNumber NVARCHAR(100),
                        DecisionDate DATETIME,
                        FromToEntity NVARCHAR(200),
                        WorkDepartment NVARCHAR(200),
                        Management NVARCHAR(200),
                        LeaveEndDate DATETIME,
                        AppointmentType NVARCHAR(100),
                        RequiredDocuments NTEXT,

                        -- Injury fields
                        InjuryType NVARCHAR(100),
                        InjuryDate DATETIME,
                        InjuryLocation NVARCHAR(200),
                        InjuryDescription NTEXT,

                        -- Unpaid leave fields
                        UnpaidLeaveType NVARCHAR(100),
                        LeaveStatus NVARCHAR(100),
                        LeaveStartDate DATETIME,
                        UnpaidLeaveEndDate DATETIME,

                        -- Additional fields
                        Notes NTEXT,
                        ContactInfo NVARCHAR(200),
                        EmergencyContact NVARCHAR(200)
                    )";

                // Create EmployeeFiles table
                string createFilesTable = @"
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmployeeFiles' AND xtype='U')
                    CREATE TABLE EmployeeFiles (
                        Id INT IDENTITY(1,1) PRIMARY KEY,
                        EmployeeId INT NOT NULL,
                        FileName NVARCHAR(255) NOT NULL,
                        FilePath NVARCHAR(500) NOT NULL,
                        FileType NVARCHAR(50),
                        FileSize BIGINT,
                        UploadDate DATETIME DEFAULT GETDATE(),
                        UploadedBy NVARCHAR(100),
                        Category NVARCHAR(100),
                        Description NTEXT,
                        FOREIGN KEY (EmployeeId) REFERENCES Employees(Id)
                    )";

                // Create EmployeeHistory table
                string createHistoryTable = @"
                    IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmployeeHistory' AND xtype='U')
                    CREATE TABLE EmployeeHistory (
                        Id INT IDENTITY(1,1) PRIMARY KEY,
                        EmployeeId INT NOT NULL,
                        Action NVARCHAR(100) NOT NULL,
                        Details NTEXT,
                        ActionDate DATETIME DEFAULT GETDATE(),
                        ActionBy NVARCHAR(100),
                        OldValue NTEXT,
                        NewValue NTEXT,
                        FOREIGN KEY (EmployeeId) REFERENCES Employees(Id)
                    )";

                dbManager.ExecuteNonQuery(createEmployeesTable);
                dbManager.ExecuteNonQuery(createFilesTable);
                dbManager.ExecuteNonQuery(createHistoryTable);
            }
            catch (Exception ex)
            {
                // لا نرمي استثناء، فقط نسجل الخطأ ونتابع
                System.Diagnostics.Debug.WriteLine($"تحذير: لم يتم إنشاء جداول الموظفين: {ex.Message}");
            }
        }

        public bool AddEmployee(Employee employee)
        {
            try
            {
                // التحقق من الاتصال بقاعدة البيانات أولاً
                if (!dbManager.TestConnection())
                {
                    throw new Exception("لا يمكن الاتصال بقاعدة البيانات. تأكد من تشغيل SQL Server LocalDB.");
                }

                string query = @"
                    INSERT INTO Employees (
                        Name, NationalId, InsuranceNumber, JobTitle, Department, HireDate,
                        EmployeeType, CreatedBy, DecisionNumber, DecisionDate, FromToEntity,
                        WorkDepartment, Management, LeaveEndDate, AppointmentType, RequiredDocuments,
                        InjuryType, InjuryDate, InjuryLocation, InjuryDescription,
                        UnpaidLeaveType, LeaveStatus, LeaveStartDate, UnpaidLeaveEndDate,
                        Notes, ContactInfo, EmergencyContact
                    ) VALUES (
                        @Name, @NationalId, @InsuranceNumber, @JobTitle, @Department, @HireDate,
                        @EmployeeType, @CreatedBy, @DecisionNumber, @DecisionDate, @FromToEntity,
                        @WorkDepartment, @Management, @LeaveEndDate, @AppointmentType, @RequiredDocuments,
                        @InjuryType, @InjuryDate, @InjuryLocation, @InjuryDescription,
                        @UnpaidLeaveType, @LeaveStatus, @LeaveStartDate, @UnpaidLeaveEndDate,
                        @Notes, @ContactInfo, @EmergencyContact
                    )";

                SqlParameter[] parameters = {
                    new SqlParameter("@Name", employee.Name),
                    new SqlParameter("@NationalId", employee.NationalId),
                    new SqlParameter("@InsuranceNumber", employee.InsuranceNumber ?? (object)DBNull.Value),
                    new SqlParameter("@JobTitle", employee.JobTitle ?? (object)DBNull.Value),
                    new SqlParameter("@Department", employee.Department ?? (object)DBNull.Value),
                    new SqlParameter("@HireDate", employee.HireDate),
                    new SqlParameter("@EmployeeType", employee.EmployeeType),
                    new SqlParameter("@CreatedBy", employee.CreatedBy),
                    new SqlParameter("@DecisionNumber", employee.DecisionNumber ?? (object)DBNull.Value),
                    new SqlParameter("@DecisionDate", employee.DecisionDate ?? (object)DBNull.Value),
                    new SqlParameter("@FromToEntity", employee.FromToEntity ?? (object)DBNull.Value),
                    new SqlParameter("@WorkDepartment", employee.WorkDepartment ?? (object)DBNull.Value),
                    new SqlParameter("@Management", employee.Management ?? (object)DBNull.Value),
                    new SqlParameter("@LeaveEndDate", employee.LeaveEndDate ?? (object)DBNull.Value),
                    new SqlParameter("@AppointmentType", employee.AppointmentType ?? (object)DBNull.Value),
                    new SqlParameter("@RequiredDocuments", employee.RequiredDocuments ?? (object)DBNull.Value),
                    new SqlParameter("@InjuryType", employee.InjuryType ?? (object)DBNull.Value),
                    new SqlParameter("@InjuryDate", employee.InjuryDate ?? (object)DBNull.Value),
                    new SqlParameter("@InjuryLocation", employee.InjuryLocation ?? (object)DBNull.Value),
                    new SqlParameter("@InjuryDescription", employee.InjuryDescription ?? (object)DBNull.Value),
                    new SqlParameter("@UnpaidLeaveType", employee.UnpaidLeaveType ?? (object)DBNull.Value),
                    new SqlParameter("@LeaveStatus", employee.LeaveStatus ?? (object)DBNull.Value),
                    new SqlParameter("@LeaveStartDate", employee.LeaveStartDate ?? (object)DBNull.Value),
                    new SqlParameter("@UnpaidLeaveEndDate", employee.UnpaidLeaveEndDate ?? (object)DBNull.Value),
                    new SqlParameter("@Notes", employee.Notes ?? (object)DBNull.Value),
                    new SqlParameter("@ContactInfo", employee.ContactInfo ?? (object)DBNull.Value),
                    new SqlParameter("@EmergencyContact", employee.EmergencyContact ?? (object)DBNull.Value)
                };

                int result = dbManager.ExecuteNonQuery(query, parameters);

                if (result > 0)
                {
                    // Add to history
                    try
                    {
                        var savedEmployee = GetEmployeeByNationalId(employee.NationalId);
                        if (savedEmployee != null)
                        {
                            AddEmployeeHistory(savedEmployee.Id, "إضافة موظف", $"تم إضافة الموظف: {employee.Name}", employee.CreatedBy);
                        }
                    }
                    catch (Exception historyEx)
                    {
                        // لا نفشل العملية بسبب مشكلة في التاريخ
                        System.Diagnostics.Debug.WriteLine($"تحذير: فشل في إضافة سجل التاريخ: {historyEx.Message}");
                    }
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                // رمي الاستثناء مع تفاصيل الخطأ
                throw new Exception($"فشل في حفظ الموظف: {ex.Message}");
            }
        }

        public Employee? GetEmployeeByNationalId(string nationalId)
        {
            try
            {
                string query = "SELECT * FROM Employees WHERE NationalId = @NationalId";
                SqlParameter[] parameters = { new SqlParameter("@NationalId", nationalId) };

                var result = dbManager.ExecuteQuery(query, parameters);
                if (result.Rows.Count > 0)
                {
                    var row = result.Rows[0];
                    return MapRowToEmployee(row);
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        public List<Employee> GetAllEmployees()
        {
            try
            {
                string query = "SELECT * FROM Employees WHERE IsActive = 1 ORDER BY CreatedDate DESC";
                var result = dbManager.ExecuteQuery(query);

                var employees = new List<Employee>();
                foreach (System.Data.DataRow row in result.Rows)
                {
                    employees.Add(MapRowToEmployee(row));
                }
                return employees;
            }
            catch
            {
                return new List<Employee>();
            }
        }

        public List<Employee> SearchEmployees(string searchTerm)
        {
            try
            {
                string query = @"
                    SELECT * FROM Employees
                    WHERE IsActive = 1 AND (
                        Name LIKE @SearchTerm OR
                        NationalId LIKE @SearchTerm OR
                        Department LIKE @SearchTerm OR
                        JobTitle LIKE @SearchTerm
                    )
                    ORDER BY Name";

                SqlParameter[] parameters = {
                    new SqlParameter("@SearchTerm", $"%{searchTerm}%")
                };

                var result = dbManager.ExecuteQuery(query, parameters);
                var employees = new List<Employee>();

                foreach (System.Data.DataRow row in result.Rows)
                {
                    employees.Add(MapRowToEmployee(row));
                }
                return employees;
            }
            catch
            {
                return new List<Employee>();
            }
        }

        private Employee MapRowToEmployee(System.Data.DataRow row)
        {
            return new Employee
            {
                Id = Convert.ToInt32(row["Id"]),
                Name = row["Name"].ToString() ?? "",
                NationalId = row["NationalId"].ToString() ?? "",
                InsuranceNumber = row["InsuranceNumber"].ToString() ?? "",
                JobTitle = row["JobTitle"].ToString() ?? "",
                Department = row["Department"].ToString() ?? "",
                HireDate = row["HireDate"] != DBNull.Value ? Convert.ToDateTime(row["HireDate"]) : DateTime.Now,
                EmployeeType = row["EmployeeType"].ToString() ?? "موظف عادي",
                IsActive = Convert.ToBoolean(row["IsActive"]),
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                CreatedBy = row["CreatedBy"].ToString() ?? "",

                // Delegation fields
                DecisionNumber = row["DecisionNumber"].ToString(),
                DecisionDate = row["DecisionDate"] != DBNull.Value ? Convert.ToDateTime(row["DecisionDate"]) : null,
                FromToEntity = row["FromToEntity"].ToString(),
                WorkDepartment = row["WorkDepartment"].ToString(),
                Management = row["Management"].ToString(),
                LeaveEndDate = row["LeaveEndDate"] != DBNull.Value ? Convert.ToDateTime(row["LeaveEndDate"]) : null,
                AppointmentType = row["AppointmentType"].ToString(),
                RequiredDocuments = row["RequiredDocuments"].ToString(),

                // Injury fields
                InjuryType = row["InjuryType"].ToString(),
                InjuryDate = row["InjuryDate"] != DBNull.Value ? Convert.ToDateTime(row["InjuryDate"]) : null,
                InjuryLocation = row["InjuryLocation"].ToString(),
                InjuryDescription = row["InjuryDescription"].ToString(),

                // Unpaid leave fields
                UnpaidLeaveType = row["UnpaidLeaveType"].ToString(),
                LeaveStatus = row["LeaveStatus"].ToString(),
                LeaveStartDate = row["LeaveStartDate"] != DBNull.Value ? Convert.ToDateTime(row["LeaveStartDate"]) : null,
                UnpaidLeaveEndDate = row["UnpaidLeaveEndDate"] != DBNull.Value ? Convert.ToDateTime(row["UnpaidLeaveEndDate"]) : null,

                // Additional fields
                Notes = row["Notes"].ToString(),
                ContactInfo = row["ContactInfo"].ToString(),
                EmergencyContact = row["EmergencyContact"].ToString()
            };
        }

        public void AddEmployeeHistory(int employeeId, string action, string details, string actionBy)
        {
            try
            {
                string query = @"
                    INSERT INTO EmployeeHistory (EmployeeId, Action, Details, ActionBy)
                    VALUES (@EmployeeId, @Action, @Details, @ActionBy)";

                SqlParameter[] parameters = {
                    new SqlParameter("@EmployeeId", employeeId),
                    new SqlParameter("@Action", action),
                    new SqlParameter("@Details", details),
                    new SqlParameter("@ActionBy", actionBy)
                };

                dbManager.ExecuteNonQuery(query, parameters);
            }
            catch
            {
                // Log error if needed
            }
        }

        // إضافة دعم حفظ ملفات الموظف
        public bool SaveEmployeeFile(int employeeId, string fileName, string filePath, string fileType, long fileSize, string uploadedBy, string category = "", string description = "")
        {
            try
            {
                return dbManager.SaveEmployeeFile(employeeId, fileName, filePath, fileType, fileSize, uploadedBy, category, description);
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في حفظ ملف الموظف: {ex.Message}");
            }
        }

        // إضافة دعم استرجاع ملفات الموظف
        public List<EmployeeFile> GetEmployeeFiles(int employeeId)
        {
            try
            {
                var files = new List<EmployeeFile>();
                var dataTable = dbManager.GetEmployeeFiles(employeeId);

                foreach (System.Data.DataRow row in dataTable.Rows)
                {
                    files.Add(new EmployeeFile
                    {
                        Id = Convert.ToInt32(row["Id"]),
                        EmployeeId = Convert.ToInt32(row["EmployeeId"]),
                        FileName = row["FileName"].ToString(),
                        FilePath = row["FilePath"].ToString(),
                        FileType = row["FileType"].ToString(),
                        FileSize = Convert.ToInt64(row["FileSize"]),
                        UploadDate = Convert.ToDateTime(row["UploadDate"]),
                        UploadedBy = row["UploadedBy"].ToString(),
                        Category = row["Category"].ToString(),
                        Description = row["Description"].ToString()
                    });
                }

                return files;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في استرجاع ملفات الموظف: {ex.Message}");
            }
        }

        // إضافة دعم حذف ملفات الموظف
        public bool DeleteEmployeeFile(int fileId)
        {
            try
            {
                return dbManager.DeleteEmployeeFile(fileId);
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في حذف ملف الموظف: {ex.Message}");
            }
        }

        // دالة تشخيص قاعدة البيانات
        public string DiagnoseDatabaseIssues()
        {
            try
            {
                return dbManager.ComprehensiveDatabaseDiagnosis();
            }
            catch (Exception ex)
            {
                return $"❌ خطأ في التشخيص: {ex.Message}";
            }
        }

        // إنشاء قاعدة البيانات والجداول
        public bool InitializeDatabase()
        {
            try
            {
                return dbManager.InitializeDatabase();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء قاعدة البيانات: {ex.Message}");
                return false;
            }
        }

        // إضافة موظف وإرجاع ID الموظف الجديد
        public int AddEmployeeAndGetId(Employee employee)
        {
            try
            {
                // التحقق من الاتصال بقاعدة البيانات أولاً
                if (!dbManager.TestConnection())
                {
                    // محاولة إنشاء قاعدة البيانات
                    if (!dbManager.InitializeDatabase())
                    {
                        throw new Exception("لا يمكن الاتصال بقاعدة البيانات أو إنشاؤها. تأكد من تشغيل SQL Server LocalDB.");
                    }
                }

                string query = @"INSERT INTO Employees
                    (Name, NationalId, InsuranceNumber, JobTitle, Department, HireDate, EmployeeType, CreatedBy,
                     DecisionNumber, DecisionDate, FromToEntity, WorkDepartment, Management, LeaveEndDate, AppointmentType, RequiredDocuments,
                     TotalInsuranceContributions, Rate12Percent, Rate9Percent, Rate3Percent, Rate1Percent_1, Rate1Percent_2, Rate1Percent_3, Rate1Percent_4, Rate025Percent,
                     CashReplacement, ConsiderationPeriod, LoanInstallment, OtherDetails,
                     InjuryType, InjuryDate, UnpaidLeaveType, LeaveStatus, LeaveStartDate, UnpaidLeaveEndDate)
                    OUTPUT INSERTED.Id
                    VALUES
                    (@Name, @NationalId, @InsuranceNumber, @JobTitle, @Department, @HireDate, @EmployeeType, @CreatedBy,
                     @DecisionNumber, @DecisionDate, @FromToEntity, @WorkDepartment, @Management, @LeaveEndDate, @AppointmentType, @RequiredDocuments,
                     @TotalInsuranceContributions, @Rate12Percent, @Rate9Percent, @Rate3Percent, @Rate1Percent_1, @Rate1Percent_2, @Rate1Percent_3, @Rate1Percent_4, @Rate025Percent,
                     @CashReplacement, @ConsiderationPeriod, @LoanInstallment, @OtherDetails,
                     @InjuryType, @InjuryDate, @UnpaidLeaveType, @LeaveStatus, @LeaveStartDate, @UnpaidLeaveEndDate)";

                SqlParameter[] parameters = {
                    new SqlParameter("@Name", employee.Name ?? ""),
                    new SqlParameter("@NationalId", employee.NationalId ?? ""),
                    new SqlParameter("@InsuranceNumber", employee.InsuranceNumber ?? ""),
                    new SqlParameter("@JobTitle", employee.JobTitle ?? ""),
                    new SqlParameter("@Department", employee.Department ?? ""),
                    new SqlParameter("@HireDate", employee.HireDate),
                    new SqlParameter("@EmployeeType", employee.EmployeeType ?? "موظف عادي"),
                    new SqlParameter("@CreatedBy", employee.CreatedBy ?? ""),

                    // Delegation fields
                    new SqlParameter("@DecisionNumber", employee.DecisionNumber ?? (object)DBNull.Value),
                    new SqlParameter("@DecisionDate", employee.DecisionDate ?? (object)DBNull.Value),
                    new SqlParameter("@FromToEntity", employee.DelegationType ?? (object)DBNull.Value),
                    new SqlParameter("@WorkDepartment", employee.WorkDepartment ?? (object)DBNull.Value),
                    new SqlParameter("@Management", employee.Management ?? (object)DBNull.Value),
                    new SqlParameter("@LeaveEndDate", employee.LeaveEndDate ?? (object)DBNull.Value),
                    new SqlParameter("@AppointmentType", employee.AppointmentType ?? (object)DBNull.Value),
                    new SqlParameter("@RequiredDocuments", employee.RequiredDocuments ?? (object)DBNull.Value),

                    // Injury fields
                    new SqlParameter("@InjuryType", employee.InjuryType ?? (object)DBNull.Value),
                    new SqlParameter("@InjuryDate", employee.InjuryDate ?? (object)DBNull.Value),

                    // Insurance contributions fields
                    new SqlParameter("@TotalInsuranceContributions", employee.TotalInsuranceContributions ?? (object)DBNull.Value),
                    new SqlParameter("@Rate12Percent", employee.Rate12Percent ?? (object)DBNull.Value),
                    new SqlParameter("@Rate9Percent", employee.Rate9Percent ?? (object)DBNull.Value),
                    new SqlParameter("@Rate3Percent", employee.Rate3Percent ?? (object)DBNull.Value),
                    new SqlParameter("@Rate1Percent_1", employee.Rate1Percent_1 ?? (object)DBNull.Value),
                    new SqlParameter("@Rate1Percent_2", employee.Rate1Percent_2 ?? (object)DBNull.Value),
                    new SqlParameter("@Rate1Percent_3", employee.Rate1Percent_3 ?? (object)DBNull.Value),
                    new SqlParameter("@Rate1Percent_4", employee.Rate1Percent_4 ?? (object)DBNull.Value),
                    new SqlParameter("@Rate025Percent", employee.Rate025Percent ?? (object)DBNull.Value),

                    // Additional fields
                    new SqlParameter("@CashReplacement", employee.CashReplacement ?? (object)DBNull.Value),
                    new SqlParameter("@ConsiderationPeriod", employee.ConsiderationPeriod ?? (object)DBNull.Value),
                    new SqlParameter("@LoanInstallment", employee.LoanInstallment ?? (object)DBNull.Value),
                    new SqlParameter("@OtherDetails", employee.OtherDetails ?? (object)DBNull.Value),

                    // Unpaid leave fields
                    new SqlParameter("@UnpaidLeaveType", employee.UnpaidLeaveType ?? (object)DBNull.Value),
                    new SqlParameter("@LeaveStatus", employee.LeaveStatus ?? (object)DBNull.Value),
                    new SqlParameter("@LeaveStartDate", employee.LeaveStartDate ?? (object)DBNull.Value),
                    new SqlParameter("@UnpaidLeaveEndDate", employee.UnpaidLeaveEndDate ?? (object)DBNull.Value)
                };

                var result = dbManager.ExecuteScalar(query, parameters);
                return result != null ? Convert.ToInt32(result) : 0;
            }
            catch (Exception ex)
            {
                // رمي الاستثناء مع تفاصيل الخطأ
                throw new Exception($"فشل في حفظ الموظف: {ex.Message}");
            }
        }
    }
}
