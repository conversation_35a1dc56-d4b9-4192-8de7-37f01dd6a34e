-- HR Management System Database Schema
-- Created by <PERSON> (<EMAIL>)

-- Employees Table
CREATE TABLE Employees (
    EmployeeID INT PRIMARY KEY IDENTITY(1,1),
    EmployeeName NVARCHAR(255) NOT NULL,
    Department NVARCHAR(100),
    Position NVARCHAR(100),
    HireDate DATE,
    Salary DECIMAL(10,2),
    Phone NVARCHAR(20),
    Email NVARCHAR(100),
    Address NVARCHAR(500),
    Status NVARCHAR(50) DEFAULT 'Active',
    CreatedDate DATETIME DEFAULT GETDATE()
);

-- Forms Table (النماذج)
CREATE TABLE Forms (
    FormID INT PRIMARY KEY IDENTITY(1,1),
    FormName NVARCHAR(255) NOT NULL,
    FormType NVARCHAR(100),
    EmployeeID INT,
    FormContent NVARCHAR(MAX),
    CreatedDate DATETIME DEFAULT GETDATE(),
    Status NVARCHAR(50) DEFAULT 'Pending',
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (EmployeeID) REFERENCES Employees(EmployeeID)
);

-- Work Injury Files (ملفات إصابة العمل)
CREATE TABLE WorkInjuryFiles (
    InjuryID INT PRIMARY KEY IDENTITY(1,1),
    EmployeeID INT NOT NULL,
    InjuryDate DATE NOT NULL,
    InjuryDescription NVARCHAR(MAX),
    InjuryLocation NVARCHAR(255),
    TreatmentDetails NVARCHAR(MAX),
    MedicalReports NVARCHAR(500),
    Status NVARCHAR(50) DEFAULT 'Under Treatment',
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID)
);

-- Unpaid Leave Files (ملفات الإجازة بدون مرتب)
CREATE TABLE UnpaidLeaveFiles (
    LeaveID INT PRIMARY KEY IDENTITY(1,1),
    EmployeeID INT NOT NULL,
    LeaveType NVARCHAR(100) NOT NULL, -- 'داخل البلاد', 'خارج البلاد', 'رعاية طفل'
    StartDate DATE NOT NULL,
    EndDate DATE NOT NULL,
    Reason NVARCHAR(MAX),
    ApprovalStatus NVARCHAR(50) DEFAULT 'Pending',
    ApprovedBy NVARCHAR(100),
    ApprovalDate DATE,
    Documents NVARCHAR(500),
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID)
);

-- Monthly Achievements (ما تم إنجازه هذا الشهر)
CREATE TABLE MonthlyAchievements (
    AchievementID INT PRIMARY KEY IDENTITY(1,1),
    EmployeeID INT NOT NULL,
    Month INT NOT NULL,
    Year INT NOT NULL,
    CompletedTasks NVARCHAR(MAX),
    PendingTasks NVARCHAR(MAX),
    Achievements NVARCHAR(MAX),
    Challenges NVARCHAR(MAX),
    NextMonthPlans NVARCHAR(MAX),
    PerformanceRating INT CHECK (PerformanceRating BETWEEN 1 AND 5),
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID)
);

-- Insert Sample Data
INSERT INTO Employees (EmployeeName, Department, Position, HireDate, Salary, Phone, Email) VALUES
('Ahmed Ibrahim', 'IT', 'Software Developer', '2023-01-15', 5000.00, '01234567890', '<EMAIL>'),
('محمد علي', 'الموارد البشرية', 'مدير الموارد البشرية', '2022-03-10', 7000.00, '01111111111', '<EMAIL>'),
('فاطمة أحمد', 'المالية', 'محاسب', '2023-06-01', 4500.00, '01222222222', '<EMAIL>'),
('سارة محمود', 'التسويق', 'أخصائي تسويق', '2023-02-20', 4000.00, '01333333333', '<EMAIL>');

INSERT INTO Forms (FormName, FormType, EmployeeID, FormContent, Status) VALUES
('طلب إجازة سنوية', 'إجازة', 1, 'طلب إجازة سنوية لمدة أسبوع', 'Approved'),
('طلب تدريب', 'تدريب', 2, 'طلب حضور دورة تدريبية في الإدارة', 'Pending');

INSERT INTO UnpaidLeaveFiles (EmployeeID, LeaveType, StartDate, EndDate, Reason, ApprovalStatus) VALUES
(1, 'خارج البلاد', '2024-07-01', '2024-07-30', 'سفر للعلاج', 'Approved'),
(3, 'رعاية طفل', '2024-06-15', '2024-09-15', 'رعاية طفل حديث الولادة', 'Pending');

-- City Council Delegates Table (المنتدبين من وإلى مجلس المدينة)
CREATE TABLE CityCouncilDelegates (
    DelegateID INT PRIMARY KEY IDENTITY(1,1),
    EmployeeID INT NOT NULL,
    DelegationType NVARCHAR(100) NOT NULL, -- 'منتدب من مجلس المدينة', 'منتدب إلى مجلس المدينة'
    DelegationStartDate DATE NOT NULL,
    DelegationEndDate DATE,
    DelegationReason NVARCHAR(MAX),
    DelegationDetails NVARCHAR(MAX),
    SourceDepartment NVARCHAR(255),
    TargetDepartment NVARCHAR(255),
    DelegationStatus NVARCHAR(50) DEFAULT 'Active', -- 'Active', 'Completed', 'Cancelled'
    ApprovedBy NVARCHAR(100),
    ApprovalDate DATE,
    Documents NVARCHAR(500),
    Notes NVARCHAR(MAX),
    CreatedDate DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID)
);

INSERT INTO MonthlyAchievements (EmployeeID, Month, Year, CompletedTasks, PendingTasks, Achievements, PerformanceRating) VALUES
(1, 5, 2024, 'تطوير نظام إدارة الموارد البشرية، إصلاح 15 خطأ برمجي', 'تطوير تطبيق المحاسبة', 'حصل على شهادة تقدير من الإدارة', 5),
(2, 5, 2024, 'مراجعة ملفات الموظفين، تنظيم 3 ورش عمل', 'إعداد تقرير الأداء السنوي', 'تحسين كفاءة قسم الموارد البشرية بنسبة 20%', 4);

-- Insert Sample Delegation Data
-- Notifications Table (التنبيهات)
CREATE TABLE Notifications (
    NotificationID INT PRIMARY KEY IDENTITY(1,1),
    Title NVARCHAR(255) NOT NULL,
    Message NVARCHAR(MAX) NOT NULL,
    NotificationType NVARCHAR(50) NOT NULL, -- 'Info', 'Warning', 'Error', 'Success'
    Priority NVARCHAR(20) DEFAULT 'Medium', -- 'Low', 'Medium', 'High', 'Critical'
    TargetUsers NVARCHAR(MAX), -- JSON array of user IDs or 'All'
    IsActive BIT DEFAULT 1,
    ExpiryDate DATETIME,
    CreatedBy NVARCHAR(100),
    CreatedDate DATETIME DEFAULT GETDATE(),
    ReadBy NVARCHAR(MAX) -- JSON array of user IDs who read the notification
);

-- Notes Table (الملاحظات)
CREATE TABLE Notes (
    NoteID INT PRIMARY KEY IDENTITY(1,1),
    Title NVARCHAR(255) NOT NULL,
    Content NVARCHAR(MAX) NOT NULL,
    Category NVARCHAR(100), -- 'General', 'Employee', 'Process', 'Reminder'
    RelatedEntityType NVARCHAR(50), -- 'Employee', 'Form', 'Leave', 'Injury', 'Delegate'
    RelatedEntityID INT,
    IsPrivate BIT DEFAULT 0,
    CreatedBy NVARCHAR(100),
    CreatedDate DATETIME DEFAULT GETDATE(),
    LastModified DATETIME DEFAULT GETDATE(),
    Tags NVARCHAR(500) -- Comma-separated tags
);

-- Tips and Guidelines Table (التلميحات والإرشادات)
CREATE TABLE Tips (
    TipID INT PRIMARY KEY IDENTITY(1,1),
    Title NVARCHAR(255) NOT NULL,
    Content NVARCHAR(MAX) NOT NULL,
    Category NVARCHAR(100) NOT NULL, -- 'Employee Management', 'Forms', 'Leave', 'Injury', 'Delegates', 'General'
    TipType NVARCHAR(50) DEFAULT 'Tip', -- 'Tip', 'Guideline', 'Best Practice', 'Warning'
    IsActive BIT DEFAULT 1,
    DisplayOrder INT DEFAULT 0,
    CreatedBy NVARCHAR(100),
    CreatedDate DATETIME DEFAULT GETDATE(),
    LastModified DATETIME DEFAULT GETDATE()
);

INSERT INTO CityCouncilDelegates (EmployeeID, DelegationType, DelegationStartDate, DelegationEndDate, DelegationReason, SourceDepartment, TargetDepartment, DelegationStatus, ApprovedBy) VALUES
(1, 'منتدب إلى مجلس المدينة', '2024-01-15', '2024-12-31', 'تمثيل قسم تكنولوجيا المعلومات في مجلس المدينة', 'IT', 'مجلس المدينة', 'Active', 'مدير الموارد البشرية'),
(2, 'منتدب من مجلس المدينة', '2024-03-01', '2024-08-31', 'تقديم الخبرة في إدارة الموارد البشرية', 'مجلس المدينة', 'الموارد البشرية', 'Active', 'رئيس مجلس المدينة');

-- Insert Sample Notifications
INSERT INTO Notifications (Title, Message, NotificationType, Priority, TargetUsers, CreatedBy) VALUES
('تحديث النظام', 'سيتم تحديث نظام إدارة الموارد البشرية يوم الجمعة القادم من الساعة 6-8 مساءً', 'Info', 'Medium', 'All', 'مدير النظام'),
('موعد نهائي للتقارير الشهرية', 'يرجى تسليم التقارير الشهرية قبل نهاية الأسبوع الجاري', 'Warning', 'High', 'All', 'مدير الموارد البشرية'),
('تهنئة', 'تم ترقية الموظف أحمد إبراهيم إلى منصب كبير المطورين', 'Success', 'Medium', 'All', 'الإدارة العليا');

-- Insert Sample Notes
INSERT INTO Notes (Title, Content, Category, RelatedEntityType, RelatedEntityID, CreatedBy) VALUES
('ملاحظة حول الموظف أحمد', 'موظف متميز ويحتاج إلى دورات تدريبية إضافية في الذكاء الاصطناعي', 'Employee', 'Employee', 1, 'مدير الموارد البشرية'),
('تحديث إجراءات الإجازات', 'يجب مراجعة إجراءات الإجازات بدون مرتب وتحديث النماذج', 'Process', 'Leave', NULL, 'مدير العمليات'),
('تذكير مهم', 'مراجعة ملفات إصابات العمل شهرياً وتحديث حالة العلاج', 'Reminder', 'Injury', NULL, 'مسؤول السلامة');

-- Insert Sample Tips
INSERT INTO Tips (Title, Content, Category, TipType, CreatedBy) VALUES
('إدارة الموظفين بفعالية', 'تأكد من تحديث بيانات الموظفين بانتظام وإجراء تقييمات دورية لضمان الأداء الأمثل', 'Employee Management', 'Best Practice', 'مدير الموارد البشرية'),
('معالجة النماذج', 'يجب مراجعة جميع النماذج خلال 48 ساعة من تاريخ التقديم لضمان سرعة الاستجابة', 'Forms', 'Guideline', 'مدير العمليات'),
('إدارة الإجازات', 'تأكد من توثيق جميع الإجازات بدون مرتب مع المستندات المطلوبة قبل الموافقة', 'Leave', 'Warning', 'مدير الموارد البشرية'),
('متابعة إصابات العمل', 'يجب متابعة حالة المصابين أسبوعياً وتحديث ملفاتهم الطبية', 'Injury', 'Tip', 'مسؤول السلامة'),
('إدارة المنتدبين', 'تأكد من وجود خطة واضحة لكل منتدب مع أهداف محددة وجدول زمني', 'Delegates', 'Best Practice', 'مدير الموارد البشرية');
