using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;

namespace Ahmedapp_for_work
{
    public class SimpleAdminDashboard : Form
    {
        private string _username;
        private DataGridView dgvUsers;
        private Label lblTitle;
        private Label lblCount;
        private Button btnRefresh;
        private Button btnClose;

        public SimpleAdminDashboard(string username)
        {
            _username = username;
            InitializeSimpleComponents();
            LoadSimpleUsers();
        }

        private void InitializeSimpleComponents()
        {
            try
            {
                // إعدادات النافذة
                this.Text = $"لوحة تحكم المدير - {_username}";
                this.Size = new Size(800, 600);
                this.StartPosition = FormStartPosition.CenterScreen;
                this.BackColor = Color.LightBlue;

                // عنوان
                lblTitle = new Label
                {
                    Text = $"مرحباً {_username} - لوحة تحكم المدير",
                    Font = new Font("Tahoma", 14, FontStyle.Bold),
                    Size = new Size(750, 30),
                    Location = new Point(25, 20),
                    TextAlign = ContentAlignment.MiddleCenter,
                    BackColor = Color.White
                };

                // عداد
                lblCount = new Label
                {
                    Text = "عدد المستخدمين: 0",
                    Font = new Font("Tahoma", 12),
                    Size = new Size(200, 25),
                    Location = new Point(580, 60),
                    BackColor = Color.Yellow
                };

                // جدول بسيط
                dgvUsers = new DataGridView
                {
                    Location = new Point(25, 100),
                    Size = new Size(750, 350),
                    BackgroundColor = Color.White,
                    AllowUserToAddRows = false,
                    AllowUserToDeleteRows = false,
                    ReadOnly = true,
                    SelectionMode = DataGridViewSelectionMode.FullRowSelect
                };

                // زر تحديث
                btnRefresh = new Button
                {
                    Text = "تحديث",
                    Size = new Size(100, 40),
                    Location = new Point(25, 470),
                    BackColor = Color.Green,
                    ForeColor = Color.White,
                    Font = new Font("Tahoma", 12, FontStyle.Bold)
                };

                // زر إغلاق
                btnClose = new Button
                {
                    Text = "إغلاق",
                    Size = new Size(100, 40),
                    Location = new Point(675, 470),
                    BackColor = Color.Red,
                    ForeColor = Color.White,
                    Font = new Font("Tahoma", 12, FontStyle.Bold)
                };

                // ربط الأحداث
                btnRefresh.Click += (s, e) => LoadSimpleUsers();
                btnClose.Click += (s, e) => this.Close();

                // إضافة العناصر
                this.Controls.Add(lblTitle);
                this.Controls.Add(lblCount);
                this.Controls.Add(dgvUsers);
                this.Controls.Add(btnRefresh);
                this.Controls.Add(btnClose);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء المكونات البسيطة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء المكونات: {ex.Message}");
                MessageBox.Show($"خطأ في إنشاء المكونات: {ex.Message}");
            }
        }

        private void LoadSimpleUsers()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔧 بدء تحميل المستخدمين البسيط...");

                // إنشاء جدول بيانات بسيط
                DataTable dt = new DataTable();
                dt.Columns.Add("اسم المستخدم", typeof(string));
                dt.Columns.Add("الصلاحية", typeof(string));
                dt.Columns.Add("الحالة", typeof(string));

                // إضافة بيانات تجريبية
                dt.Rows.Add("admin", "Admin", "نشط");
                dt.Rows.Add("مدير", "Admin", "نشط");
                dt.Rows.Add("hr", "User", "نشط");
                dt.Rows.Add("موارد", "User", "نشط");
                dt.Rows.Add("user", "User", "نشط");

                // ربط البيانات
                dgvUsers.DataSource = dt;

                // تحديث العداد
                lblCount.Text = $"عدد المستخدمين: {dt.Rows.Count}";

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {dt.Rows.Count} مستخدم بسيط");

                // محاولة تحميل من قاعدة البيانات الحقيقية
                try
                {
                    var realData = DatabaseHelper.GetAllUsers();
                    if (realData != null && realData.Rows.Count > 0)
                    {
                        dgvUsers.DataSource = realData;
                        lblCount.Text = $"عدد المستخدمين: {realData.Rows.Count} (من قاعدة البيانات)";
                        System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {realData.Rows.Count} مستخدم من قاعدة البيانات");
                    }
                }
                catch (Exception dbEx)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ فشل تحميل من قاعدة البيانات: {dbEx.Message}");
                    lblCount.Text += " (بيانات تجريبية)";
                }

                // تحديث الواجهة
                dgvUsers.Refresh();
                this.Refresh();

            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل المستخدمين البسيط: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}");
            }
        }
    }
}
