@echo off
title Final HR Management System v5.3.0
color 0A

echo.
echo ========================================
echo    Final HR Management System v5.3.0
echo    نظام إدارة الموارد البشرية النهائي
echo    Clean Version - No External Dependencies
echo ========================================
echo.

echo Changing to clean directory...
cd /d "%~dp0\CleanHR"

echo Building Final HR Management System...
dotnet build CleanHR.csproj --verbosity quiet

if errorlevel 1 (
    echo ❌ Build failed. Check .NET installation.
    echo.
    echo Please ensure .NET 6.0 is installed:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    echo Alternative: Try running RUN_CLEAN_HR.bat in CleanHR folder
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.
echo Starting HR Management System...
echo.
echo 🎯 COMPLETE FEATURES:
echo • 👥 Employee Management - View all employees in detailed list
echo • ➕ Add Employees - Complete form with validation
echo • 🔍 Advanced Search - Search by name, ID, job, department
echo • 📊 Comprehensive Reports - Statistics and department breakdown
echo • ✏️ Edit Employees - Modify existing employee data
echo • 🗑️ Delete Employees - Safe deletion with confirmation
echo • 💾 Backup System - Create timestamped backups
echo • 📤 Export Data - Export to CSV for Excel
echo • 📖 Help System - Complete user documentation
echo • 🔄 Real-time Updates - Automatic data refresh
echo.
echo 📱 WHAT YOU'LL SEE:
echo 1. 📱 Welcome message with system info
echo 2. 🏠 Main window with 10 functional buttons
echo 3. 📋 Employee forms, lists, and reports
echo 4. 💾 Data automatically saved to hr_data.txt
echo.
echo 🚀 SYSTEM INFO:
echo • 5 sample employees included
echo • No database setup required
echo • No external libraries needed
echo • Works on Windows 7+
echo • Portable and self-contained
echo.

dotnet run --project CleanHR.csproj

echo.
echo Application finished.
cd ..
pause
