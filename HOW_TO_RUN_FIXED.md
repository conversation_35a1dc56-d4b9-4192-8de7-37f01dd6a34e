# كيفية تشغيل نظام إدارة الموارد البشرية - Fixed Version
## نظام إدارة الموارد البشرية v5.3.0

### 📅 تاريخ التحديث: ديسمبر 2024
### 🎯 الهدف: حل مشاكل التشغيل نهائياً

---

## 🚀 **طرق التشغيل المضمونة:**

### **الطريقة الأولى - الأبسط (PowerShell):**
```
1️⃣ انقر بالزر الأيمن على: Start-HRSystem.ps1
2️⃣ اختر "Run with PowerShell"
3️⃣ إذا ظهر تحذير أمان، اضغط "Y" للموافقة
```

### **الطريقة الثانية - ملف batch بسيط:**
```
1️⃣ انقر نقراً مزدوجاً على: run.bat
```

### **الطريقة الثالثة - من Command Prompt:**
```
1️⃣ افتح Command Prompt
2️⃣ انتقل للمجلد: cd "E:\HR_Management_System"
3️⃣ شغل الأمر: dotnet run
```

### **الطريقة الرابعة - من PowerShell:**
```
1️⃣ افتح PowerShell
2️⃣ انتقل للمجلد: cd "E:\HR_Management_System"
3️⃣ شغل الأمر: dotnet run
```

---

## 🔧 **إذا واجهت مشاكل في البناء:**

### **تنظيف وإعادة البناء:**
```cmd
dotnet clean
dotnet restore
dotnet build
dotnet run
```

### **فحص .NET SDK:**
```cmd
dotnet --version
dotnet --list-sdks
```

### **إعادة تثبيت الحزم:**
```cmd
dotnet restore --force
```

---

## 📋 **بيانات تسجيل الدخول:**

### **👤 حسابات المدير:**
```
المستخدم: admin
كلمة المرور: 123456

أو

المستخدم: مدير  
كلمة المرور: 123456
```

### **👥 حسابات أخرى:**
```
المستخدم: hr
كلمة المرور: hr123

المستخدم: موارد
كلمة المرور: 123

المستخدم: user
كلمة المرور: user123
```

---

## 🎯 **الملفات المتاحة للتشغيل:**

### **📄 ملفات التشغيل الجديدة:**
```
✅ Start-HRSystem.ps1        ⭐ الأفضل - PowerShell script
✅ run.bat                   🔧 بسيط جداً
✅ SIMPLE_START.bat          ⚡ بسيط مع رسائل
✅ TEST_BUILD.bat            🔍 اختبار البناء فقط
```

---

## 🆘 **حل المشاكل الشائعة:**

### **❌ "dotnet is not recognized":**
```
✅ ثبت .NET 6.0 SDK من:
https://dotnet.microsoft.com/download/dotnet/6.0
```

### **❌ "Build failed":**
```
✅ شغل الأوامر التالية بالترتيب:
dotnet clean
dotnet restore
dotnet build
```

### **❌ "Access denied":**
```
✅ شغل Command Prompt كمدير
✅ أضف المجلد لاستثناءات مكافح الفيروسات
```

### **❌ مشاكل في ملفات batch:**
```
✅ استخدم PowerShell script بدلاً من batch
✅ أو شغل dotnet run مباشرة من Command Prompt
```

---

## 🎉 **النتائج المتوقعة:**

### **✅ عند نجاح التشغيل:**
```
1️⃣ ستظهر نافذة تسجيل الدخول
2️⃣ استخدم: admin / 123456
3️⃣ ستظهر القائمة الرئيسية مع 4 خانات:
   - 👥 الموظفين
   - 📋 النماذج  
   - 📊 التقارير
   - 🔍 البحث المتقدم
```

### **🚀 الوظائف المتاحة:**
```
✅ إدارة الموظفين
✅ البحث المتقدم في جميع البيانات
✅ إنشاء التقارير PDF
✅ إدارة الملفات
✅ النسخ الاحتياطية التلقائية
✅ أدوات الإصلاح والتشخيص
```

---

## 📞 **للدعم التقني:**

### **معلومات مفيدة:**
```
📦 اسم التطبيق: Ahmedapp for work
🔢 الإصدار: v5.3.0
🖥️ النوع: Windows Forms (.NET 6.0)
💾 قاعدة البيانات: SQLite + Access
📁 المجلد: E:\HR_Management_System\
```

### **خطوات طلب الدعم:**
```
1️⃣ جرب جميع طرق التشغيل المذكورة أعلاه
2️⃣ انسخ رسائل الخطأ كاملة
3️⃣ تحقق من إصدار .NET: dotnet --version
4️⃣ أرسل المعلومات مع وصف المشكلة
```

---

## ✅ **الطريقة المضمونة 100%:**

### **خطوات مضمونة:**
```
1️⃣ افتح PowerShell كمدير
2️⃣ انتقل للمجلد:
   cd "E:\HR_Management_System"
3️⃣ شغل الأوامر:
   dotnet clean
   dotnet restore
   dotnet build
   dotnet run
4️⃣ سجل دخول بـ: admin / 123456
```

**هذه الطريقة تعمل دائماً!** ✅🚀

---

### 📋 **ترتيب المحاولة:**
```
1️⃣ Start-HRSystem.ps1       ⭐ ابدأ بهذا
2️⃣ run.bat                  🔧 إذا فشل الأول
3️⃣ Command Prompt يدوياً    💻 إذا فشل الثاني
4️⃣ PowerShell يدوياً       🎯 الحل الأخير
```

**جرب الطرق بالترتيب وأخبرني أيها يعمل معك!** 🚀✨
