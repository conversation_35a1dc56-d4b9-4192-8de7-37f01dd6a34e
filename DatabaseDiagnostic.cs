using System;
using System.Data.SqlClient;
using System.Windows.Forms;
using System.Configuration;

namespace Ahmedapp_for_work
{
    public static class DatabaseDiagnostic
    {
        public static void RunDiagnostic()
        {
            try
            {
                string connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString
                    ?? @"Data Source=(LocalDB)\MSSQLLocalDB;Initial Catalog=HRManagementDB;Integrated Security=True;Connect Timeout=30";

                string diagnosticReport = "تشخيص قاعدة البيانات - Database Diagnostic Report\n";
                diagnosticReport += new string('=', 50) + "\n\n";

                // Test 1: Connection Test
                diagnosticReport += "1. اختبار الاتصال - Connection Test:\n";
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        connection.Open();
                        diagnosticReport += "   ✅ نجح الاتصال بقاعدة البيانات\n";
                        diagnosticReport += $"   📊 إصدار SQL Server: {connection.ServerVersion}\n";
                        diagnosticReport += $"   🗄️ اسم قاعدة البيانات: {connection.Database}\n";
                    }
                }
                catch (Exception ex)
                {
                    diagnosticReport += $"   ❌ فشل الاتصال: {ex.Message}\n";
                }

                diagnosticReport += "\n";

                // Test 2: Check if database exists
                diagnosticReport += "2. فحص وجود قاعدة البيانات - Database Existence:\n";
                try
                {
                    string checkDbQuery = "SELECT COUNT(*) FROM sys.databases WHERE name = 'HRManagementDB'";
                    using (SqlConnection connection = new SqlConnection(@"Data Source=(LocalDB)\MSSQLLocalDB;Integrated Security=True;Connect Timeout=30"))
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(checkDbQuery, connection))
                        {
                            int dbCount = (int)command.ExecuteScalar();
                            if (dbCount > 0)
                            {
                                diagnosticReport += "   ✅ قاعدة البيانات HRManagementDB موجودة\n";
                            }
                            else
                            {
                                diagnosticReport += "   ⚠️ قاعدة البيانات HRManagementDB غير موجودة\n";
                                diagnosticReport += "   💡 سيتم إنشاؤها تلقائياً عند أول استخدام\n";
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    diagnosticReport += $"   ❌ خطأ في فحص قاعدة البيانات: {ex.Message}\n";
                }

                diagnosticReport += "\n";

                // Test 3: Check tables
                diagnosticReport += "3. فحص الجداول - Tables Check:\n";
                try
                {
                    using (SqlConnection connection = new SqlConnection(connectionString))
                    {
                        connection.Open();

                        string[] requiredTables = { "Employees", "EmployeeFiles", "EmployeeHistory", "Notifications", "Notes", "Tips" };

                        foreach (string tableName in requiredTables)
                        {
                            string checkTableQuery = $"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{tableName}'";
                            using (SqlCommand command = new SqlCommand(checkTableQuery, connection))
                            {
                                int tableCount = (int)command.ExecuteScalar();
                                if (tableCount > 0)
                                {
                                    diagnosticReport += $"   ✅ جدول {tableName} موجود\n";
                                }
                                else
                                {
                                    diagnosticReport += $"   ⚠️ جدول {tableName} غير موجود\n";
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    diagnosticReport += $"   ❌ خطأ في فحص الجداول: {ex.Message}\n";
                }

                diagnosticReport += "\n";

                // Test 4: LocalDB Status
                diagnosticReport += "4. حالة LocalDB - LocalDB Status:\n";
                try
                {
                    System.Diagnostics.Process process = new System.Diagnostics.Process();
                    process.StartInfo.FileName = "sqllocaldb";
                    process.StartInfo.Arguments = "info";
                    process.StartInfo.UseShellExecute = false;
                    process.StartInfo.RedirectStandardOutput = true;
                    process.StartInfo.CreateNoWindow = true;
                    process.Start();

                    string output = process.StandardOutput.ReadToEnd();
                    process.WaitForExit();

                    if (output.Contains("MSSQLLocalDB"))
                    {
                        diagnosticReport += "   ✅ SQL Server LocalDB مثبت ومتاح\n";
                    }
                    else
                    {
                        diagnosticReport += "   ❌ SQL Server LocalDB غير متاح\n";
                    }
                }
                catch (Exception ex)
                {
                    diagnosticReport += $"   ⚠️ لا يمكن فحص LocalDB: {ex.Message}\n";
                }

                diagnosticReport += "\n";

                // Test 5: Configuration
                diagnosticReport += "5. إعدادات التطبيق - Application Settings:\n";
                diagnosticReport += $"   📧 البريد الأساسي: {ConfigurationManager.AppSettings["DeveloperEmail"]}\n";
                diagnosticReport += $"   📧 البريد الثانوي: {ConfigurationManager.AppSettings["DeveloperEmailSecondary"]}\n";
                diagnosticReport += $"   🔢 الإصدار: {ConfigurationManager.AppSettings["AppVersion"]}\n";
                diagnosticReport += $"   🏢 الشركة: {ConfigurationManager.AppSettings["CompanyName"]}\n";

                diagnosticReport += "\n";

                // Recommendations
                diagnosticReport += "التوصيات - Recommendations:\n";
                diagnosticReport += new string('=', 30) + "\n";
                diagnosticReport += "1. إذا فشل الاتصال، تأكد من تثبيت SQL Server LocalDB\n";
                diagnosticReport += "2. إذا كانت الجداول غير موجودة، سيتم إنشاؤها تلقائياً\n";
                diagnosticReport += "3. تأكد من تشغيل التطبيق كمدير إذا واجهت مشاكل في الصلاحيات\n";
                diagnosticReport += "4. في حالة استمرار المشاكل، استخدم البيانات التجريبية\n";

                // Show diagnostic report
                Form diagnosticForm = new Form
                {
                    Text = "تشخيص قاعدة البيانات - Database Diagnostic",
                    Size = new System.Drawing.Size(800, 600),
                    StartPosition = FormStartPosition.CenterParent,
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    MaximizeBox = false,
                    MinimizeBox = false
                };

                TextBox reportTextBox = new TextBox
                {
                    Text = diagnosticReport,
                    Multiline = true,
                    ScrollBars = ScrollBars.Vertical,
                    ReadOnly = true,
                    Font = new System.Drawing.Font("Consolas", 10),
                    Dock = DockStyle.Fill,
                    BackColor = System.Drawing.Color.FromArgb(248, 249, 250)
                };

                Button closeButton = new Button
                {
                    Text = "إغلاق",
                    Size = new System.Drawing.Size(100, 30),
                    Location = new System.Drawing.Point(350, 520),
                    BackColor = System.Drawing.Color.FromArgb(52, 152, 219),
                    ForeColor = System.Drawing.Color.White,
                    FlatStyle = FlatStyle.Flat
                };
                closeButton.Click += (s, e) => diagnosticForm.Close();

                diagnosticForm.Controls.Add(reportTextBox);
                diagnosticForm.Controls.Add(closeButton);
                diagnosticForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشخيص قاعدة البيانات:\n\n{ex.Message}",
                              "خطأ في التشخيص", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static bool CreateDatabaseIfNotExists()
        {
            try
            {
                string masterConnectionString = @"Data Source=(LocalDB)\MSSQLLocalDB;Integrated Security=True;Connect Timeout=30";

                using (SqlConnection connection = new SqlConnection(masterConnectionString))
                {
                    connection.Open();

                    // Check if database exists
                    string checkDbQuery = "SELECT COUNT(*) FROM sys.databases WHERE name = 'HRManagementDB'";
                    using (SqlCommand command = new SqlCommand(checkDbQuery, connection))
                    {
                        int dbCount = (int)command.ExecuteScalar();
                        if (dbCount == 0)
                        {
                            // Create database
                            string createDbQuery = "CREATE DATABASE HRManagementDB";
                            using (SqlCommand createCommand = new SqlCommand(createDbQuery, connection))
                            {
                                createCommand.ExecuteNonQuery();
                            }
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"فشل في إنشاء قاعدة البيانات: {ex.Message}");
                return false;
            }
        }
    }
}
